about = "About"
add_category = "Add category"
add_content = "Add content"
add_link = "Add link"
add_menu = "Add menu"
add_new_page = "Add new page"
add_new_post = "Add new post"
add_source_link_optional = "Add source link (optional)"
add_sub = "Add sub page"
address_url = "Address (URL)"
admin = "Admin"
admin_panel_style_based_on = "Admin panel style based on"
all_blog_posts = "All blog posts"
all_cache_has_been_deleted = "All cache has been deleted !"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Archives"
are_you_sure_you_want_to_delete_ = "Are you sure you want to delete <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "At the moment you are using auto generated menu."
audio_post = "Audio post"
audio_post_comment = "Creating blog post with featured audio"
author = "Author"
author_description = "Just another HTMLy user"
back_to = "Back to"
backup = "Backup"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "In one paragraph, tell us more about your blog."
blog_theme = "Blog theme"
blog_title = "Blog title"
blog_title_placeholder = "My HTMLy Blog"
blog_posts_displayed_as = "Blog posts displayed as"
breadcrumb_home_text = "Breadcrumb home text"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "By using this importer you are agree if the feed is yours or at least you have the authority to publish it."
css_class_optional = "CSS Class (optional)"
cache_expiration = "Cache expiration (in hours)"
cache_off = "Cache off"
cache_timestamp = "Cache timestamp"
cancel = "Cancel"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "Captcha failed"
categories = "Categories"
category = "Category"
check_update = "Check for update"
clear_cache = "Clear cache"
comma_separated_values = "Comma separated values"
comment_system = "Comment system"
comments = "Comments"
config = "Config"
congrats_you_have_the_latest_version_of_htmly = "Congrats! You have the latest version of HTMLy."
content = "Content"
contents = "Contents"
copyright_line = "Copyright line"
copyright_line_placeholder = "(c) Your name."
create_backup = "Create backup"
created = "Created"
custom = "Custom"
custom_settings = "Custom Settings"
dashboard = "Dashboard"
date = "Date"
date_format = "Date Format"
delete = "Delete"
description = "Description"
disable = "Disable"
disabled = "Disabled"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "Draft"
edit = "Edit"
edit_category = "Edit category"
edit_post = "Edit"
edit_profile = "Edit profile"
enable = "Enable"
enable_blog_url = "Enable blog URL"
enter_image_url = "Enter image URL"
facebook_app_id = "Facebook App ID"
facebook_page = "Facebook page"
featured_audio = "Featured Audio"
featured_image = "Featured Image"
featured_link = "Featured Link"
featured_quote = "Featured Quote"
featured_video = "Featured Video"
feed_url = "Feed URL"
filename = "Filename"
follow = "Follow"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Front page displays"
full_post = "Full post"
general = "General"
general_settings = "General Settings"
get_one_here = "Obtain your reCaptcha keys here: "
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Home"
if_left_empty_we_will_excerpt_it_from_the_content_below = "If leave empty we will excerpt it from the content below"
if_the_url_is_left_empty_we_will_use_the_page_title = "If the url leave empty we will use the page title"
if_the_url_is_left_empty_we_will_use_the_post_title = "If the url leave empty we will use the post title"
image_post = "Image post"
image_post_comment = "Creating blog post with featured image"
import = "Import"
import_feed = "Start Import Feed"
import_rss = "Import RSS"
import_rss_feed_2.0 = "Import RSS Feed 2.0"
insert_image = "Insert Image"
invalid_error = "ERROR: Invalid username or password"
language = "System Language"
link_name = "Link name"
link_post = "Link post"
link_post_comment = "Creating blog post with featured link"
login = "Login"
login_page = "Login page"
logout = "Logout"
menu = "Menu"
menus = "Menu Editor"
meta_description = "Meta description"
meta_description_character = "Meta description character"
metatags = "Metatags"
metatags_settings = "Metatags Settings"
mine = "Mine"
more = "More"
my_draft = "My draft"
my_posts = "My posts"
name = "Name"
newer = "Newer"
next = "Next"
next_post = "Next post"
no_available_backup = "No available backup at this time."
no_draft_found = "No draft found"
no_posts_found = "No posts found"
no_related_post_found = "No related post found"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Nope"
not = "No"
older = "Older"
only = "Only"
operations = "Operations"
page = "Page"
page_generation_time = "Page generation time"
pages = "Pages"
pass_error = "Password field is required"
password = "Password"
performance = "Performance"
performance_settings = "Performance Settings"
permalink = "Permalink"
popular = "Popular"
popular_posts = "Popular posts"
popular_posts_widget = "Popular posts widget"
popular_posts_widget_at_most = "Popular posts widget at most"
popular_tags = "Popular tags"
post_by_author = "Posts by this author"
posted_in = "Posted in"
posted_on = "Posted on"
posts = "Posts"
posts_by = "Post by"
posts_draft = "Posts draft"
posts_in_archive_page_at_most = "Posts in archive page at most"
posts_in_category_page_at_most = "Posts in category page at most"
posts_in_front_page_show_at_most = "Posts in front page show at most"
posts_in_profile_page_at_most = "Posts in profile page at most"
posts_in_search_result_at_most = "Posts in search result at most"
posts_in_tag_page_at_most = "Posts in tag page at most"
posts_in_type_page_at_most = "Posts in type page at most"
posts_index_settings = "Posts index settings"
posts_list = "Posts list"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pre-release"
prev = "Previous"
prev_post = "Previous Post"
preview = "Preview"
profile_for = "Profile for"
proudly_powered_by = "Proudly powered by"
publish = "Publish"
publish_draft = "Publish draft"
published = "Published"
quote_post = "Quote post"
quote_post_comment = "Creating blog post with featured quote"
rss_character = "RSS character"
rss_feeds_show_the_most_recent = "RSS feeds show the most recent"
rss_settings = "RSS settings"
read_more_text = "Read more text"
read_more_text_placeholder = "Read more"
reading = "Reading"
reading_settings = "Reading Settings"
recaptcha = "Login Protection"
recent_posts = "Recent posts"
recent_posts_widget_at_most = "Recent posts widget at most"
regular_post = "Regular post"
regular_post_comment = "Creating regular blog post"
related_posts = "Related posts"
related_widget_posts_at_most = "Related widget posts at most"
revert_to_draft = "Revert to draft"
save = "Save"
save_config = "Save config"
save_edit = "Save Edit"
save_menu = "Save menu"
save_as_draft = "Save as draft"
save_category = "Save category"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Search"
search_for = "Search for"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Secret Key"
settings = "Settings"
sign_in_to_start_your_session = "Sign in to start your session"
site_key = "Site Key"
sitemap = "Sitemap"
slug = "Slug"
social_media = "Social Media"
static_page = "Static page"
static_page_comment = "Creating static page"
static_pages = "Static pages"
summary = "Summary"
summary_character = "Summary character"
tag = "Tag"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Tagline"
tagline_placeholder = "Databaseless PHP Blogging Platform"
tagline_description = "In a few words, explain what this blog is about."
tags = "Tags"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "This is legacy code. Usually new created analytics using gtag.js"
this_page_doesnt_exist = "This page doesn't exist !"
time = "Time"
timezone = "Timezone"
title = "Title"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "To use Disqus or Facebook comments you need to provide your Disqus shortname or your Facebook App ID."
token_error = "CSRF Token not correct"
tools = "Tools"
twitter_account = "Twitter account"
type_to_search = "Type to search"
uncategorized = "Uncategorized"
uncategorized_comment = "Topics that don't need a category, or don't fit into any other existing category"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Update"
update_available = "Update Available"
update_draft = "Update draft"
update_post = "Update post"
update_to = "Update to"
upload = "Upload"
user = "User"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Valid values range from 0.0 to 1.0. See"
video_post = "Video post"
video_post_comment = "Creating blog post with featured video"
view = "View"
view_post = "View"
views = "Views"
widget = "Widget"
widget_settings = "Widget Settings"
would_you_like_to_try_our = "Would you like to try our "
yes_im_in = "Yes I'm in"
yes_not_recommended = "Yes (not recommended)"
you_dont_have_permission_to_access_this_page = "You don't have permission to access this page"
your_new_config_key = "Your New Config Key"
your_new_value = "Your New Value"
your_backups = "Your backups"
your_latest_blog_posts = "Your latest blog posts"
your_recent_posts = "Your recent posts"
by = "by"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>hint:</u> Use <code>Ctrl</code>/<code>CMD</code> + <code>F</code> to search for your config key or value."
homepage = "homepage"
instead = "instead"
item_class = "Insert CSS class"
item_slug = "Insert Link URL"
now = "now"
of = "of"
optional = "optional"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>pro tips:</u> You can create custom config keys and print out your config key value anywhere in your template."
read_more = "read more"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
