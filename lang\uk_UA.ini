about = "Про нас"
add_category = "Додати категорію"
add_content = "Додати контент"
add_link = "Додати посилання"
add_menu = "Додати меню"
add_new_page = "Додати нову сторінку"
add_new_post = "Додати новий запис"
add_source_link_optional = "Додати джерело (необов'язково)"
add_sub = "Додати підсторінку"
address_url = "Адреса (URL)"
admin = "Адмін"
admin_panel_style_based_on = "Стиль адмін-панелі на основі"
all_blog_posts = "Всі записи блогу"
all_cache_has_been_deleted = "Весь кеш було видалено!"
all_posts_tagged = "Всі записи з тегом"
archive_for = "Архів для"
archive_page_for = "Сторінка архіву для"
archives = "Архіви"
are_you_sure_you_want_to_delete_ = "Ви впевнені, що хочете видалити <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Зараз ви використовуєте автоматично згенероване меню."
audio_post = "Аудіо запис"
audio_post_comment = "Створення блогу з аудіо"
author = "Автор"
author_description = "Ще один користувач HTMLy"
back_to = "Назад до"
backup = "Резервне копіювання"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "В одному абзаці розкажіть більше про ваш блог."
blog_theme = "Тема блогу"
blog_title = "Назва блогу"
blog_title_placeholder = "Мій HTMLy блог"
blog_posts_displayed_as = "Відображення записів блогу як"
breadcrumb_home_text = "Текст головної крихти"
by_using_this_importer_you_are_agree_if_the_feed_is_yours_or_at_least_you_have_the_authority_to_publish_it = "Використовуючи цей імпортер, ви погоджуєтесь, що фід ваш або ви маєте право його публікувати."
css_class_optional = "CSS клас (необов'язково)"
cache_expiration = "Час життя кешу (в годинах)"
cache_off = "Кеш вимкнено"
cache_timestamp = "Часова мітка кешу"
cancel = "Скасувати"
cannot_read_feed_content = "Неможливо прочитати вміст фіду"
captcha_error = "Помилка reCAPTCHA"
categories = "Категорії"
category = "Категорія"
check_update = "Перевірити оновлення"
clear_cache = "Очистити кеш"
comma_separated_values = "Значення через кому"
comment_system = "Система коментування"
comments = "Коментарі"
config = "Конфігурація"
congrats_you_have_the_latest_version_of_htmly = "Вітаємо! У вас остання версія HTMLy."
content = "Контент"
contents = "Контенти"
copyright_line = "Рядок копірайту"
copyright_line_placeholder = "(c) Ваше ім'я."
create_backup = "Створити резервну копію"
created = "Створено"
custom = "Користувацький"
custom_settings = "Користувацькі налаштування"
dashboard = "Панель управління"
date = "Дата"
date_format = "Формат дати"
delete = "Видалити"
description = "Опис"
disable = "Вимкнути"
disabled = "Вимкнено"
disqus_shortname = "Коротка назва Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Чернетка"
edit = "Редагувати"
edit_category = "Редагувати категорію"
edit_post = "Редагувати"
edit_profile = "Редагувати профіль"
enable = "Увімкнути"
enable_blog_url = "Увімкнути /blog URL"
enter_image_url = "Введіть URL зображення"
facebook_app_id = "ID додатка Facebook"
facebook_page = "Сторінка Facebook"
featured_audio = "Рекомендоване аудіо"
featured_image = "Рекомендоване зображення"
featured_link = "Рекомендоване посилання"
featured_quote = "Рекомендована цитата"
featured_video = "Рекомендоване відео"
feed_url = "URL фіду"
filename = "Назва файлу"
follow = "Слідкувати"
for_google_site_verification_meta = "Для google-site-verification мета"
for_msvalidate_01_meta = "Для msvalidate.01 мета"
front_page_displays = "Головна сторінка відображає"
full_post = "Повний запис"
general = "Загальні"
general_settings = "Загальні налаштування"
get_one_here = "Отримайте один тут"
github_pre_release = "Попередня версія Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (спадщина)"
google_search_console = "Google Search Console"
home = "Головна"
if_leave_empty_we_will_excerpt_it_from_the_content_below = "Якщо залишити порожнім, ми витягнемо це з контенту нижче"
if_the_url_leave_empty_we_will_use_the_page_title = "Якщо URL залишити порожнім, ми використаємо заголовок сторінки"
if_the_url_leave_empty_we_will_use_the_post_title = "Якщо URL залишити порожнім, ми використаємо заголовок запису"
image_post = "Зображення запису"
image_post_comment = "Створення блогу з зображенням"
import = "Імпорт"
import_feed = "Почати імпорт фіду"
import_rss = "Імпорт RSS"
import_rss_feed_2.0 = "Імпорт RSS фіду 2.0"
insert_image = "Вставити зображення"
invalid_error = "ПОМИЛКА: Невірне ім'я користувача або пароль"
language = "Мова системи"
link_name = "Назва посилання"
link_post = "Посилання на запис"
link_post_comment = "Створення блогу з посиланням"
login = "Увійти"
login_page = "Сторінка входу"
logout = "Вийти"
menu = "Меню"
menus = "Редактор меню"
meta_description = "Мета опис"
meta_description_character = "Символи мета опису"
metatags = "Мета-теги"
metatags_settings = "Налаштування мета-тегів"
mine = "Мої"
more = "Більше"
my_draft = "Моя чернетка"
my_posts = "Мої записи"
name = "Ім'я"
newer = "Новіші"
next = "Наступні"
next_post = "Наступний запис"
no_available_backup = "Немає доступної резервної копії на цей час."
no_draft_found = "Чернеток не знайдено"
no_posts_found = "Записів не знайдено"
no_related_post_found = "Пов'язаних записів не знайдено"
no_scheduled_posts_found = "Запланованих записів не знайдено!"
no_search_results = "Результатів пошуку не знайдено"
nope = "Ні"
not = "Ні"
older = "Старіші"
only = "Тільки"
operations = "Операції"
page = "Сторінка"
page_generation_time = "Час генерації сторінки"
pages = "Сторінки"
pass_error = "Поле пароля є обов'язковим"
password = "Пароль"
performance = "Продуктивність"
performance_settings = "Налаштування продуктивності"
permalink = "Постійне посилання"
popular = "Популярні"
popular_posts = "Популярні записи"
popular_posts_widget = "Віджет популярних записів"
popular_posts_widget_at_most = "Віджет популярних записів, не більше ніж"
popular_tags = "Популярні теги"
post_by_author = "Записи цього автора"
posted_in = "Опубліковано в"
posted_on = "Опубліковано"
posts = "Записи"
posts_by = "Запис від"
posts_draft = "Чернетки записів"
posts_in_archive_page_at_most = "Записів на сторінці архіву, не більше ніж"
posts_in_category_page_at_most = "Записів на сторінці категорії, не більше ніж"
posts_in_front_page_show_at_most = "Записів на головній сторінці, не більше ніж"
posts_in_profile_page_at_most = "Записів на сторінці профілю, не більше ніж"
posts_in_search_result_at_most = "Записів у результатах пошуку, не більше ніж"
posts_in_tag_page_at_most = "Записів на сторінці тегу, не більше ніж"
posts_in_type_page_at_most = "Записів на сторінці типу, не більше ніж"
posts_index_settings = "Налаштування індексації записів"
posts_list = "Список записів"
posts_tagged = "Записи з тегом"
posts_with_type = "Записи з типом"
pre_release = "Попередній випуск"
prev = "Попередні"
prev_post = "Попередній запис"
preview = "Попередній перегляд"
profile_for = "Профіль для"
proudly_powered_by = "Працює на"
publish = "Опублікувати"
publish_draft = "Опублікувати чернетку"
published = "Опубліковано"
quote_post = "Цитата запису"
quote_post_comment = "Створення блогу з цитатою"
rss_character = "Символи RSS"
rss_feeds_show_the_most_recent = "RSS фіди показують найновіше"
rss_settings = "Налаштування RSS"
read_more_text = "Текст 'Читати більше'"
read_more_text_placeholder = "Читати більше"
reading = "Читання"
reading_settings = "Налаштування читання"
recaptcha = "reCAPTCHA"
recent_posts = "Останні записи"
recent_posts_widget_at_most = "Віджет останніх записів, не більше ніж"
regular_post = "Звичайний запис"
regular_post_comment = "Створення звичайного запису блогу"
related_posts = "Пов'язані записи"
related_widget_posts_at_most = "Віджет пов'язаних записів, не більше ніж"
revert_to_draft = "Повернути до чернетки"
save = "Зберегти"
save_config = "Зберегти конфігурацію"
save_edit = "Зберегти редагування"
save_menu = "Зберегти меню"
save_as_draft = "Зберегти як чернетку"
save_category = "Зберегти категорію"
scheduled = "Заплановано"
scheduled_posts = "Заплановані записи"
scheduled_tips = "Публікація запису з майбутньою датою або часом, він буде відправлений до запланованих записів"
search = "Пошук"
search_for = "Шукати"
search_results_for = "Результати пошуку для"
search_results_not_found = "Результатів пошуку не знайдено!"
secret_key = "Секретний ключ"
settings = "Налаштування"
sign_in_to_start_your_session = "Увійдіть, щоб розпочати сеанс"
site_key = "Ключ сайту"
sitemap = "Карта сайту"
slug = "Slug"
social_media = "Соціальні мережі"
static_page = "Статична сторінка"
static_page_comment = "Створення статичної сторінки"
static_pages = "Статичні сторінки"
summary = "Резюме"
summary_character = "Символи резюме"
tag = "Тег"
tagcloud_widget_at_most = "Хмара тегів, не більше ніж"
tagline = "Слоган"
tagline_placeholder = "Без бази даних PHP платформа для блогів"
tagline_description = "У кількох словах поясніть, про що цей блог."
tags = "Теги"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Це спадковий код. Зазвичай нові аналітики створюються за допомогою gtag.js"
this_page_doesnt_exist = "Ця сторінка не існує!"
time = "Час"
timezone = "Часовий пояс"
title = "Заголовок"
to_using_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Для використання коментарів Disqus або Facebook ви повинні надати коротке ім'я Disqus або ID додатка Facebook."
token_error = "Невірний CSRF токен"
tools = "Інструменти"
twitter_account = "Акаунт у Twitter"
type_to_search = "Введіть для пошуку"
uncategorized = "Без категорії"
uncategorized_comment = "Тематики, які не потребують категорії або не підходять до жодної з існуючих категорій"
universal_analytics = "Універсальна аналітика (gtag.js)"
unknown_feed_format = "Невідомий формат фіду"
update = "Оновити"
update_available = "Доступне оновлення"
update_draft = "Оновити чернетку"
update_post = "Оновити запис"
update_to = "Оновити до"
upload = "Завантажити"
user = "Користувач"
user_error = "Поле користувача є обов'язковим"
valid_values_range_from_0_to_1.0._see = "Дійсні значення в діапазоні від 0.0 до 1.0. Див."
video_post = "Відео запис"
video_post_comment = "Створення блогу з відео"
view = "Перегляд"
view_post = "Перегляд"
views = "Перегляди"
widget = "Віджет"
widget_settings = "Налаштування віджетів"
would_you_like_to_try_our = "Бажаєте спробувати наш "
yes_im_in = "Так, я згоден"
yes_not_recommended = "Так (не рекомендується)"
you_dont_have_permission_to_access_this_page = "У вас немає дозволу на доступ до цієї сторінки"
your_new_config_key = "Ваш новий конфігураційний ключ"
your_new_value = "Ваше нове значення"
your_backups = "Ваші резервні копії"
your_latest_blog_posts = "Ваші останні записи блогу"
your_recent_posts = "Ваші останні записи"
by = "від"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>підказка:</u> Використовуйте <code>Ctrl</code>/<code>CMD</code> + <code>F</code> для пошуку вашого конфігураційного ключа або значення."
homepage = "Головна сторінка"
instead = "замість"
item_class = "Вставити CSS клас"
item_slug = "Вставити URL посилання"
now = "зараз"
of = "з"
optional = "необов'язково"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_creating_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>професійна підказка:</u> Ви можете створити власні конфігураційні ключі та виводити їх значення будь-де у вашому шаблоні."
read_more = "читати далі"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "ваш ключ"
summary_behavior = "Поведінка резюме"
default = "За замовчуванням"
check_shortcode = "Перевірити шорткод"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "У режимі резюме, чи перевіряти спочатку шорткод перед обрізанням контенту до x символів"
manage_users = "Керування користувачами"
add_user = "Додати користувача"
username = "Ім'я користувача"
role = "Роль"
change_password = "Змінити пароль"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
