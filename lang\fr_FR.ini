about = "À propos"
add_category = "Ajouter une catégorie"
add_content = "Ajouter du contenu"
add_link = "Ajouter le lien"
add_menu = "Ajouter un menu"
add_new_page = "Ajouter une page"
add_new_post = "Ajouter un billet"
add_source_link_optional = "Ajouter le lien source (optionnel)"
add_sub = "Ajouter une sous-page"
address_url = "Adresse (URL)"
admin = "Administrateur"
admin_panel_style_based_on = "Style du panneau d'administration basé sur"
all_blog_posts = "Tous les billets de blog"
all_cache_has_been_deleted = "Le cache a été vidé !"
all_posts_tagged = "Tous les billets taggués"
archive_for = "Archive pour"
archive_page_for = "Page d'archive pour"
archives = "Archives"
are_you_sure_you_want_to_delete_ = "Êtes-vous sûr de vouloir supprimer <strong>%s</strong> ?"
at_the_moment_you_are_using_auto_generated_menu = "Pour le moment, vous utilisez le menu généré automatiquement."
audio_post = "Billet audio"
audio_post_comment = "Créer un billet de blog avec un fichier audio"
author = "Auteur"
author_description = "Un autre utilisateur de HTMLy"
back_to = "Retourner vers"
backup = "Sauvegarde"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "En un paragraphe, dites-nous en plus sur votre blog."
blog_theme = "Thème graphique du blog"
blog_title = "Titre du blog"
blog_title_placeholder = "Mon blog HTMLy"
blog_posts_displayed_as = "Billets affichés comme"
breadcrumb_home_text = "Texte d'accueil du fil d'Ariane"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "En utilisant cet importateur, vous devez vous assurer qu'il s'agit de votre flux RSS ou que vous ayez l'autorisation de publier son contenu."
css_class_optional = "Classe CSS (optionnel)"
cache_expiration = "Durée avant l'expiration du cache (en heures)"
cache_off = "Désactiver le cache"
cache_timestamp = "Horodatage du cache"
cancel = "Annuler"
cannot_read_feed_content = "Impossible de lire le contenu du flux"
captcha_error = "reCaptcha incorrect"
categories = "Catégories"
category = "Catégorie"
check_update = "Rechercher des mises à jour"
clear_cache = "Nettoyer le cache"
comma_separated_values = "Valeurs séparées par des virgules"
comment_system = "Système de commentaires"
comments = "Commentaires"
config = "Configuration"
congrats_you_have_the_latest_version_of_htmly = "Félicitations ! Vous disposez de la dernière version de HTMLy."
content = "Contenu"
contents = "Contenus"
copyright_line = "Ligne de copyright"
copyright_line_placeholder = "(c) Votre nom."
create_backup = "Créer une sauvegarde"
created = "Créé"
custom = "Personnalisé"
custom_settings = "Paramètres personnalisés"
dashboard = "Tableau de bord"
date = "Date"
date_format = "Format des dates"
delete = "Supprimer"
description = "Description"
disable = "Désactiver"
disabled = "Désactivé"
disqus_shortname = "Nom abrégé (shortname) Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Brouillon"
edit = "Éditer"
edit_category = "Éditer la catégorie"
edit_post = "Éditer"
edit_profile = "Éditer le profil"
enable = "Activer"
enable_blog_url = "Activer l'URL blog"
enter_image_url = "Entrer l'URL de l'image"
facebook_app_id = "ID d'application Facebook"
facebook_page = "Page Facebook"
featured_audio = "Audio en vedette"
featured_image = "Image en vedette"
featured_link = "Lien en vedette"
featured_quote = "Citation en vedette"
featured_video = "Vidéo en vedette"
feed_url = "Adresse du flux"
filename = "Nom du fichier"
follow = "Suivre"
for_google_site_verification_meta = "Pour le méta-tag google-site-verification"
for_msvalidate_01_meta = "Pour le méta-tag msvalidate.01"
front_page_displays = "Affichage en première page"
full_post = "Billet entier"
general = "Général"
general_settings = "Paramètres généraux"
get_one_here = "Obtenez vos clés reCaptcha ici :"
github_pre_release = "Pré-release Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Accueil"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Si vous laissez vide, nous l'extrairons du contenu ci-dessous"
if_the_url_is_left_empty_we_will_use_the_page_title = "Si l'URL est laissée vide, nous utiliserons le titre de la page"
if_the_url_is_left_empty_we_will_use_the_post_title = "Si l'URL est laissée vide, nous utiliserons le titre du billet"
image_post = "Billet avec une image"
image_post_comment = "Créer un billet de blog avec une image"
import = "Importer"
import_feed = "Démarrer l'importation du flux"
import_rss = "Importer un flux RSS"
import_rss_feed_2.0 = "Importer le flux RSS 2.0"
insert_image = "Insérer l'image"
invalid_error = "Erreur : nom d'utilisateur ou mot de passe incorrect"
language = "Langue du système"
link_name = "Nom du lien"
link_post = "Billet avec un lien"
link_post_comment = "Créer un billet de blog avec un lien"
login = "Connexion"
login_page = "Page de connexion"
logout = "Déconnexion"
menu = "Menu"
menus = "Éditeur de menu"
meta_description = "Meta-description"
meta_description_character = "Nombre de caractères des méta-description"
metatags = "Méta-tags"
metatags_settings = "Paramètres des méta-tags"
mine = "Mes billets"
more = "Plus"
my_draft = "Mes brouillons"
my_posts = "Mes billets"
name = "Nom"
newer = "Plus récent"
next = "Suivant"
next_post = "Billet suivant"
no_available_backup = "Aucune sauvegarde disponible."
no_draft_found = "Aucun brouillon trouvé"
no_posts_found = "Aucun billet trouvé"
no_related_post_found = "Aucun billet en rapport trouvé"
no_scheduled_posts_found = "Aucun billet programmé trouvé !"
no_search_results = "Aucun résultat de recherche"
nope = "Non !"
not = "Non"
older = "Plus ancien"
only = "Seulement"
operations = "Opérations"
page = "Page"
page_generation_time = "Temps de génération de la page"
pages = "Pages"
pass_error = "Le champ Mot de passe est requis"
password = "Mot de passe"
performance = "Performance"
performance_settings = "Paramètres des performances"
permalink = "Lien permanent"
popular = "Populaire"
popular_posts = "Billets populaires"
popular_posts_widget = "Widget des billets populaires"
popular_posts_widget_at_most = "Nombre de billets maximum affichés dans le widget des billets populaires"
popular_tags = "Tags populaires"
post_by_author = "Billets de cet auteur"
posted_in = "Posté en"
posted_on = "Posté le"
posts = "Billets"
posts_by = "Billets par"
posts_draft = "Billets à l'état brouillon"
posts_in_archive_page_at_most = "Nombre de billets maximum dans la page des archives"
posts_in_category_page_at_most = "Nombre de billets maximum dans la page des catégories"
posts_in_front_page_show_at_most = "Nombre de billets maximum en première page"
posts_in_profile_page_at_most = "Nombre de billets maximum dans la page des profils"
posts_in_search_result_at_most = "Nombre de billets maximum dans les résultats de recherche"
posts_in_tag_page_at_most = "Nombre de billets maximum dans la page des tags"
posts_in_type_page_at_most = "Nombre de billets maximum dans la page des types"
posts_index_settings = "Paramètres d'indexation des articles"
posts_list = "Liste des billets"
posts_tagged = "Billets taggués"
posts_with_type = "Billets avec type"
pre_release = "Pré-release"
prev = "Ancien"
prev_post = "Billet précédent"
preview = "Pré-visualisation"
profile_for = "Profil de"
proudly_powered_by = "Fièrement propulsé par"
publish = "Publier"
publish_draft = "Publier le brouillon"
published = "Publié"
quote_post = "Billet avec une citation"
quote_post_comment = "Créer un billet avec un citation"
rss_character = "Caractères maximum des contenus du flux RSS"
rss_feeds_show_the_most_recent = "Nombre de billets maximum les plus récents du flux RSS"
rss_settings = "Paramètres du flux RSS"
read_more_text = "Texte Continuer à lire"
read_more_text_placeholder = "Continuer à lire"
reading = "Lecture"
reading_settings = "Paramètres de lecture"
recaptcha = "reCAPTCHA"
recent_posts = "Billets récents"
recent_posts_widget_at_most = "Nombre de billets maximum affichés dans le widget des billets récents"
regular_post = "Billet simple"
regular_post_comment = "Créer un billet de blog simple"
related_posts = "Billets en rapport"
related_widget_posts_at_most = "Nombre de billets maximum affichés dans le widget des billets similaires"
revert_to_draft = "Retourner en brouillon"
save = "Enregistrer"
save_config = "Sauvegarder la configuration"
save_edit = "Sauvegarder la modification"
save_menu = "Sauvegarder le menu"
save_as_draft = "Enregistrer en brouillon"
save_category = "Catégorie de la sauvegarde"
scheduled = "Programmés"
scheduled_posts = "Billets programmés"
scheduled_tips = "La publication d'un billet avec une date ou une heure future sera intégrée dans les billets programmés."
search = "Recherche"
search_for = "Rechercher pour"
search_results_for = "Résultats de recherche pour"
search_results_not_found = "Résultats de recherche non trouvés !"
secret_key = "Clé secrète"
settings = "Paramètres"
sign_in_to_start_your_session = "Connectez-vous pour démarrer votre session"
site_key = "Clé de site"
sitemap = "Plan du site"
slug = "Jeton"
social_media = "Réseaux sociaux"
static_page = "Page statique"
static_page_comment = "Créer une page statique"
static_pages = "Pages statiques"
summary = "Résumé"
summary_character = "Nombre de caractères du résumé"
tag = "Tag"
tagcloud_widget_at_most = "TagCloud au maximum"
tagline = "Slogan"
tagline_placeholder = "Plateforme de blog PHP sans base de données"
tagline_description = "En quelques mots, expliquez de quoi parle ce blog."
tags = "Tags"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Il s'agit d'un code hérité (legacy). Généralement, gtag.js est utilisé"
this_page_doesnt_exist = "Cette page n'existe pas !"
time = "Temps"
timezone = "Fuseau horaire"
title = "Titre"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Pour utiliser Disqus ou les commentaires Facebook, vous devez fournir le nom abrégé (shortname) Disqus ou l'ID d'application Facebook."
token_error = "Jeton CSRF incorrect"
tools = "Outils"
twitter_account = "Compte Twitter"
type_to_search = "Tapez pour rechercher"
uncategorized = "Sans catégorie"
uncategorized_comment = "Sujets qui n'ont pas besoin de catégorie, ou qui ne conviennent à aucune catégorie existante"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Format de flux inconnu"
update = "Mise à jour"
update_available = "Mise à jour disponible"
update_draft = "Mettre à jour le brouillon"
update_post = "Mettre à jour le billet"
update_to = "Mettre à jour vers"
upload = "Envoyer"
user = "Utilisateur"
user_error = "Le champ Utilisateur est requis"
valid_values_range_from_0_to_1.0._see = "Les valeurs valides vont de 0,0 à 1,0. Voir"
video_post = "Billet vidéo"
video_post_comment = "Créer un billet de blog avec une vidéo"
view = "Voir"
view_post = "Consulter"
views = "Vues"
widget = "Widget"
widget_settings = "Paramètres des widgets"
would_you_like_to_try_our = "Souhaitez-vous essayer notre "
yes_im_in = "Oui, je suis prêt"
yes_not_recommended = "Oui (déconseillé)"
you_dont_have_permission_to_access_this_page = "Vous n'êtes pas autorisé à accéder à cette page"
your_new_config_key = "Votre nouvelle clé de configuration"
your_new_value = "Votre nouvelle valeur"
your_backups = "Vos sauvegardes"
your_latest_blog_posts = "Vos derniers billets"
your_recent_posts = "Vos billets récents"
by = "par"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>astuce :</u> Utilisez <code>Ctrl</code>/<code>CMD</code> + <code>F</code> pour rechercher votre clé ou valeur de configuration."
homepage = "page d'accueil"
instead = "à la place"
item_class = "Insérer la classe CSS"
item_slug = "Insérer le jeton"
now = "maintenant"
of = "de"
optional = "optionnel"
post_your_post_slug = "/billet/jeton-du-billet"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>astuce de pro :</u> Vous pouvez créer une clé de configuration personnalisée et afficher la valeur de votre clé de configuration n'importe où dans votre modèle."
read_more = "lire plus"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/annee/mois/jeton-du-billet"
your_key = "votre.cle"
summary_behavior = "Comportement du résumé"
default = "Défaut"
check_shortcode = "Vérifier le shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "En mode résumé, il est possible de vérifier le shortcode avant de réduire le contenu à x caractères."
manage_users = "Gérer les utilisateurs"
add_user = "Ajouter un utilisateur"
username = "Nom d'utilisateur"
role = "Rôle"
change_password = "Changer le mot de passe"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
