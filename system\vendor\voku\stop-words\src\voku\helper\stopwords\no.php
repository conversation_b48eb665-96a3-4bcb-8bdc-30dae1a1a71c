<?php

// norwegian

static $data = [
    'og',
    'i',
    'jeg',
    'det',
    'at',
    'en',
    'et',
    'den',
    'til',
    'er',
    'som',
    'på',
    'de',
    'med',
    'han',
    'av',
    'ikke',
    'ikkje',
    'der',
    'så',
    'var',
    'meg',
    'seg',
    'men',
    'ett',
    'har',
    'om',
    'vi',
    'min',
    'mitt',
    'ha',
    'hadde',
    'hun',
    'nå',
    'over',
    'da',
    'ved',
    'fra',
    'du',
    'ut',
    'sin',
    'dem',
    'oss',
    'opp',
    'man',
    'kan',
    'hans',
    'hvor',
    'eller',
    'hva',
    'skal',
    'selv',
    'sjøl',
    'her',
    'alle',
    'vil',
    'bli',
    'ble',
    'blei',
    'blitt',
    'kunne',
    'inn',
    'når',
    'være',
    'kom',
    'noen',
    'noe',
    'ville',
    'dere',
    'som',
    'deres',
    'kun',
    'ja',
    'etter',
    'ned',
    'skulle',
    'denne',
    'for',
    'deg',
    'si',
    'sine',
    'sitt',
    'mot',
    'å',
    'meget',
    'hvorfor',
    'dette',
    'disse',
    'uten',
    'hvordan',
    'ingen',
    'din',
    'ditt',
    'blir',
    'samme',
    'hvilken',
    'hvilke',
    'sånn',
    'inni',
    'mellom',
    'vår',
    'hver',
    'hvem',
    'vors',
    'hvis',
    'både',
    'bare',
    'enn',
    'fordi',
    'før',
    'mange',
    'også',
    'slik',
    'vært',
    'være',
    'båe',
    'begge',
    'siden',
    'dykk',
    'dykkar',
    'dei',
    'deira',
    'deires',
    'deim',
    'di',
    'då',
    'eg',
    'ein',
    'eit',
    'eitt',
    'elles',
    'honom',
    'hjå',
    'ho',
    'hoe',
    'henne',
    'hennar',
    'hennes',
    'hoss',
    'hossen',
    'ikkje',
    'ingi',
    'inkje',
    'korleis',
    'korso',
    'kva',
    'kvar',
    'kvarhelst',
    'kven',
    'kvi',
    'kvifor',
    'me',
    'medan',
    'mi',
    'mine',
    'mykje',
    'no',
    'nokon',
    'noka',
    'nokor',
    'noko',
    'nokre',
    'si',
    'sia',
    'sidan',
    'so',
    'somt',
    'somme',
    'um',
    'upp',
    'vere',
    'vore',
    'verte',
    'vort',
    'varte',
    'vart',
];

$result =& $data;
unset($data);
return $result;
