<p>This tests for a bug where quotes escaped by PHP when using 
<code>preg_replace</code> with the <code>/e</code> modifier must be correctly unescaped
(hence the <code>_UnslashQuotes</code> function found only in PHP Markdown).</p>

<p>Headers below should appear exactly as they are typed (no backslash
added or removed).</p>

<h1><PERSON><PERSON> "quoted\" again \""</h1>

<h2><PERSON><PERSON> "quoted\" again \""</h2>

<h3><PERSON><PERSON> "quoted\" again \""</h3>

<p>Test with tabs for <code>_Detab</code>:</p>

<pre><code>Code    'block' with    some    "tabs"  and "quotes"
</code></pre>
