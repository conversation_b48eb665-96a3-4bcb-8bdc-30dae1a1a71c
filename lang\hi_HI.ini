about = "के बारे में"
add_category = "श्रेणी जोड़ना"
add_content = "सामग्री जोड़ें"
add_link = "लिंक_जोड़ें"
add_menu = "मेनू जोड़ें"
add_new_page = "नया पेज जोड़ें"
add_new_post = "नई पोस्ट जोड़ें"
add_source_link_optional = "स्रोत लिंक जोड़ें (वैकल्पिक)"
add_sub = "उप पृष्ठ जोड़ें"
address_url = "पता (URL)"
admin = "व्यवस्थापक"
admin_panel_style_based_on = "व्यवस्थापक पैनल शैली पर आधारित"
all_blog_posts = "सभी ब्लॉग पोस्ट"
all_cache_has_been_deleted = "सारा कैश (द्रुतिका) हटा दिया गया है|"
all_posts_tagged = "सभी पोस्ट टैग किए गए"
archive_for = "पुरालेख के लिए"
archive_page_for = "पृष्ठ पुरालेख के लिए"
archives = "अभिलेखागार"
are_you_sure_you_want_to_delete_ = "क्या आप आश्वस्त है कि आपको डिलीट करना है <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "फिलहाल आप ऑटो जेनरेटेड मेनू का उपयोग कर रहे हैं।"
audio_post = "ऑडियो पोस्ट"
audio_post_comment = "फ़ीचर्ड ऑडियो के साथ ब्लॉग पोस्ट बनाया जा रहा है"
author = "लेखक"
author_description = "केवल एक और HTMLy उपयोगकर्ता"
back_to = "वापस"
backup = "बैकअप"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "एक पैराग्राफ में, हमें अपने ब्लॉग के बारे में और जानकारी दे।"
blog_theme = "ब्लॉग थीम (शैली)"
blog_title = "ब्लॉग का शीर्षक"
blog_title_placeholder = "मेरा HTMLy ब्लॉग"
blog_posts_displayed_as = "ब्लॉग पोस्ट के प्रदर्शन का तरीका"
breadcrumb_home_text = "ब्रेडक्रंब होम टेक्स्ट"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_itv = "इस आयातक का उपयोग करके आप पुष्टि करते हैं कि फ़ीड आपकी है या कम से कम आपके पास इसे प्रकाशित करने का अधिकार है।"
css_class_optional = "सीएसएस क्लास (वैकल्पिक)"
cache_expiration = "कैश (द्रुतिका) समाप्ति (घंटे में)"
cache_off = "कैश द(्रुतिका) बंद"
cache_timestamp = "कैश (द्रुतिका)  समयमोहर"
cancel = "रद्द करना"
cannot_read_feed_content = "फ़ीड सामग्री नहीं पढ़ सकता"
captcha_error = "रीकैप्चा सही नहीं है"
categories = "श्रेणियाँ"
category = "वर्ग"
check_update = "अपडेट जांचें"
clear_cache = "कैश (द्रुतिका)  को साफ़ करें"
comma_separated_values = "अल्पविराम से अलग किये गए मान"
comment_system = "टिप्पणी प्रणाली"
comments = "टिप्पणियाँ"
config = "कॉन्फ़िग"
congrats_you_have_the_latest_version_of_htmly = "बधाई! आपके पास  HTMLy का नवीनतम संस्करण है|"
content = "विषय"
contents = "विषय-सूची"
copyright_line = "कॉपीराइट लाइन"
copyright_line_placeholder = "(C) आपका नाम|"
create_backup = "बैकअप बनाना"
created = "बनाया था"
custom = "स्वनिर्धारित"
custom_settings = "कस्टम सेटिंग्स"
dashboard = "डैशबोर्ड"
date = "तारीख"
date_format = "तारिख का प्रारूप"
delete = "मिटाना"
description = "विवरण"
disable = "अक्षम करना"
disabled = "अक्षम"
disqus_shortname = "डिस्कस का नाम"
disqus_shortname_placeholder = "htmly"
draft = "ड्राफ्ट"
edit = "संपादन करें"
edit_category = "श्रेणी संपादित करें"
edit_post = "संपादन करें"
edit_profile = "प्रोफ़ाइल संपादित करें"
enable = "सक्षम करें"
enable_blog_url = "सक्षम करे ब्लॉग URL"
enter_image_url = "छवि URL दर्ज करें"
facebook_app_id = "फेसबुक ऐप आईडी"
facebook_page = "फेसबुक पेज"
featured_audio = "विशेष रुप से प्रदर्शित ऑडियो"
featured_image = "फीचर्ड चित्र"
featured_link = "विशेष रुप से प्रदर्शित लिंक"
featured_quote = "विशेष उद्धरण"
featured_video = "विशेष रुप से प्रदर्शित वीडियो"
feed_url = "फीड URL"
filename = "फ़ाइल का नाम"
follow = "अनुसरण करना"
for_google_site_verification_meta = "Google साइट सत्यापन मेटा के लिए"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "फ्रंट पेज प्रदर्शित करता है"
full_post = "पूरी पोस्ट"
general = "सामान्य"
general_settings = "सामान्य सेटिंग्स"
get_one_here = "यहां एक प्राप्त करें"
github_pre_release = "Github प्री-रिलीज़"
google_analytics = "Google एनालिटिक्स"
google_analytics_legacy = "Google एनालिटिक्स (प्राचीन)"
google_search_console = "Google खोज कंसोल"
home = "घर"
if_left_empty_we_will_excerpt_it_from_the_content_below = "यदि खाली छोड़ दिया जाए तो हम इसे नीचे दी गई सामग्री से उद्धृत करेंगे"
if_the_url_is_left_empty_we_will_use_the_page_title = "यदि URL खाली छोड़ दिया जाए तो हम पृष्ठ शीर्षक का उपयोग करेंगे"
if_the_url_is_left_empty_we_will_use_the_post_title = "यदि URL खाली छोड़ दिया जाए तो हम पोस्ट शीर्षक का उपयोग करेंगे"
image_post = "छवि पोस्ट"
image_post_comment = "विशेष रुप से प्रदर्शित छवि के साथ ब्लॉग पोस्ट बनाया जा रहा है"
import = "आयात"
import_feed = "फ़ीड आयात प्रारंभ करें"
import_rss = "RSS आयात करें"
import_rss_feed_2.0 = "RSS फ़ीड 2.0 आयात करें"
insert_image = "चित्र डालें"
invalid_error = "त्रुटि! अमान्य उपयोगकर्ता नाम या पासवर्ड"
language = "सिस्टम भाषा"
link_name = "लिंक नाम"
link_post = "लिंक पोस्ट"
link_post_comment = "विशेष रुप से प्रदर्शित लिंक के साथ ब्लॉग पोस्ट बनाया जा रहा है"
login = "लॉग इन करें"
login_page = "लोग इन वाला पन्ना"
logout = "लॉग आउट"
menu = "मेन्यू"
menus = "मेनू संपादक"
meta_description = "मेटा विवरण"
meta_description_character = "मेटा विवरण अक्षर"
metatags = "मेटा टैग"
metatags_settings = "मेटाटैग सेटिंग्स"
mine = "मेरा"
more = "अधिक"
my_draft = "मेरा ड्राफ्ट"
my_posts = "मेरी पोस्ट"
name = "नाम"
newer = "नया"
next = "अगला"
next_post = "अगली पोस्ट"
no_available_backup = "इस समय कोई बैकअप उपलब्ध नहीं है|"
no_draft_found = "कोई ड्राफ्ट नहीं मिला"
no_posts_found = "कोई प्रकाशन नहीं मिला"
no_related_post_found = "कोई संबंधित पोस्ट नहीं मिली"
no_scheduled_posts_found = "कोई निर्धारित पोस्ट नहीं मिली!"
no_search_results = "खोजने पर कोई परिणाम नहीं मिला"
nope = "नहीं"
not = "नहीं"
older = "पुराने"
only = "केवल"
operations = "संचालन"
page = "पृष्ठ"
page_generation_time = "पेज निर्माण का समय"
pages = "पृष्ठों"
pass_error = "पासवर्ड फ़ील्ड आवश्यक है"
password = "पासवर्ड"
performance = "प्रदर्शन"
performance_settings = "प्रदर्शन समायोजन"
permalink = "स्थायी लिंक"
popular = "लोकप्रिय"
popular_posts = "लोकप्रिय लेख"
popular_posts_widget = "लोकप्रिय पोस्ट विजेट"
popular_posts_widget_at_most = "अधिक से अधिक लोकप्रिय पोस्ट विजेट"
popular_tags = "प्रसिद्ध टग्स"
post_by_author = "इस लेखक द्वारा पोस्ट"
posted_in = "में प्रकाशित किया गया था"
posted_on = "प्रकाशित किया गया"
posts = "पोस्ट"
posts_by = "द्वारा पोस्ट"
posts_draft = "पोस्ट ड्राफ्ट"
posts_in_archive_page_at_most = "अधिकतम संग्रह पृष्ठ में पोस्ट"
posts_in_category_page_at_most = "अधिकतम श्रेणी पृष्ठ में पोस्ट"
posts_in_front_page_show_at_most = "पहले पन्ने पर पोस्ट अधिकतम दिखाई देती हैं"
posts_in_profile_page_at_most = "अधिकतम प्रोफ़ाइल पृष्ठ पर पोस्ट करें"
posts_in_search_result_at_most = "अधिकतम खोज परिणाम में पोस्ट"
posts_in_tag_page_at_most = "अधिकतम टैग पेज में पोस्ट"
posts_in_type_page_at_most = "अधिक से अधिक टाइप पेज में पोस्ट करें"
posts_index_settings = "इंडेक्स सेटिंग्स पोस्ट करें"
posts_list = "पोस्ट सूची"
posts_tagged = "टैग किए गए पोस्ट"
posts_with_type = "प्रकार सहित पोस्ट"
pre_release = "पूर्व-रिलीज़"
prev = "पिछला"
prev_post = "पिछला पोस्ट"
preview = "झलकी"
profile_for = "के लिए प्रोफाइल"
proudly_powered_by = "गर्व से संचालित"
publish = "प्रकाशित करना"
publish_draft = "ड्राफ्ट प्रकाशित करें"
published = "प्रकाशित"
quote_post = "उद्धरण पोस्ट"
quote_post_comment = "विशेष उद्धरण के साथ ब्लॉग पोस्ट बनाया जा रहा है"
rss_character = "RSS अक्षर"
rss_feeds_show_the_most_recent = "RSS फ़ीड नवीनतम दिखाते हैं "
rss_settings = "RSS समायोजन"
read_more_text = "और अधिक पाठ पढ़ें"
read_more_text_placeholder = "अधिक पढ़े"
reading = "पढ़ना"
reading_settings = "सेटिंग पढ़ना"
recaptcha = "रीकैप्चा"
recent_posts = "हाल के पोस्ट"
recent_posts_widget_at_most = "अधिक से अधिक हालिया पोस्ट विजेट"
regular_post = "नियमित पोस्ट"
regular_post_comment = "नियमित ब्लॉग पोस्ट बनाया जा रहा है"
related_posts = "संबंधित पोस्ट"
related_widget_posts_at_most = "अधिक से अधिक संबंधित विजेट पोस्ट"
revert_to_draft = "ड्राफ्ट पर वापस लौटें"
save = "सहेजें"
save_config = "कॉन्फ़िगरेशन सहेजें"
save_edit = "संपादित सहेजें"
save_menu = "मेनू सहेजें"
save_as_draft = "ड्राफ्ट के रूप में सेव करें"
save_category = "श्रेणी सहेजें"
scheduled = "अनुसूचित"
scheduled_posts = "अनुसूचित पोस्ट"
scheduled_tips = "किसी पोस्ट को भविष्य की तारीख या समय के साथ प्रकाशित करने पर वह निर्धारित पोस्ट में चला जाएगा"
search = "खोज"
search_for = "निम्न को खोजें"
search_results_for = "खोजे गए परिणाम"
search_results_not_found = "खोज परिणाम नहीं मिले!"
secret_key = "गुप्त कुंजी"
settings = "समायोजन"
sign_in_to_start_your_session = "अपना सत्र शुरू करने के लिए साइन इन करें"
site_key = "कार्यस्थल की कुंजी"
sitemap = "साइट मैप"
slug = "काउंटर"
social_media = "सामाजिक मीडिया"
static_page = "स्टेटिक पृष्ठ"
static_page_comment = "स्टेटिक पेज बनाया जा रहा है"
static_pages = "स्टेटिक पेज"
summary = "सारांश"
summary_character = "सारांश अक्षर"
tag = "टैग"
tagcloud_widget_at_most = "अधिक से अधिक टैगक्लाउड"
tagline = "टैग लाइन"
tagline_placeholder = "डेटाबेस रहित PHP ब्लॉगिंग प्लेटफ़ॉर्म"
tagline_description = "कुछ शब्दों में बताएं कि यह ब्लॉग किस बारे में है।"
tags = "टैग"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "यह लीगेसी कोड है. आमतौर पर gtag.js का उपयोग करके नए विश्लेषण तैयार किए जाते हैं"
this_page_doesnt_exist = "यह पेज मौजूद नहीं है!"
time = "समय"
timezone = "समय क्षेत्र"
title = "शीर्षक"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "डिस्कस या फेसबुक टिप्पणियों का उपयोग करने के लिए आपको अपना डिस्कस संक्षिप्त नाम या अपनी फेसबुक ऐप आईडी प्रदान करनी होगी।"
token_error = "CSRF टोकन सही नहीं है"
tools = "औजार"
twitter_account = "ट्विटर खाता"
type_to_search = "खोजने के लिए लिखें"
uncategorized = "अवर्गीकृत"
uncategorized_comment = "ऐसे विषय जिन्हें किसी श्रेणी की आवश्यकता नहीं है, या किसी अन्य मौजूदा श्रेणी में फिट नहीं बैठते हैं"
universal_analytics = "यूनिवर्सल एनालिटिक्स (gtag.js)"
unknown_feed_format = "अज्ञात फ़ीड प्रारूप"
update = "अद्यतन"
update_available = "उपलब्ध अद्यतन"
update_draft = "ड्राफ्ट अद्यतन करें"
update_post = "पोस्ट अपडेट करें"
update_to = "इसे अपडेट करें"
upload = "अपलोड"
user = "उपयोगकर्ता"
user_error = "उपयोगकर्ता फ़ील्ड आवश्यक है"
valid_values_range_from_0_to_1.0._see = "वैध मूल्यों 0 से 1.0 की रेंज में है| आगे देखिये ..."
video_post = "वीडियो पोस्ट"
video_post_comment = "विशेष रुप से प्रदर्शित वीडियो के साथ ब्लॉग पोस्ट बनाया जा रहा है"
view = "देखिए"
view_post = "देखिए"
views = "देखा गया"
widget = "विजेट"
widget_settings = "विजेट सेटिंग्स"
would_you_like_to_try_our = "क्या आप आज़माना चाहेंगे हमारा "
yes_im_in = "हा मुझे मंजूर है"
yes_not_recommended = "हाँ (अनुशंसित नहीं)"
you_dont_have_permission_to_access_this_page = "आपको इस पृष्ठ तक पहुंचने की अनुमति नहीं है"
your_new_config_key = "आपकी नई कॉन्फ़िगरेशन कुंजी"
your_new_value = "आपका नया मूल्य"
your_backups = "आपके बैकअप"
your_latest_blog_posts = "आपके नवीनतम ब्लॉग पोस्ट"
your_recent_posts = "आपकी हाल की पोस्ट"
by = "द्वारा"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>संकेत:</u> अपनी कॉन्फ़िगरेशन कुंजी या मान खोजने के लिए <code>Ctrl</code>/<code>CMD</code> + <code>F</code> का उपयोग करें।"
homepage = "मुखपृष्ठ"
instead = "के बजाय"
item_class = "CSS क्लास डालें"
item_slug = "लिंक URL डालें"
now = "अब"
of = "का"
optional = "वैकल्पिक"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>प्रो युक्तियाँ:</u>आप कस्टम कॉन्फिग कुंजी बना सकते हैं और अपने टेम्पलेट में कहीं भी अपनी कॉन्फिग कुंजी मान प्रिंट कर सकते हैं|"
read_more = "और पढ़ें"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "सारांश व्यवहार"
default = "Default"
check_shortcode = "शॉर्टकोड जांचें"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "सारांश मोड में, सामग्री को x वर्ण में ट्रिम करने से पहले शॉर्टकोड की जांच करें या नहीं"
manage_users = "उपयोगकर्ताओं को प्रबंधित करें"
add_user = "उपयोगकर्ता जोड़ें"
username = "उपयोगकर्ता नाम"
role = "भूमिका"
change_password = "पासवर्ड बदलें"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
