about = "Acerca de"
add_category = "Añadir categoría"
add_content = "Agregar contenido"
add_link = "Agregar enlace"
add_menu = "Agregar menú"
add_new_page = "Agregar nueva página"
add_new_post = "Agregar nueva publicación"
add_source_link_optional = "Agregar enlace de origen (opcional)"
add_sub = "Agregar sub categoría"
address_url = "Address (URL)"
admin = "Administración"
admin_panel_style_based_on = "Estilo del panel de administración basado en"
all_blog_posts = "Todas las publicaciones del blog"
all_cache_has_been_deleted = "¡Se ha eliminado todo el caché!"
all_posts_tagged = "Todas las publicaciones etiquetadas"
archive_for = "Archivar para"
archive_page_for = "Página de archivo para"
archives = "Archivo"
are_you_sure_you_want_to_delete_ = "Está seguro de que desea eliminar <strong>% s </strong>?"
at_the_moment_you_are_using_auto_generated_menu = "En este momento estás utilizando el menú generado automáticamente."
audio_post = "Publicación de audio"
audio_post_comment = "Crear una  publicación de blog con audio destacado"
author = "Autor"
author_description = "Sólo otro usuario HTMLy"
back_to = "Volver a"
backup = "Respaldo"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "En un párrafo, cuéntanos más sobre tu blog."
blog_theme = "Tema del blog"
blog_title = "Título del blog"
blog_title_placeholder = "Mi HTMLy Blog"
blog_posts_displayed_as = "Las publicaciones del blog se muestran como"
breadcrumb_home_text = "Texto de inicio de ruta de navegación"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Al utilizar este importador, aceptas si el feed es tuyo o al menos tienes la autoridad para publicarlo."
css_class_optional = "Clase CSS (opcional)"
cache_expiration = "Caducidad de la caché (en horas)"
cache_off = "Caché desactivado"
cache_timestamp = "Marca de tiempo de caché"
cancel = "Cancelar"
cannot_read_feed_content = "No se puede leer el contenido del feed"
captcha_error = "reCaptcha no es correcto"
categories = "Categorías"
category = "Categoría"
check_update = "Buscar actualizaciones"
clear_cache = "Limpiar cache"
comma_separated_values = "Valores separados por comas"
comment_system = "Sistema de comentarios"
comments = "Comentarios"
config = "Configuración"
congrats_you_have_the_latest_version_of_htmly = "¡Felicidades! Tienes la última versión de HTMLy."
content = "Contenido"
contents = "Contenido"
copyright_line = "Línea de derechos de autor"
copyright_line_placeholder = "(c) Su nombre."
create_backup = "Crear copia de seguridad"
created = "Creado"
custom = "Personalizado"
custom_settings = "Configuración personalizada"
dashboard = "Panel de control"
date = "Fecha"
date_format = "Formato de fecha"
delete = "Eliminar"
description = "Descripción"
disable = "Desactivar"
disabled = "Desactivado"
disqus_shortname = "Nombre corto de Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Borrador"
edit = "Editar"
edit_category = "Editar categoría"
edit_post = "Editar"
edit_profile = "Editar perfil"
enable = "Habilitar"
enable_blog_url = "Habilitar URL del blog"
enter_image_url = "Ingrese la URL de la imagen"
facebook_app_id = "ID de aplicación de Facebook"
facebook_page = "página de Facebook"
featured_audio = "Audio destacado"
featured_image = "Imagen destacada"
featured_link = "Enlace destacado"
featured_quote = "Cita destacada"
featured_video = "Vídeo destacado"
feed_url = "URL de la fuente"
filename = "Nombre del archivo"
follow = "Seguir"
for_google_site_verification_meta = "Para meta de verificación del sitio de Google"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Visualización de la página principal"
full_post = "Publicación completa"
general = "Generales"
general_settings = "Configuración general"
get_one_here = "Consigue uno aquí"
github_pre_release = "Prelanzamiento de Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (heredado)"
google_search_console = "Consola de búsqueda de Google"
home = "Inicio"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Si lo dejamos vacío, lo extraeremos del contenido siguiente"
if_the_url_is_left_empty_we_will_use_the_page_title = "Si la URL se deja vacía usaremos el título de la página"
if_the_url_is_left_empty_we_will_use_the_post_title = "Si la URL se deja vacía usaremos el título de la publicación"
image_post = "Publicación de imagen"
image_post_comment = "Crear publicación de blog con imagen destacada"
import = "Importar"
import_feed = "Iniciar importación de feed"
import_rss = "Importar RSS"
import_rss_feed_2.0 = "Importar fuente RSS 2.0"
insert_image = "Insertar imagen"
invalid_error = "ERROR: nombre de usuario o contraseña no válidos"
language = "Idioma del sistema"
link_name = "Nombre del enlace"
link_post = "Publicación de un Enlace"
link_post_comment = "Crear una publicación de blog con un enlace destacado"
login = "acceso"
login_page = "Página de inicio de sesión"
logout = "Cerrar sesión"
menu = "Menú"
menus = "Editor de menús"
meta_description = "Meta descripción"
meta_description_character = "Carácter de meta descripción"
metatags = "Metaetiquetas"
metatags_settings = "Configuración de metaetiquetas"
mine = "Mía"
more = "Más"
my_draft = "Mis borradores"
my_posts = "Mis publicaciones"
name = "Nombre"
newer = "Más nuevo"
next = "Próximo"
next_post = "Publicación siguiente"
no_available_backup = "No hay copia de seguridad disponible en este momento."
no_draft_found = "No se encontró ningún borrador"
no_posts_found = "No se han encontrado publicaciones"
no_related_post_found = "No se encontró ninguna publicación relacionada"
no_scheduled_posts_found = "¡No se encontraron publicaciones programadas!"
no_search_results = "Sin resultados de búsqueda"
nope = "Nope"
not = "No"
older = "Mayor"
only = "Solo"
operations = "Operaciones"
page = "Página"
page_generation_time = "Tiempo de generación de página"
pages = "Páginas"
pass_error = "El campo de contraseña es obligatorio"
password = "Contraseña"
performance = "Rendimiento"
performance_settings = "Configuración de rendimiento"
permalink = "enlace permanente"
popular = "Populares"
popular_posts = "Entradas populares"
popular_posts_widget = "Widget de publicaciones populares"
popular_posts_widget_at_most = "Máximo de publicaciones populares"
popular_tags = "Etiquetas Populares"
post_by_author = "Publicaciones de este autor"
posted_in = "Publicado en"
posted_on = "Publicado en"
posts = "Publicaciones"
posts_by = "Publicaciones por"
posts_draft = "Publicaciones en Borrador"
posts_in_archive_page_at_most = "Máximo de Publicaciones en la página archivo"
posts_in_category_page_at_most = "Máximo de Publicaciones en la página categoría"
posts_in_front_page_show_at_most = "Máximo de Publicaciones en la página página principal"
posts_in_profile_page_at_most = "Máximo de Publicaciones en la página de perfil"
posts_in_search_result_at_most = "Máximo de Publicaciones en el resultado de búsqueda"
posts_in_tag_page_at_most = "Máximo de Publicaciones en la página de etiquetas"
posts_in_type_page_at_most = "Máximo de Publicaciones en la página tipo"
posts_index_settings = "Configuración del índice de publicaciones"
posts_list = "Lista de publicaciones"
posts_tagged = "Publicaciones etiquetadas"
posts_with_type = "Publicaciones con tipo"
pre_release = "Prelanzamiento"
prev = "Antiguo"
prev_post = "Publicación anterior"
preview = "Vista anticipada"
profile_for = "Perfil para"
proudly_powered_by = "Orgullosamente impulsado por"
publish = "Publicar"
publish_draft = "Publicar borrador"
published = "Publicado"
quote_post = "Publicación de Cita destacada"
quote_post_comment = "Crear una publicación de blog con cita destacada"
rss_character = "carácter RSS"
rss_feeds_show_the_most_recent = "Los canales RSS muestran los más recientes"
rss_settings = "Configuración RSS"
read_more_text = "Leer más texto"
read_more_text_placeholder = "Leer más"
reading = "Lectura"
Reading_settings = "Configuración de lectura"
recaptcha = "reCAPTCHA"
recent_posts = "Mensajes recientes"
recent_posts_widget_at_most = "Widget de publicaciones recientes como máximo"
regular_post = "Publicación de blog regular"
regular_post_comment = "Crear una publicación de blog regular"
related_posts = "Artículos Relacionados"
related_widget_posts_at_most = "Publicaciones de widgets relacionados como máximo"
revert_to_draft = "Volver a borrador"
save = "Salvar"
save_config = "Guardar configuración"
save_edit = "Guardar edición"
save_menu = "Guardar menú"
save_as_draft = "Guardar como borrador"
save_category = "Guardar categoría"
scheduled = "Publicaciones programadas"
scheduled_posts = "Publicaciones programadas"
scheduled_tips = "Al publicar una publicación con fecha u hora futura, irá a las publicaciones programadas"
search = "Buscar"
search_for = "Buscar"
search_results_for = "Resultados de búsqueda para"
search_results_not_found = "¡No se encontraron resultados de búsqueda!"
secret_key = "Clave secreta"
settings = "Configuración"
sign_in_to_start_your_session = "Inicia sesión para iniciar tu sesión"
site_key = "Clave del sitio"
sitestamp = "Mapa del sitio"
slug = "Babosa"
social_media = "Medios sociales"
static_page = "Página estática"
static_page_comment = "Crear una página estática"
static_pages = "Páginas estáticas"
summary = "Sumario"
summary_character = "Carácter sumario"
tag = "Etiqueta"
tagcloud_widget_at_most = "TagCloud como máximo"
tagline = "Tagline"
tagline_placeholder = "Plataforma de blogs PHP sin base de datos"
tagline_description = "En pocas palabras, explica de qué trata este blog."
tags = "Etiquetas"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Este es el código heredado. Generalmente se crean nuevos análisis usando gtag.js."
this_page_doesnt_exist = "¡Esta página no existe!"
time = "Tiempo"
timezone = "Zona horaria"
title = "Título"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Para utilizar Disqus o comentarios de Facebook, debe proporcionar el nombre corto de Disqus o el ID de la aplicación de Facebook."
token_error = "El token CSRF no es correcto"
tools = "Útiles"
twitter_account = "cuenta de Twitter"
type_to_search = "Escriba para buscar"
uncategorized = "Sin categorizar"
uncategorized_comment = "Temas que no necesitan una categoría o que no encajan en ninguna otra categoría existente"
universal_analytics = "Análisis universal (gtag.js)"
unknown_feed_format = "Formato de alimentación desconocido"
update = "Actualizar"
update_available = "Actualización disponible"
update_draft = "Actualizar borrador"
update_post = "Actualizar publicación"
update_to = "Actualizar a"
upload = "subir"
user = "Usuario"
user_error = "El campo de usuario es obligatorio"
valid_values_range_from_0_to_1.0._see = "Los valores válidos varían de 0,0 a 1,0. Ver"
video_post = "Publicación de video"
video_post_comment = "Crear una publicación de blog con video destacado"
view = "Ver"
view_post = "Ver"
views = "Puntos de vista"
widget = "Widget"
widget_settings = "Configuración de widgets"
would_you_like_to_try_our = "¿Te gustaría probar nuestra "
yes_im_in = "Sí, estoy en"
yes_not_recommended = "Sí (no recomendado)"
you_dont_have_permission_to_access_this_page = "No tienes permiso para acceder a esta pagina"
your_new_config_key = "Su nueva clave de configuración"
your_new_value = "Tu nuevo valor"
your_backups = "Tus copias de seguridad"
your_latest_blog_posts = "Tus últimas publicaciones de blog"
your_recent_posts = "Tus publicaciones recientes"
by = "por"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>sugerencia:</u> Utilice <code>Ctrl</code>/<code>CMD</code> + <code>F</code> para buscar su clave o valor de configuración."
homepage = "página de inicio"
instead = "en lugar"
item_class = "Insertar clase CSS"
item_slug = "Insertar URL del enlace"
now = "ahora"
of = "de"
optional = "opcional"
post_your_post_slug = "/post/tu-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>consejos profesionales:</u> Puede crear una clave de configuración personalizada e imprimir el valor de su clave de configuración en cualquier lugar de su plantilla."
read_more = "Lee mas"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Comportamiento resumido"
default = "Predeterminado"
check_shortcode = "comprobar el código corto"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "En el modo resumen, verifique el código abreviado primero o no antes de recortar el contenido a x char"
manage_users = "Administrar usuarios"
add_user = "Agregar usuario"
username = "nombre de usuario"
role = "rol"
change_password = "Cambiar contraseña"
config_mfa = "Configurar MFA"
mfacode = "Código MFA"
verify_code = "Verificar el código MFA"
verify_password = "Verificar la contraseña actual"
manualsetupkey = "También puede agregar manualmente la clave de configuración"
mfa_error = "El código MFA no es correcto"
disablemfa = "Deshabilitar MFA"
enable_auto_save = "Habilitar guardado automático"
explain_autosave = "Cuando está habilitado, el contenido nuevo o el borrador se guardarán automáticamente cada 60 segundos".
login_protect_system = "Sistema de protección de inicio de sesión"
cloudflare_info = "Revise la documentación de Turnstile de Cloudflare: "
mfa_config = "Autenticación multifactor (MFA)"
set_mfa_globally = "Establecer el estado de MFA"
explain_mfa = "Cuando está habilitado, MFA es opcional para todos los usuarios. Cuando está deshabilitado, ningún usuario puede usarlo y oculta el campo en la página de inicio de sesión".
set_version_publicly = "Visibilidad de la versión"
explain_version = "De manera predeterminada, la versión de HTMLy es visible públicamente en el código fuente; algunos administradores pueden preferir ocultar esto."
focus_mode = "Activar/desactivar foco"
writing = "Escritura"
writing_settings = "Configuración de escritura"
security = "Seguridad"
security_settings = "Configuración de seguridad"
msg_error_field_req_username = "El campo de nombre de usuario es obligatorio."
msg_error_field_req_password = "El campo de contraseña es obligatorio."
msg_error_field_req_title = "El campo de título es obligatorio."
msg_error_field_req_content = "El campo de contenido es obligatorio."
msg_error_field_req_tag = "El campo de etiqueta es obligatorio."
msg_error_field_req_image = "El campo de imagen es obligatorio."
msg_error_field_req_video = "El campo de vídeo es obligatorio."
msg_error_field_req_link = "El campo de enlace es obligatorio."
msg_error_field_req_quote = "El campo de cita es obligatorio."
msg_error_field_req_audio = "El campo de audio es obligatorio."
msg_error_field_req_feedurl = "Debe especificar la URL del feed."
rss_feeds_description_select = "Descripción RSS"
rss_description_body = "Cuerpo de la publicación"
rss_description_meta = "Metadescripción de la publicación"
admin_theme = "Tema de administración"
admin_theme_light = "Claro"
admin_theme_dark = "Oscuro"
search_index = "Índice de búsqueda"
fulltext_search = "Búsqueda de texto completo"
add_search_index = "Añadir publicaciones al índice de búsqueda"
clear_search_index = "Borrar índice de búsqueda"
unindexed_posts = "Aquí se encuentran las publicaciones que no han sido indexadas"
indexed_posts = "Las publicaciones han sido indexadas"
custom_fields = "Campos personalizados"
