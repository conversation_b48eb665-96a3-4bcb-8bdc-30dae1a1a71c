about = "Sobre"
add_category = "Adicionar categoria"
add_content = "Adicionar conteúdo"
add_link = "Adicionar link"
add_menu = "Adicionar menu"
add_new_page = "Adicionar nova página"
add_new_post = "Adicionar nova postagem"
add_source_link_optional = "Adicione o link da fonte (opcional)"
add_sub = "Adicionar sub página"
address_url = "Endereço (URL)"
admin = "Admin"
admin_panel_style_based_on = "Painel de administração baseado no"
all_blog_posts = "Todas as postagens do blog"
all_cache_has_been_deleted = "Todo cache foi excluído !"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Arquivos"
are_you_sure_you_want_to_delete_ = "Tem certeza de que deseja excluir <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "No momento você está usando a geração automática de menus."
audio_post = "Postagem com áudio"
audio_post_comment = "Criação de postagem com áudio em destaque"
author = "Autor"
author_description = "Just another HTMLy user"
back_to = "Voltar para"
backup = "Backup"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Em um parágrafo, descreva sobre seu site/blog."
blog_theme = "Tema do site/blog"
blog_title = "Título (site/blog)"
blog_title_placeholder = "Meu site / blog"
blog_posts_displayed_as = "Exibir postagens como"
breadcrumb_home_text = "Breadcrumb: texto da página inicial"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Utilizando este importador você está de acordo que o feed é seu ou pelo menos tenha a autorização para publicar."
css_class_optional = "Classe CSS (opcional)"
cache_expiration = "Expiração do cache (em horas)"
cache_off = "Desativar Cache"
cache_timestamp = "Timestamp do cache"
cancel = "Cancelar"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "Categorias"
category = "Categoria"
check_update = "Verificar atualização"
clear_cache = "Limpar cache"
comma_separated_values = "Os valores devem ser separados por virgula"
comment_system = "Sistema de comentário"
comments = "Comentários"
config = "Configurar"
congrats_you_have_the_latest_version_of_htmly = "Parabêns ! Você tem a última versão do HTMLy."
content = "Conteúdo"
contents = "Conteúdo"
copyright_line = "Direitos reservados"
copyright_line_placeholder = "(c) Seu nome."
create_backup = "Gerar backup"
created = "Criado"
custom = "Customizar"
custom_settings = "Configurações customizadas"
dashboard = "Painel de Controle"
date = "Data"
date_format = "Formato de data"
delete = "Excluir"
description = "Descrição"
disable = "Desabilitar"
disabled = "Desabilitado"
disqus_shortname = "Usuário Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Rascunho"
edit = "Editar"
edit_category = "Editar categoria"
edit_post = "Edit"
edit_profile = "Editar perfil"
enable = "Habilitar"
enable_blog_url = "Habilitar a URL blog"
enter_image_url = "Insira a URL da imagem"
facebook_app_id = "Facebook App ID"
facebook_page = "Página do Facebook"
featured_audio = "Audio em destaque"
featured_image = "Imagem em destaque"
featured_link = "Link em destaque"
featured_quote = "Citação em destaque"
featured_video = "Video em destaque"
feed_url = "URL do Feed"
filename = "Nome do arquivo"
follow = "Seguir"
for_google_site_verification_meta = "Para o meta google-site-verification"
for_msvalidate_01_meta = "Para o meta msvalidate.01"
front_page_displays = "Mostrar na primeira página"
full_post = "Postagem completa"
general = "Geral"
general_settings = "Configurações Gerais"
get_one_here = "Efetue as configuração no link"
github_pre_release = "Pré-lançamento do Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legado)"
google_search_console = "Google Search Console"
home = "Inicial"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Se deixar em branco, iremos extraí-lo do conteúdo abaixo"
if_the_url_is_left_empty_we_will_use_the_page_title = "Se a url estiver vazia, usaremos o título da página"
if_the_url_is_left_empty_we_will_use_the_post_title = "Se a url estiver vazia, usaremos o título da postagem"
image_post = "Postagem com imagem"
image_post_comment = "Criação de postagem com imagem em destaque"
import = "Importar"
import_feed = "Iniciar importação do feed"
import_rss = "Importar RSS"
import_rss_feed_2.0 = "Importar o Feed RSS 2.0"
insert_image = "Inserir Imagem"
invalid_error = "ERROR: Invalid username or password"
language = "Idioma do sistema"
link_name = "Nome do link"
link_post = "Postagem de link"
link_post_comment = "Criação de postagem com link em destaque"
login = "Logar"
login_page = "Página de login"
logout = "Sair"
menu = "Menu"
menus = "Editor de menus"
meta_description = "Meta Descrição"
meta_description_character = "Caracteres da 'Meta description'"
metatags = "Metatags"
metatags_settings = "Configurações de Metatags"
mine = "Meu"
more = "Mais"
my_draft = "Meu rascunho"
my_posts = "Minhas postagens"
name = "Nome"
newer = "Mais recente"
next = "Próximo"
next_post = "Próxima postagem"
no_available_backup = "Não há nenhum backup no momento."
no_draft_found = "Nenhum rascunho encontrado"
no_posts_found = "nenhuma postagem encontrada"
no_related_post_found = "Nenhuma postagem relacionada encontrada"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Não"
not = "Não"
older = "Antigo"
only = "Somente"
operations = "Operações"
page = "Page"
page_generation_time = "Tempo de geração da página"
pages = "Pages"
pass_error = "Password field is required"
password = "Senha"
performance = "Performance"
performance_settings = "Configurações de Performance"
permalink = "Link permanente"
popular = "Popular"
popular_posts = "Postagens populares"
popular_posts_widget = "Posts populares"
popular_posts_widget_at_most = "Limite de post populares"
popular_tags = "Tags populares"
post_by_author = "Posts by this author"
posted_in = "Postado em"
posted_on = "Posted em"
posts = "Postagens"
posts_by = "Posts by"
posts_draft = "Rascunhos"
posts_in_archive_page_at_most = "Limite na página de arquivados"
posts_in_category_page_at_most = "Limite na página de categoria"
posts_in_front_page_show_at_most = "Limitar posts na primeira página"
posts_in_profile_page_at_most = "Limite na página de perfil"
posts_in_search_result_at_most = "Limite em resultados de pesquisa"
posts_in_tag_page_at_most = "Limite na página de tags"
posts_in_type_page_at_most = "Limite na página por tipo (type)"
posts_index_settings = "Configurações de índice das postagens"
posts_list = "Postagens"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pré-lançamento"
prev = "Antigo"
prev_post = "Postagem anterior"
preview = "Pré visualização"
profile_for = "Profile for"
proudly_powered_by = "Orgulhosamente desenvolvido por"
publish = "Publicar"
publish_draft = "Publicar rascunho"
published = "Publicados"
quote_post = "Postagem com citação"
quote_post_comment = "Criação de postagem de blog com citação em destaque"
rss_character = "Limite de caracteres do RSS"
rss_feeds_show_the_most_recent = "Limite de postagens recentes"
rss_settings = "Configurações RSS"
read_more_text = "Texto para continuar a leitura"
read_more_text_placeholder = "Leia mais"
reading = "Leitura"
reading_settings = "Configurações de Leitura"
recaptcha = "reCAPTCHA"
recent_posts = "Postagens recentes"
recent_posts_widget_at_most = "Limite de posts recentes"
regular_post = "Postagem padrão"
regular_post_comment = "Criação de postagem padrão no blog"
related_posts = "Postagens relacionadas"
related_widget_posts_at_most = "Limite de posts relacionados"
revert_to_draft = "Reverter para rascunho"
save = "Salvar"
save_config = "Salvar configurações"
save_edit = "Salvar edição"
save_menu = "Salvar menu"
save_as_draft = "Salvar como rascunho"
save_category = "Salvar categoria"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Buscar"
search_for = "Buscar por"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Chave secreta"
settings = "Configurações"
sign_in_to_start_your_session = "Faça login para iniciar a sessão"
site_key = "Chave do site"
sitemap = "Sitemap"
slug = "Slug"
social_media = "Mídia Social"
static_page = "Página estática"
static_page_comment = "Criação de página estática"
static_pages = "Páginas estáticas"
summary = "Postagem Resumida"
summary_character = "Limite de caracteres do resumo"
tag = "Tag"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Slogan"
tagline_placeholder = "Descreva um slogan"
tagline_description = "Em poucas palavras defina o site/blog de forma objetiva."
tags = "Tags"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Este é um código legado. Normalmente o novo Analytics usa o gtag.js"
this_page_doesnt_exist = "A página requisitada não existe !"
time = "Hora"
timezone = "Fuso horário"
title = "Título"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Para usar o Disqus ou comentário do Facebook, você precisa fornecer o nome abreviado do Disqus ou o ID do aplicativo do Facebook."
token_error = "CSRF Token not correct"
tools = "Ferramentas"
twitter_account = "Conta do Twitter"
type_to_search = "Digite para buscar"
uncategorized = "Sem categoria"
uncategorized_comment = "Tópicos que não precisam de uma categoria ou não se encaixam em nenhuma outra categoria existente"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Atualizar"
update_available = "Atualização disponível"
update_draft = "Atualizar rascunho"
update_post = "Atualizar postagem"
update_to = "Atualizar para"
upload = "Carregar"
user = "Usuário"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Valores válidos são entre 0.0 e 1.0. Veja mais em"
video_post = "Postagem de video"
video_post_comment = "Criação de postagem com vídeo em destaque"
view = "Visualizar"
view_post = "View"
views = "Visualizações"
widget = "Widget"
widget_settings = "Configurações dos Widgets"
would_you_like_to_try_our = "Poderia tentar a nossa "
yes_im_in = "Sim, estou dentro"
yes_not_recommended = "Sim (não recomendado)"
you_dont_have_permission_to_access_this_page = "Você não tem permissão para acessar esta página"
your_new_config_key = "Sua nova chave configurada"
your_new_value = "Seu novo valor"
your_backups = "Seus backups"
your_latest_blog_posts = "Suas postagens de blog"
your_recent_posts = "Suas postagens recentes"
by = "por"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>Dica:</u> Utilize <code>Ctrl</code>/<code>CMD</code> + <code>F</code> para buscar pela chave ou valor."
homepage = "página inicial"
instead = "no lugar"
item_class = "Insira a classe CSS do link"
item_slug = "Insira a URL do link"
now = "agora"
of = "of"
optional = "opcional"
post_your_post_slug = "/post/url-da-postagem"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>Dica profissional:</u> Você pode criar chaves customizadas e imprimir seus valores em qualquer local de seu tema."
read_more = "leia mais"
widget_key_placeholder = "Seu código"
year_month_your_post_slug = "/ano/mes/url-da-postagem"
your_key = "sua.chave"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
