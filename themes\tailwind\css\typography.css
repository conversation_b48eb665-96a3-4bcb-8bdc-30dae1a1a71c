@font-face {
  font-family:Space Grotesk;
  font-style:normal;
  font-weight:300 700;
  font-display:swap;
  src:url(../fonts/62328fecf9e80426-s.woff2) format("woff2");
  unicode-range:u+0102-0103,
  u+0110-0111,
  u+0128-0129,
  u+0168-0169,
  u+01a0-01a1,
  u+01af-01b0,
  u+0300-0301,
  u+0303-0304,
  u+0308-0309,
  u+0323,
  u+0329,
  u+1ea0-1ef9,
  u+20ab
}
@font-face {
  font-family:Space Grotesk;
  font-style:normal;
  font-weight:300 700;
  font-display:swap;
  src:url(../fonts/c7eb187887c48af6-s.woff2) format("woff2");
  unicode-range:u+0100-02ba,
  u+02bd-02c5,
  u+02c7-02cc,
  u+02ce-02d7,
  u+02dd-02ff,
  u+0304,
  u+0308,
  u+0329,
  u+1d00-1dbf,
  u+1e00-1e9f,
  u+1ef2-1eff,
  u+2020,
  u+20a0-20ab,
  u+20ad-20c0,
  u+2113,
  u+2c60-2c7f,
  u+a720-a7ff
}
@font-face {
  font-family:Space Grotesk;
  font-style:normal;
  font-weight:300 700;
  font-display:swap;
  src:url(../fonts/2d141e1a38819612-s.p.woff2) format("woff2");
  unicode-range:u+00??,
  u+0131,
  u+0152-0153,
  u+02bb-02bc,
  u+02c6,
  u+02da,
  u+02dc,
  u+0304,
  u+0308,
  u+0329,
  u+2000-206f,
  u+20ac,
  u+2122,
  u+2191,
  u+2193,
  u+2212,
  u+2215,
  u+feff,
  u+fffd
}
@font-face {
  font-family:Space Grotesk Fallback;
  src:local("Arial");
  ascent-override:89.71%;
  descent-override:26.62%;
  line-gap-override:0.00%;
  size-adjust:109.69%
}
.__className_space {
  font-family:Space Grotesk,Space Grotesk Fallback;
  font-style:normal
}
.__variable_space {
  --font-space-grotesk:"Space Grotesk","Space Grotesk Fallback"
}
:root {
  --docsearch-primary-color:#5468ff;
  --docsearch-text-color:#1c1e21;
  --docsearch-spacing:12px;
  --docsearch-icon-stroke-width:1.4;
  --docsearch-highlight-color:var(--docsearch-primary-color);
  --docsearch-muted-color:#969faf;
  --docsearch-container-background:rgba(101,108,133,.8);
  --docsearch-logo-color:#5468ff;
  --docsearch-modal-width:560px;
  --docsearch-modal-height:600px;
  --docsearch-modal-background:#f5f6f7;
  --docsearch-modal-shadow:inset 1px 1px 0 0 hsla(0,0%,100%,.5),0 3px 8px 0 #555a64;
  --docsearch-searchbox-height:56px;
  --docsearch-searchbox-background:#ebedf0;
  --docsearch-searchbox-focus-background:#fff;
  --docsearch-searchbox-shadow:inset 0 0 0 2px var(--docsearch-primary-color);
  --docsearch-hit-height:56px;
  --docsearch-hit-color:#444950;
  --docsearch-hit-active-color:#fff;
  --docsearch-hit-background:#fff;
  --docsearch-hit-shadow:0 1px 3px 0 #d4d9e1;
  --docsearch-key-gradient:linear-gradient(-225deg,#d5dbe4,#f8f8f8);
  --docsearch-key-shadow:inset 0 -2px 0 0 #cdcde6,inset 0 0 1px 1px #fff,0 1px 2px 1px rgba(30,35,90,.4);
  --docsearch-footer-height:44px;
  --docsearch-footer-background:#fff;
  --docsearch-footer-shadow:0 -1px 0 0 #e0e3e8,0 -3px 6px 0 rgba(69,98,155,.12)
}
html[data-theme=dark] {
  --docsearch-text-color:#f5f6f7;
  --docsearch-container-background:rgba(9,10,17,.8);
  --docsearch-modal-background:#15172a;
  --docsearch-modal-shadow:inset 1px 1px 0 0 #2c2e40,0 3px 8px 0 #000309;
  --docsearch-searchbox-background:#090a11;
  --docsearch-searchbox-focus-background:#000;
  --docsearch-hit-color:#bec3c9;
  --docsearch-hit-shadow:none;
  --docsearch-hit-background:#090a11;
  --docsearch-key-gradient:linear-gradient(-26.5deg,#565872,#31355b);
  --docsearch-key-shadow:inset 0 -2px 0 0 #282d55,inset 0 0 1px 1px #51577d,0 2px 2px 0 rgba(3,4,9,.3);
  --docsearch-footer-background:#1e2136;
  --docsearch-footer-shadow:inset 0 1px 0 0 rgba(73,76,106,.5),0 -4px 8px 0 rgba(0,0,0,.2);
  --docsearch-logo-color:#fff;
  --docsearch-muted-color:#7f8497
}
.DocSearch-Button {
  align-items:center;
  background:var(--docsearch-searchbox-background);
  border:0;
  border-radius:40px;
  color:var(--docsearch-muted-color);
  cursor:pointer;
  display:flex;
  font-weight:500;
  height:36px;
  justify-content:space-between;
  margin:0 0 0 16px;
  padding:0 8px;
  -webkit-user-select:none;
  -moz-user-select:none;
  user-select:none
}
.DocSearch-Button:active,
.DocSearch-Button:focus,
.DocSearch-Button:hover {
  background:var(--docsearch-searchbox-focus-background);
  box-shadow:var(--docsearch-searchbox-shadow);
  color:var(--docsearch-text-color);
  outline:none
}
.DocSearch-Button-Container {
  align-items:center;
  display:flex
}
.DocSearch-Search-Icon {
  stroke-width:1.6
}
.DocSearch-Button .DocSearch-Search-Icon {
  color:var(--docsearch-text-color)
}
.DocSearch-Button-Placeholder {
  font-size:1rem;
  padding:0 12px 0 6px
}
.DocSearch-Button-Keys {
  display:flex;
  min-width:calc(40px + .8em)
}
.DocSearch-Button-Key {
  align-items:center;
  background:var(--docsearch-key-gradient);
  border-radius:3px;
  box-shadow:var(--docsearch-key-shadow);
  color:var(--docsearch-muted-color);
  display:flex;
  height:18px;
  justify-content:center;
  margin-right:.4em;
  position:relative;
  padding:0 0 2px;
  border:0;
  top:-1px;
  width:20px
}
@media (max-width:768px) {
  .DocSearch-Button-Keys,
  .DocSearch-Button-Placeholder {
    display:none
  }
}
.DocSearch--active {
  overflow:hidden!important
}
.DocSearch-Container,
.DocSearch-Container * {
  box-sizing:border-box
}
.DocSearch-Container {
  background-color:var(--docsearch-container-background);
  height:100vh;
  left:0;
  position:fixed;
  top:0;
  width:100vw;
  z-index:200
}
.DocSearch-Container a {
  text-decoration:none
}
.DocSearch-Link {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background:none;
  border:0;
  color:var(--docsearch-highlight-color);
  cursor:pointer;
  font:inherit;
  margin:0;
  padding:0
}
.DocSearch-Modal {
  background:var(--docsearch-modal-background);
  border-radius:6px;
  box-shadow:var(--docsearch-modal-shadow);
  flex-direction:column;
  margin:60px auto auto;
  max-width:var(--docsearch-modal-width);
  position:relative
}
.DocSearch-SearchBar {
  display:flex;
  padding:var(--docsearch-spacing) var(--docsearch-spacing) 0
}
.DocSearch-Form {
  align-items:center;
  background:var(--docsearch-searchbox-focus-background);
  border-radius:4px;
  box-shadow:var(--docsearch-searchbox-shadow);
  display:flex;
  height:var(--docsearch-searchbox-height);
  margin:0;
  padding:0 var(--docsearch-spacing);
  position:relative;
  width:100%
}
.DocSearch-Input {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background:transparent;
  border:0;
  color:var(--docsearch-text-color);
  flex:1;
  font:inherit;
  font-size:1.2em;
  height:100%;
  outline:none;
  padding:0 0 0 8px;
  width:80%
}
.DocSearch-Input::-moz-placeholder {
  color:var(--docsearch-muted-color);
  opacity:1
}
.DocSearch-Input::placeholder {
  color:var(--docsearch-muted-color);
  opacity:1
}
.DocSearch-Input::-webkit-search-cancel-button,
.DocSearch-Input::-webkit-search-decoration,
.DocSearch-Input::-webkit-search-results-button,
.DocSearch-Input::-webkit-search-results-decoration {
  display:none
}
.DocSearch-LoadingIndicator,
.DocSearch-MagnifierLabel,
.DocSearch-Reset {
  margin:0;
  padding:0
}
.DocSearch-MagnifierLabel,
.DocSearch-Reset {
  align-items:center;
  color:var(--docsearch-highlight-color);
  display:flex;
  justify-content:center
}
.DocSearch-Container--Stalled .DocSearch-MagnifierLabel,
.DocSearch-LoadingIndicator {
  display:none
}
.DocSearch-Container--Stalled .DocSearch-LoadingIndicator {
  align-items:center;
  color:var(--docsearch-highlight-color);
  display:flex;
  justify-content:center
}
@media screen and (prefers-reduced-motion:reduce) {
  .DocSearch-Reset {
    animation:none;
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:none;
    background:none;
    border:0;
    border-radius:50%;
    color:var(--docsearch-icon-color);
    cursor:pointer;
    right:0;
    stroke-width:var(--docsearch-icon-stroke-width)
  }
}
.DocSearch-Reset {
  animation:fade-in .1s ease-in forwards;
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background:none;
  border:0;
  border-radius:50%;
  color:var(--docsearch-icon-color);
  cursor:pointer;
  padding:2px;
  right:0;
  stroke-width:var(--docsearch-icon-stroke-width)
}
.DocSearch-Reset[hidden] {
  display:none
}
.DocSearch-Reset:hover {
  color:var(--docsearch-highlight-color)
}
.DocSearch-LoadingIndicator svg,
.DocSearch-MagnifierLabel svg {
  height:24px;
  width:24px
}
.DocSearch-Cancel {
  display:none
}
.DocSearch-Dropdown {
  max-height:calc(var(--docsearch-modal-height) - var(--docsearch-searchbox-height) - var(--docsearch-spacing) - var(--docsearch-footer-height));
  min-height:var(--docsearch-spacing);
  overflow-y:auto;
  overflow-y:overlay;
  padding:0 var(--docsearch-spacing);
  scrollbar-color:var(--docsearch-muted-color) var(--docsearch-modal-background);
  scrollbar-width:thin
}
.DocSearch-Dropdown::-webkit-scrollbar {
  width:12px
}
.DocSearch-Dropdown::-webkit-scrollbar-track {
  background:transparent
}
.DocSearch-Dropdown::-webkit-scrollbar-thumb {
  background-color:var(--docsearch-muted-color);
  border:3px solid var(--docsearch-modal-background);
  border-radius:20px
}
.DocSearch-Dropdown ul {
  list-style:none;
  margin:0;
  padding:0
}
.DocSearch-Label {
  font-size:.75em;
  line-height:1.6em
}
.DocSearch-Help,
.DocSearch-Label {
  color:var(--docsearch-muted-color)
}
.DocSearch-Help {
  font-size:.9em;
  margin:0;
  -webkit-user-select:none;
  -moz-user-select:none;
  user-select:none
}
.DocSearch-Title {
  font-size:1.2em
}
.DocSearch-Logo a {
  display:flex
}
.DocSearch-Logo svg {
  color:var(--docsearch-logo-color);
  margin-left:8px
}
.DocSearch-Hits:last-of-type {
  margin-bottom:24px
}
.DocSearch-Hits mark {
  background:none;
  color:var(--docsearch-highlight-color)
}
.DocSearch-HitsFooter {
  color:var(--docsearch-muted-color);
  display:flex;
  font-size:.85em;
  justify-content:center;
  margin-bottom:var(--docsearch-spacing);
  padding:var(--docsearch-spacing)
}
.DocSearch-HitsFooter a {
  border-bottom:1px solid;
  color:inherit
}
.DocSearch-Hit {
  border-radius:4px;
  display:flex;
  padding-bottom:4px;
  position:relative
}
@media screen and (prefers-reduced-motion:reduce) {
  .DocSearch-Hit--deleting {
    transition:none
  }
}
.DocSearch-Hit--deleting {
  opacity:0;
  transition:all .25s linear
}
@media screen and (prefers-reduced-motion:reduce) {
  .DocSearch-Hit--favoriting {
    transition:none
  }
}
.DocSearch-Hit--favoriting {
  transform:scale(0);
  transform-origin:top center;
  transition:all .25s linear;
  transition-delay:.25s
}
.DocSearch-Hit a {
  background:var(--docsearch-hit-background);
  border-radius:4px;
  box-shadow:var(--docsearch-hit-shadow);
  display:block;
  padding-left:var(--docsearch-spacing);
  width:100%
}
.DocSearch-Hit-source {
  background:var(--docsearch-modal-background);
  color:var(--docsearch-highlight-color);
  font-size:.85em;
  font-weight:600;
  line-height:32px;
  margin:0 -4px;
  padding:8px 4px 0;
  position:sticky;
  top:0;
  z-index:10
}
.DocSearch-Hit-Tree {
  color:var(--docsearch-muted-color);
  height:var(--docsearch-hit-height);
  opacity:.5;
  stroke-width:var(--docsearch-icon-stroke-width);
  width:24px
}
.DocSearch-Hit[aria-selected=true] a {
  background-color:var(--docsearch-highlight-color)
}
.DocSearch-Hit[aria-selected=true] mark {
  text-decoration:underline
}
.DocSearch-Hit-Container {
  align-items:center;
  color:var(--docsearch-hit-color);
  display:flex;
  flex-direction:row;
  height:var(--docsearch-hit-height);
  padding:0 var(--docsearch-spacing) 0 0
}
.DocSearch-Hit-icon {
  height:20px;
  width:20px
}
.DocSearch-Hit-action,
.DocSearch-Hit-icon {
  color:var(--docsearch-muted-color);
  stroke-width:var(--docsearch-icon-stroke-width)
}
.DocSearch-Hit-action {
  align-items:center;
  display:flex;
  height:22px;
  width:22px
}
.DocSearch-Hit-action svg {
  display:block;
  height:18px;
  width:18px
}
.DocSearch-Hit-action+.DocSearch-Hit-action {
  margin-left:6px
}
.DocSearch-Hit-action-button {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background:none;
  border:0;
  border-radius:50%;
  color:inherit;
  cursor:pointer;
  padding:2px
}
svg.DocSearch-Hit-Select-Icon {
  display:none
}
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-Select-Icon {
  display:block
}
.DocSearch-Hit-action-button:focus,
.DocSearch-Hit-action-button:hover {
  background:rgba(0,0,0,.2);
  transition:background-color .1s ease-in
}
@media screen and (prefers-reduced-motion:reduce) {
  .DocSearch-Hit-action-button:focus,
  .DocSearch-Hit-action-button:hover {
    transition:none
  }
}
.DocSearch-Hit-action-button:focus path,
.DocSearch-Hit-action-button:hover path {
  fill:#fff
}
.DocSearch-Hit-content-wrapper {
  display:flex;
  flex:1 1 auto;
  flex-direction:column;
  font-weight:500;
  justify-content:center;
  line-height:1.2em;
  margin:0 8px;
  overflow-x:hidden;
  position:relative;
  text-overflow:ellipsis;
  white-space:nowrap;
  width:80%
}
.DocSearch-Hit-title {
  font-size:.9em
}
.DocSearch-Hit-path {
  color:var(--docsearch-muted-color);
  font-size:.75em
}
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-Tree,
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-action,
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-icon,
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-path,
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-text,
.DocSearch-Hit[aria-selected=true] .DocSearch-Hit-title,
.DocSearch-Hit[aria-selected=true] mark {
  color:var(--docsearch-hit-active-color)!important
}
@media screen and (prefers-reduced-motion:reduce) {
  .DocSearch-Hit-action-button:focus,
  .DocSearch-Hit-action-button:hover {
    background:rgba(0,0,0,.2);
    transition:none
  }
}
.DocSearch-ErrorScreen,
.DocSearch-NoResults,
.DocSearch-StartScreen {
  font-size:.9em;
  margin:0 auto;
  padding:36px 0;
  text-align:center;
  width:80%
}
.DocSearch-Screen-Icon {
  color:var(--docsearch-muted-color);
  padding-bottom:12px
}
.DocSearch-NoResults-Prefill-List {
  display:inline-block;
  padding-bottom:24px;
  text-align:left
}
.DocSearch-NoResults-Prefill-List ul {
  display:inline-block;
  padding:8px 0 0
}
.DocSearch-NoResults-Prefill-List li {
  list-style-position:inside;
  list-style-type:"» "
}
.DocSearch-Prefill {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background:none;
  border:0;
  border-radius:1em;
  color:var(--docsearch-highlight-color);
  cursor:pointer;
  display:inline-block;
  font-size:1em;
  font-weight:700;
  padding:0
}
.DocSearch-Prefill:focus,
.DocSearch-Prefill:hover {
  outline:none;
  text-decoration:underline
}
.DocSearch-Footer {
  align-items:center;
  background:var(--docsearch-footer-background);
  border-radius:0 0 8px 8px;
  box-shadow:var(--docsearch-footer-shadow);
  display:flex;
  flex-direction:row-reverse;
  flex-shrink:0;
  height:var(--docsearch-footer-height);
  justify-content:space-between;
  padding:0 var(--docsearch-spacing);
  position:relative;
  -webkit-user-select:none;
  -moz-user-select:none;
  user-select:none;
  width:100%;
  z-index:300
}
.DocSearch-Commands {
  color:var(--docsearch-muted-color);
  display:flex;
  list-style:none;
  margin:0;
  padding:0
}
.DocSearch-Commands li {
  align-items:center;
  display:flex
}
.DocSearch-Commands li:not(:last-of-type) {
  margin-right:.8em
}
.DocSearch-Commands-Key {
  align-items:center;
  background:var(--docsearch-key-gradient);
  border-radius:2px;
  box-shadow:var(--docsearch-key-shadow);
  display:flex;
  height:18px;
  justify-content:center;
  margin-right:.4em;
  padding:0 0 1px;
  color:var(--docsearch-muted-color);
  border:0;
  width:20px
}
@media (max-width:768px) {
  :root {
    --docsearch-spacing:10px;
    --docsearch-footer-height:40px
  }
  .DocSearch-Dropdown {
    height:100%
  }
  .DocSearch-Container {
    height:100vh;
    height:-webkit-fill-available;
    height:calc(var(--docsearch-vh, 1vh) * 100);
    position:absolute
  }
  .DocSearch-Footer {
    border-radius:0;
    bottom:0;
    position:absolute
  }
  .DocSearch-Hit-content-wrapper {
    display:flex;
    position:relative;
    width:80%
  }
  .DocSearch-Modal {
    border-radius:0;
    box-shadow:none;
    height:100vh;
    height:-webkit-fill-available;
    height:calc(var(--docsearch-vh, 1vh) * 100);
    margin:0;
    max-width:100%;
    width:100%
  }
  .DocSearch-Dropdown {
    max-height:calc(var(--docsearch-vh, 1vh) * 100 - var(--docsearch-searchbox-height) - var(--docsearch-spacing) - var(--docsearch-footer-height))
  }
  .DocSearch-Cancel {
    -webkit-appearance:none;
    -moz-appearance:none;
    appearance:none;
    background:none;
    border:0;
    color:var(--docsearch-highlight-color);
    cursor:pointer;
    display:inline-block;
    flex:none;
    font:inherit;
    font-size:1em;
    font-weight:500;
    margin-left:var(--docsearch-spacing);
    outline:none;
    overflow:hidden;
    padding:0;
    -webkit-user-select:none;
    -moz-user-select:none;
    user-select:none;
    white-space:nowrap
  }
  .DocSearch-Commands,
  .DocSearch-Hit-Tree {
    display:none
  }
}
@keyframes fade-in {
  0% {
    opacity:0
  }
  to {
    opacity:1
  }
}
.light .DocSearch {
  --docsearch-primary-color:#db2777;
  --docsearch-highlight-color:#db2777;
  --docsearch-searchbox-shadow:inset 0 0 0 2px #db2777;
  --docsearch-muted-color:#6b7280;
  --docsearch-container-background:rgba(156,163,175,.8);
  --docsearch-modal-background:#e5e7eb;
  --docsearch-searchbox-background:#f3f4f6;
  --docsearch-searchbox-focus-background:#f3f4f6;
  --docsearch-hit-color:#374151;
  --docsearch-hit-shadow:none;
  --docsearch-hit-active-color:#1f2937;
  --docsearch-hit-background:#f3f4f6;
  --docsearch-footer-background:#f3f4f6
}
.dark .DocSearch {
  --docsearch-primary-color:#db2777;
  --docsearch-highlight-color:#db2777;
  --docsearch-searchbox-shadow:inset 0 0 0 2px #db2777;
  --docsearch-text-color:#d1d5db;
  --docsearch-muted-color:#9ca3af;
  --docsearch-container-background:rgba(17,24,39,.8);
  --docsearch-modal-background:#111827;
  --docsearch-modal-shadow:inset 1px 1px 0 0 #2c2e40,0 3px 8px 0 #000309;
  --docsearch-searchbox-background:#1f2937;
  --docsearch-searchbox-focus-background:#1f2937;
  --docsearch-hit-color:#e5e7eb;
  --docsearch-hit-shadow:none;
  --docsearch-hit-active-color:#f3f4f6;
  --docsearch-hit-background:#1f2937;
  --docsearch-footer-background:#111827;
  --docsearch-footer-shadow:inset 0 1px 0 0 rgba(73,76,106,.5),0 -4px 8px 0 rgba(0,0,0,.2);
  --docsearch-key-gradient:linear-gradient(-26.5deg,#1f2937,#111827);
  --docsearch-key-shadow:inset 0 -2px 0 0 #282d55,inset 0 0 1px 1px #51577d,0 2px 2px 0 rgba(3,4,9,.3);
  --docsearch-logo-color:#d1d5db
}
.dark .DocSearch-Input,
.dark .DocSearch-Input:focus,
.light .DocSearch-Input,
.light .DocSearch-Input:focus {
  box-shadow:0 0 #0000;
  background:transparent
}
@media (prefers-color-scheme:dark) {
  .markdown-alert {
    --color-border-default:#30363d;
    --color-accent-fg:#58a6ff;
    --color-accent-emphasis:#1f6feb;
    --color-danger-fg:#f85149;
    --color-danger-emphasis:#da3633;
    --color-attention-fg:#d29922;
    --color-attention-emphasis:#9e6a03;
    --color-done-fg:#a371f7;
    --color-done-emphasis:#8957e5;
    --color-success-fg:#3fb950;
    --color-success-emphasis:#238636
  }
}
@media (prefers-color-scheme:light) {
  .markdown-alert {
    --color-border-default:#d0d7de;
    --color-accent-fg:#0969da;
    --color-accent-emphasis:#0969da;
    --color-danger-fg:#d1242f;
    --color-danger-emphasis:#cf222e;
    --color-attention-fg:#9a6700;
    --color-attention-emphasis:#9a6700;
    --color-done-fg:#8250df;
    --color-done-emphasis:#8250df;
    --color-success-fg:#1a7f37;
    --color-success-emphasis:#1f883d
  }
}
.markdown-alert {
  border-left:.25em solid var(--borderColor-default,var(--color-border-default));
  color:inherit;
  margin-bottom:16px;
  padding:.5rem 1em
}
.markdown-alert>:last-child {
  margin-bottom:0!important
}
.markdown-alert .markdown-alert-title {
  align-items:center;
  display:flex;
  font-size:14px;
  font-weight:500;
  line-height:1
}
.markdown-alert .markdown-alert-title svg.octicon {
  margin-right:8px!important;
  margin-right:var(--base-size-8,8px)!important;
  fill:currentColor
}
.markdown-alert.markdown-alert-note {
  border-left-color:var(--borderColor-accent-emphasis,var(--color-accent-emphasis))
}
.markdown-alert.markdown-alert-note .markdown-alert-title {
  color:var(--color-accent-fg);
  color:var(--fgColor-accent,var(--color-accent-fg))
}
.markdown-alert.markdown-alert-tip {
  border-left-color:var(--borderColor-success-emphasis,var(--color-success-emphasis))
}
.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color:var(--color-success-fg);
  color:var(--fgColor-success,var(--color-success-fg))
}
.markdown-alert.markdown-alert-important {
  border-left-color:var(--borderColor-done-emphasis,var(--color-done-emphasis))
}
.markdown-alert.markdown-alert-important .markdown-alert-title {
  color:var(--color-done-fg);
  color:var(--fgColor-done,var(--color-done-fg))
}
.markdown-alert.markdown-alert-warning {
  border-left-color:var(--borderColor-attention-emphasis,var(--color-attention-emphasis))
}
.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color:var(--color-attention-fg);
  color:var(--fgColor-attention,var(--color-attention-fg))
}
.markdown-alert.markdown-alert-caution {
  border-left-color:var(--borderColor-danger-emphasis,var(--color-danger-emphasis))
}
.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color:var(--color-danger-fg);
  color:var(--fgColor-danger,var(--color-danger-fg))
}
