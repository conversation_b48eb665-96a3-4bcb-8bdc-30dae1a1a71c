about = "О сайте"
add_category = "Добавить категорию"
add_content = "Добавить статью"
add_link = "Добавить ссылку"
add_menu = "Добавить меню"
add_new_page = "Добавить новую страницу"
add_new_post = "Добавить новую статью"
add_source_link_optional = "Добавить ссылку на источник (не обязательно)"
add_sub = "Добавить подстраницу"
address_url = "Адрес (URL)"
admin = "Админ"
admin_panel_style_based_on = "Стиль админ панели основан на"
all_blog_posts = "Все статьи"
all_cache_has_been_deleted = "Все кеши были очищены!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Архивы"
are_you_sure_you_want_to_delete_ = "Вы уверены, что хотите удалить <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Сейчас вы используете автоматически созданное меню"
audio_post = "Статья с аудио"
audio_post_comment = "Создать статью с аудио"
author = "Автор"
author_description = "Еще один пользователь HTMLy"
back_to = "Вернуться на"
backup = "Бэкап"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Расскажите больше о своем блоге (один абзац)"
blog_theme = "Тема блога"
blog_title = "Заголовок блога"
blog_title_placeholder = "Мой блог на HTMLy"
blog_posts_displayed_as = "Записи в блоге отображаются в виде"
breadcrumb_home_text = "Текст для главной страницы в хлебных крошках"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Используя данный импортер, вы подтверждаете, что лента принадлежит вам, или у вас есть полномочия на ее публикацию"
css_class_optional = "CSS класс (не обязательно)"
cache_expiration = "Срок действия кеша (в часах)"
cache_off = "Кеш выключен"
cache_timestamp = "Отметка времени кеша"
cancel = "Отменить"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha не верна"
categories = "Категории"
category = "Категория"
check_update = "Проверить обновления"
clear_cache = "Очистить кеш"
comma_separated_values = "Разделенные запятыми значения"
comment_system = "Выберите систему"
comments = "Комментарии"
config = "Конфигурация"
congrats_you_have_the_latest_version_of_htmly = "Поздравляем! У вас установлена последняя версия HTMLy"
content = "Содержимое"
contents = "Содержание"
copyright_line = "Авторские права"
copyright_line_placeholder = "(c) Ваше имя"
create_backup = "Создать резервную копию"
created = "Создано"
custom = "Пользовательские"
custom_settings = "Пользовательские настройки"
dashboard = "Панель"
date = "Дата"
date_format = "Формат даты"
delete = "Удалить"
description = "Описание"
disable = "Выключить"
disabled = "Отключено"
disqus_shortname = "Имя Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Черновик"
edit = "Редактировать"
edit_category = "Редактировать категорию"
edit_post = "Редактировать"
edit_profile = "Редактировать профиль"
enable = "Включить"
enable_blog_url = "Включить ссылку blog"
enter_image_url = "Введите URL изображения"
facebook_app_id = "Facebook App ID"
facebook_page = "Страница Facebook"
featured_audio = "Аудио"
featured_image = "Изображение"
featured_link = "Ссылка"
featured_quote = "Цитата"
featured_video = "Видео"
feed_url = "Адрес рассылки"
filename = "Имя файла"
follow = "Следовать"
for_google_site_verification_meta = "Для google-site-verification (мета тег)"
for_msvalidate_01_meta = "Для msvalidate.01 (мета тег)"
front_page_displays = "Показывать на главной странице"
full_post = "Статья полностью"
general = "Общие"
general_settings = "Общие настройки"
get_one_here = "Получить"
github_pre_release = "Предварительный выпуск на Github"
google_analytics = "Аналитика Google"
google_analytics_legacy = "Аналитика Google (legacy)"
google_search_console = "Поисковая консоль Google"
home = "Главная"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Если оставить пустым, данные будут внесены из основного содержимого"
if_the_url_is_left_empty_we_will_use_the_page_title = "Если адрес оставить пустым, будет использован заголовок страницы"
if_the_url_is_left_empty_we_will_use_the_post_title = "Если адрес оставить пустым, будет использован заголовок статьи"
image_post = "Статья с изображением"
image_post_comment = "Создать статью с изображением"
import = "Импорт"
import_feed = "Запустить импорт ленты"
import_rss = "Импорт RSS"
import_rss_feed_2.0 = "Импорт RSS Feed 2.0"
insert_image = "Вставить изображение"
invalid_error = "ОШИБКА: Неверные имя пользователя или Пароль"
language = "Язык системы"
link_name = "Название ссылки"
link_post = "Статья со ссылкой"
link_post_comment = "Создать статью со ссылкой"
login = "Вход"
login_page = "Страница входа"
logout = "Выйти"
menu = "Меню"
menus = "Редактор меню"
meta_description = "Мета-описание"
meta_description_character = "Количество символов в мета-описании"
metatags = "Мета теги"
metatags_settings = "Настройки мета тегов"
mine = "Моё"
more = "Подробнее"
my_draft = "Мой черновик"
my_posts = "Мои статьи"
name = "Имя"
newer = "Новое"
next = "Вперед"
next_post = "Следующая статья"
no_available_backup = "Резервные копии отсутствуют"
no_draft_found = "Черновики не найдены"
no_newer_posts = "Нет новых статей"
no_posts_found = "Статьи не найдены"
no_related_post_found = "Нет похожих статей."
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "Безрезультатно"
nope = "Нет"
not = "Нет"
older = "Старое"
only = "Только"
operations = "Операции"
page = "Страница"
page_generation_time = "Время создания страницы"
pages = "Страницы"
pass_error = "Введите пароль"
password = "Пароль"
performance = "Производительность"
performance_settings = "Настройки производительности"
permalink = "Постоянная ссылка"
popular = "Популярный"
popular_posts = "Популярные статьи"
popular_posts_widget = "Популярные статьи"
popular_posts_widget_at_most = "Количество популярных статей, не более"
popular_tags = "Популярные теги"
post_by_author = "Авторские статьи"
posted_in = "Опубликовано в"
posted_on = "Опубликовано "
posts = "Статьи"
posts_by = "Статьи автора"
posts_draft = "Черновики"
posts_in_archive_page_at_most = "Количество статей на архивных страницах, не более"
posts_in_category_page_at_most = "Количество статей на странице категорий, не более"
posts_in_front_page_show_at_most = "Количество статей на главной, не более"
posts_in_profile_page_at_most = "Количество статей на страницах профиля пользователя, не более"
posts_in_search_result_at_most = "Количество статей в результатах поиска, не более"
posts_in_tag_page_at_most = "Количество статей на страницах для каждой метки, не более"
posts_in_type_page_at_most = "Количество статей на страницах для каждого типа записей, не более"
posts_index_settings = "Настройки количества статей"
posts_list = "Список статей"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "предварительный выпуск"
prev = "Назад"
prev_post = "Предыдущая статья"
preview = "Просмотр"
profile_for = "Profile for"
proudly_powered_by = "Разработано"
publish = "Опубликовать"
publish_draft = "Опубликовать черновик"
published = "Опубликовано"
quote_post = "Статья с цитатой"
quote_post_comment = "Создать статью с цитатой"
rss_character = "Количество символов в RSS"
rss_feeds_show_the_most_recent = "Количество последних новостей в RSS каналах"
rss_settings = "Настройки RSS"
read_more_text = "Текст Читать далее"
read_more_text_placeholder = "Подробнее"
reading = "Чтение"
reading_settings = "Настройки чтения"
recaptcha = "reCAPTCHA"
recent_posts = "Новые статьи"
recent_posts_widget_at_most = "Количество новых статей, не более"
regular_post = "Стандартная статья"
regular_post_comment = "Создать стандартную статью"
related_posts = "Похожие статьи"
related_widget_posts_at_most = "Количество похожих статей, не более"
revert_to_draft = "Переключить на черновик"
save = "Сохранить"
save_config = "Сохранить настройки"
save_edit = "Сохранить"
save_menu = "Сохранить меню"
save_as_draft = "Сохранить как черновик"
save_category = "Сохранить категорию"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Поиск"
search_for = "Искать"
search_results_for = "Search results for"
search_results_not_found = "По вашему запросу ничего не найдено!"
secret_key = "Секретный ключ"
settings = "Настройки"
sign_in_to_start_your_session = "Авторизация пользователя"
site_key = "Ключ сайта"
sitemap = "Карта сайта"
slug = "ЧПУ"
social_media = "Социальные медиа"
static_page = "Статичная страница"
static_page_comment = "Создать статичную страницу"
static_pages = "Статичные страницы"
summary = "Отрывок"
summary_character = "Количество символов в отрывке"
tag = "Метка"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Подзаголовок"
tagline_placeholder = "Платформа для ведения блогов на PHP без базы данных"
tagline_description = "В нескольких словах расскажите, о чем этот блог"
tags = "Метки"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Это устаревший код. Обычно новая аналитика создается с использованием gtag.js"
this_page_doesnt_exist = "Такая станица не существует!"
time = "Время"
timezone = "Часовой пояс"
title = "Заголовок"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Для использования комментариев Disqus или Facebook вам необходимо указать короткое имя Disqus или Facebook App ID."
token_error = "Неправильный токен"
tools = "Инструменты"
twitter_account = "Аккаунт Twitter"
type_to_search = "Введите для поиска"
uncategorized = "Без категории"
uncategorized_comment = "Статьи, которым не нужна категория или которые не подходят ни к одной существующей категории."
universal_analytics = "Универсальная (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Обновить"
update_available = "Доступно обновление"
update_draft = "Обновить черновик"
update_post = "Обновить статью"
update_to = "Обновить до"
upload = "Загрузить"
user = "Пользователь"
user_error = "Заполните имя пользователя"
valid_values_range_from_0_to_1.0._see = "Допустимые значения от 0.0 до 1.0. Подробнее: "
video_post = "Статья с видео"
video_post_comment = "Создать статью с видео"
view = "Просмотр"
view_post = "Просмотр"
views = "Просмотры"
widget = "Виджет"
widget_settings = "Управление виджетами"
would_you_like_to_try_our = "Хотели бы вы попробовать "
yes_im_in = "Да"
yes_not_recommended = "Да (не рекомендуется)"
you_dont_have_permission_to_access_this_page = "У вас нет разрешений для доступа к этой странице"
your_new_config_key = "Ваш новый ключ конфигурации"
your_new_value = "Значение ключа"
your_backups = "Резервные копии"
your_latest_blog_posts = "Ваши последние статьи"
your_recent_posts = "Ваши недавние статьи"
by = ""
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>совет:</u> Для поиска ключа конфигурации и его значения используйте комбинацию клавиш <code>Ctrl</code>/<code>CMD</code> + <code>F</code>"
homepage = "главная"
instead = "вместо"
item_class = "Добавьте класс CSS"
item_slug = "Вставьте ЧП ссылку"
now = "сейчас"
of = "of"
optional = "необязательный"
post_your_post_slug = "/post/постоянная-ссылка-на-статью"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>совет:</u> Вы можете создать собственный ключ конфигурации и выводить значение этого ключа в любом месте вашего шаблона"
read_more = "Читать далее"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/год/месяц/постоянная-ссылка-на-статью"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
