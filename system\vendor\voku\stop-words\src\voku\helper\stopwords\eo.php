<?php

// esperanto

static $data = [
    'adiaŭ',
    'ajn',
    'al',
    'ankoraŭ',
    'antaŭ',
    'aŭ',
    'bonan',
    'bonvole',
    'bonvolu',
    'bv',
    'ci',
    'cia',
    'cian',
    'cin',
    'd-ro',
    'da',
    'de',
    'dek',
    'deka',
    'do',
    'doktor\'',
    'doktoro',
    'du',
    'dua',
    'dum',
    'eble',
    'ekz',
    'ekze<PERSON>',
    'en',
    'estas',
    'estis',
    'estos',
    'estu',
    'estus',
    'eĉ',
    'f-no',
    'feliĉan',
    'for',
    'fraŭlino',
    'ha',
    'havas',
    'havis',
    'havos',
    'havu',
    'havus',
    'he',
    'ho',
    'hu',
    'ili',
    'ilia',
    'ilian',
    'ilin',
    'inter',
    'io',
    'ion',
    'iu',
    'iujn',
    'iun',
    'ja',
    'jam',
    'je',
    'jes',
    'k',
    'kaj',
    'ke',
    'kio',
    'kion',
    'kiu',
    'kiujn',
    'kiun',
    'kvankam',
    'kvar',
    'kvara',
    'kvazaŭ',
    'kvin',
    'kvina',
    'la',
    'li',
    'lia',
    'lian',
    'lin',
    'malantaŭ',
    'male',
    'malgraŭ',
    'mem',
    'mi',
    'mia',
    'mian',
    'min',
    'minus',
    'naŭ',
    'naŭa',
    'ne',
    'nek',
    'nenio',
    'nenion',
    'neniu',
    'neniun',
    'nepre',
    'ni',
    'nia',
    'nian',
    'nin',
    'nu',
    'nun',
    'nur',
    'ok',
    'oka',
    'oni',
    'onia',
    'onian',
    'onin',
    'plej',
    'pli',
    'plu',
    'plus',
    'por',
    'post',
    'preter',
    's-no',
    's-ro',
    'se',
    'sed',
    'sep',
    'sepa',
    'ses',
    'sesa',
    'si',
    'sia',
    'sian',
    'sin',
    'sinjor\'',
    'sinjorino',
    'sinjoro',
    'sub',
    'super',
    'supren',
    'sur',
    'tamen',
    'tio',
    'tion',
    'tiu',
    'tiujn',
    'tiun',
    'tra',
    'tri',
    'tria',
    'tuj',
    'tute',
    'unu',
    'unua',
    've',
    'verŝajne',
    'vi',
    'via',
    'vian',
    'vin',
    'ĉi',
    'ĉio',
    'ĉion',
    'ĉiu',
    'ĉiujn',
    'ĉiun',
    'ĉu',
    'ĝi',
    'ĝia',
    'ĝian',
    'ĝin',
    'ĝis',
    'ĵus',
    'ŝi',
    'ŝia',
    'ŝin',
];

$result =& $data;
unset($data);
return $result;
