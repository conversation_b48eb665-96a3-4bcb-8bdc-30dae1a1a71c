{"name": "voku/portable-ascii", "description": "Portable ASCII library - performance optimized (ascii) string functions for php.", "type": "library", "keywords": ["clean", "php", "ascii"], "homepage": "https://github.com/voku/portable-ascii", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "http://www.moelleken.org/"}], "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": "~6.0 || ~7.0 || ~9.0"}, "suggest": {"ext-intl": "Use Intl for transliterator_transliterate() support"}, "autoload": {"psr-4": {"voku\\": "src/voku/"}}, "autoload-dev": {"psr-4": {"voku\\tests\\": "tests/"}}}