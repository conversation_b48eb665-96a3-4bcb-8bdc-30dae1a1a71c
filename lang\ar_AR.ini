about = "حول"
add_category = "إضافة فئة"
add_content = "إضافة محتوى"
add_link = "إضافة ارتباط"
add_menu = "إضافة قائمة"
add_new_page = "إضافة صفحة جديدة"
add_new_post = "إضافة منشور جديد"
add_source_link_optional = "إضافة رابط المصدر (اختياري)"
add_sub = "إضافة صفحة فرعية"
address_url = "العنوان (URL)"
admin = "المسؤول"
admin_panel_style_based_on = "نمط لوحة المشرف يعتمد على"
all_blog_posts = "كافة منشورات المدونة"
all_cache_has_been_deleted = "تم حذف كل ذاكرة التخزين المؤقت!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "أرشيفات"
are_you_sure_you_want_to_delete_ = "هل أنت متأكد أنك تريد حذف <strong>٪ s </strong>؟"
at_the_moment_you_are_using_auto_generated_menu = "في الوقت الحالي أنت تستخدم قائمة تم إنشاؤها تلقائيًا."
audio_post = "مشاركة صوتية"
audio_post_comment = "إنشاء منشور مدونة بصوت مميز"
author = "المؤلف"
author_description = "Just another HTMLy user"
back_to = "رجوع إلى"
backup = "النسخ الاحتياطي"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "في فقرة واحدة ، أخبرنا بالمزيد عن مدونتك."
blog_theme = "سمة المدونة"
blog_title = "عنوان المدونة"
blog_title_placeholder = "مدونة HTMLy الخاصة بي"
blog_posts_displayed_as = "تم عرض مشاركات المدونة على شكل"
breadcrumb_home_text = "نص الصفحة الرئيسية لمسار التنقل"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "باستخدام هذا المستورد ، فأنت توافق على ما إذا كانت الخلاصة ملكك أو على الأقل لديك الصلاحية لنشرها."
css_class_optional = "فئة CSS (اختياري)"
cache_expiration = "انتهاء صلاحية ذاكرة التخزين المؤقت (بالساعات)"
cache_off = "إيقاف ذاكرة التخزين المؤقت"
cache_timestamp = "الطابع الزمني لذاكرة التخزين المؤقت"
cancel = "إلغاء"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "فئات"
category = "فئة"
check_update = "تحقق من التحديث"
clear_cache = "مسح ذاكرة التخزين المؤقت"
comma_separated_values = "قيم مفصولة بفواصل"
comment_system = "نظام التعليق"
comments = "تعليقات"
config = "التكوين"
congrats_you_have_the_latest_version_of_htmly = "تهانينا! لديك أحدث إصدار من HTMLy."
content = "المحتوى"
contents = "المحتويات"
copyright_line = "خط حقوق النشر"
copyright_line_placeholder = "(ج) اسمك."
create_backup = "إنشاء نسخة احتياطية"
created = "تم إنشاؤه"
custom = "مخصص"
custom_settings = "إعدادات مخصصة"
dashboard = "لوحة المعلومات"
date = "التاريخ"
date_format = "تنسيق التاريخ"
delete = "حذف"
description = "الوصف"
disable = "تعطيل"
disabled = "معطل"
disqus_shortname = "اسم قصير لمناقصة"
disqus_shortname_placeholder = "htmly"
draft = "مسودة"
edit = "تحرير"
edit_category = "تحرير فئة"
edit_post = "Edit"
edit_profile = "تحرير ملف التعريف"
enable = "تمكين"
enable_blog_url = "تمكين عنوان URL للمدونة"
enter_image_url = "أدخل عنوان URL للصورة"
facebook_app_id = "معرف تطبيق Facebook"
facebook_page = "صفحة Facebook"
featured_audio = "صوت مميز"
featured_image = "صورة مميزة"
featured_link = "رابط مميز"
featured_quote = "اقتباس مميز"
featured_video = "فيديو مميز"
feed_url = "موجز URL"
filename = "اسم الملف"
follow = "متابعة"
for_google_site_verification_meta = "بالنسبة إلى تعريف موقع google-site-verification"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "الصفحة الأولى تعرض"
full_post = "مشاركة كاملة"
general = "عام"
general_settings = "إعدادات عامة"
get_one_here = "احصل على واحد هنا"
github_pre_release = "الإصدار التجريبي من Github"
google_analytics = "تحليلات Google"
google_analytics_legacy = "Google Analytics (قديم)"
google_search_console = "Google Search Console"
home = "الصفحة الرئيسية"
if_left_empty_we_will_excerpt_it_from_the_content_below = "إذا تركت فارغة ، فسنقتطفها من المحتوى أدناه"
if_the_url_is_left_empty_we_will_use_the_page_title = "إذا ترك عنوان url فارغًا ، فسنستخدم عنوان الصفحة"
if_the_url_is_left_empty_we_will_use_the_post_title = "إذا ترك عنوان url فارغًا ، فسنستخدم عنوان المنشور"
image_post = "مشاركة صورة"
image_post_comment = "إنشاء منشور مدونة بصورة مميزة"
import = "استيراد"
import_feed = "بدء استيراد موجز ويب"
import_rss = "استيراد RSS"
import_rss_feed_2.0 = "استيراد موجز RSS 2.0"
insert_image = "إدراج صورة"
invalid_error = "ERROR: Invalid username or password"
language = "لغة النظام"
link_name = "اسم الرابط"
link_post = "رابط المنشور"
link_post_comment = "إنشاء منشور مدونة مع ارتباط مميز"
login = "تسجيل الدخول"
login_page = "صفحة تسجيل الدخول"
logout = "تسجيل الخروج"
menu = "Menu"
menus = "محرر القائمة"
meta_description = "وصف التعريف"
meta_description_character = "حرف وصف التعريف"
metatags = "العلامات الوصفية"
metatags_settings = "إعدادات العلامات الوصفية"
mine = "ملكي"
more = "المزيد"
my_draft = "مسودتي"
my_posts = "مشاركاتي"
name = "الاسم"
newer = "أحدث"
next = "التالي"
next_post = "المنشور التالي"
no_available_backup = "لا يوجد نسخ احتياطي متوفر في الوقت الحالي."
no_draft_found = "لم يتم العثور على مسودة"
no_posts_found = "لم يتم العثور على أية مشاركات"
no_related_post_found = "لم يتم العثور على منشور ذي صلة"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "لا"
not = "لا"
older = "أقدم"
only = "فقط"
operations = "العمليات"
page = "Page"
page_generation_time = "وقت إنشاء الصفحة"
pages = "Pages"
pass_error = "Password field is required"
password = "كلمة المرور"
performance = "أداء"
performance_settings = "إعدادات الأداء"
permalink = "الرابط الثابت"
popular = "Popular"
popular_posts = "المشاركات الشائعة"
popular_posts_widget = "أداة المشاركات الشائعة"
popular_posts_widget_at_most = "أداة المشاركات الشائعة على الأكثر"
popular_tags = "العلامات الشائعة"
post_by_author = "Posts by this author"
posted_in = "تم النشر في"
posted_on = "تم النشر في"
posts = "المشاركات"
posts_by = "Posts by"
posts_draft = "مسودة المشاركات"
posts_in_archive_page_at_most = "المشاركات في صفحة الأرشيف على الأكثر"
posts_in_category_page_at_most = "المشاركات في صفحة الفئات على الأكثر"
posts_in_front_page_show_at_most = "تظهر المشاركات في الصفحة الأولى على الأكثر"
posts_in_profile_page_at_most = "المشاركات في صفحة الملف الشخصي على الأكثر"
posts_in_search_result_at_most = "المشاركات في نتائج البحث على الأكثر"
posts_in_tag_page_at_most = "المشاركات في صفحة العلامة على الأكثر"
posts_in_type_page_at_most = "المشاركات في صفحة النوع على الأكثر"
posts_index_settings = "إعدادات فهرس المشاركات"
posts_list = "قائمة المشاركات"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "الإصدار التجريبي"
prev = "قديم"
prev_post = "المنشور السابق"
preview = "معاينة"
profile_for = "Profile for"
proudly_powered_by = "مدعوم بفخر بواسطة"
publish = "نشر"
publish_draft = "نشر المسودة"
published = "منشور"
quote_post = "مشاركة مقتبسة"
quote_post_comment = "إنشاء منشور مدونة باقتباس مميز"
rss_character = "شخصية RSS"
rss_feeds_show_the_most_recent = "تظهر موجزات RSS الأحدث"
rss_settings = "إعدادات RSS"
read_more_text = "قراءة المزيد من النص"
read_more_text_placeholder = "قراءة المزيد"
reading = "جارٍ القراءة"
reading_settings = "إعدادات القراءة"
recaptcha = "reCAPTCHA"
recent_posts = "المشاركات الأخيرة"
recent_posts_widget_at_most = "أداة المشاركات الأخيرة على الأكثر"
regular_post = "مشاركة عادية"
regular_post_comment = "إنشاء منشور مدونة عادي"
related_posts = "منشورات ذات صلة"
related_widget_posts_at_most = "منشورات الأدوات ذات الصلة على الأكثر"
revert_to_draft = "عودة إلى المسودة"
save = "حفظ"
save_config = "حفظ التكوين"
save_edit = "حفظ التحرير"
save_menu = "حفظ القائمة"
save_as_draft = "حفظ كمسودة"
save_category = "حفظ فئة"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "بحث"
search_for = "بحث عن"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "المفتاح السري"
settings = "الإعدادات"
sign_in_to_start_your_session = "قم بتسجيل الدخول لبدء جلستك"
site_key = "مفتاح الموقع"
sitemap = "خريطة الموقع"
slug = "سبيكة"
social_media = "وسائل التواصل الاجتماعي"
static_page = "صفحة ثابتة"
static_page_comment = "إنشاء صفحة ثابتة"
static_pages = "صفحات ثابتة"
summary = "ملخص"
summary_character = "شخصية الملخص"
tag = "علامة"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "سطر الوصف"
tagline_placeholder = "منصة تدوين PHP بدون قاعدة بيانات"
tagline_description = "في بضع كلمات ، اشرح موضوع هذه المدونة."
tags = "علامات"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "هذا رمز قديم. عادةً ما يتم إنشاء تحليلات جديدة باستخدام gtag.js"
this_page_doesnt_exist = "هذه الصفحة غير موجودة!"
time = "الوقت"
timezone = "المنطقة الزمنية"
title = "العنوان"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "لاستخدام Disqus أو Facebook ، تحتاج إلى تقديم اسم قصير لـ Disqus أو معرّف تطبيق Facebook."
token_error = "CSRF Token not correct"
tools = "أدوات"
twitter_account = "حساب Twitter"
type_to_search = "اكتب للبحث"
uncategorized = "غير مصنف"
uncategorized_comment = "الموضوعات التي لا تحتاج إلى فئة أو لا تتناسب مع أي فئة أخرى موجودة"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "تحديث"
update_available = "التحديث متاح"
update_draft = "تحديث المسودة"
update_post = "تحديث المنشور"
update_to = "تحديث إلى"
upload = "تحميل"
user = "مستخدم"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "تتراوح القيم الصالحة من 0.0 إلى 1.0. راجع"
video_post = "مشاركة فيديو"
video_post_comment = "إنشاء منشور مدونة بفيديو مميز"
view = "عرض"
view_post = "View"
views = "المشاهدات"
widget = "أداة"
widget_settings = "إعدادات الأداة"
would_you_like_to_try_our = "هل ترغب في تجربة"
yes_im_in = "نعم أنا موجود"
yes_not_recommended = "نعم (غير مستحسن)"
you_dont_have_permission_to_access_this_page = "ليس لديك إذن للوصول إلى هذه الصفحة"
your_new_config_key = "مفتاح التكوين الجديد الخاص بك"
your_new_value = "قيمتك الجديدة"
your_backups = "نُسخك الاحتياطية"
your_latest_blog_posts = "أحدث مشاركات المدونة الخاصة بك"
your_recent_posts = "مشاركاتك الأخيرة"
by = "بقلم"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u> تلميح: </ u> استخدم <code> Ctrl </code> / <code> CMD </code> + <code> F </code> للبحث عن مفتاح التهيئة أو القيمة."
homepage = "الصفحة الرئيسية"
instead = "بدلاً من ذلك"
item_class = "إدراج فئة CSS"
item_slug = "إدراج عنوان URL للرابط"
now = "الآن"
of = "of"
optional = "اختياري"
post_your_post_slug = "/ نشر / your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u> نصائح احترافية: </ u> يمكنك إنشاء مفتاح تهيئة مخصص وطباعة قيمة مفتاح التهيئة في أي مكان في القالب."
read_more = "اقرأ المزيد"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/ سنة / شهر / your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
