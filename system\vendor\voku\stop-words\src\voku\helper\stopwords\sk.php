<?php

// slovak

static $data = [
    'a',
    'aby',
    'aj',
    'ak',
    'ako',
    'ale',
    'alebo',
    'and',
    'ani',
    'áno',
    'asi',
    'a<PERSON>',
    'bez',
    'bude',
    'budem',
    'bude<PERSON>',
    'budeme',
    'budete',
    'budú',
    'by',
    'bol',
    'bola',
    'boli',
    'bolo',
    'by<PERSON>',
    'cez',
    'čo',
    'či',
    '<PERSON>al<PERSON><PERSON>',
    '<PERSON>al<PERSON>ia',
    'ďal<PERSON>ie',
    'dnes',
    'do',
    'ho',
    'ešte',
    'for',
    'i',
    'ja',
    'je',
    'jeho',
    'jej',
    'ich',
    'iba',
    'iné',
    'iný',
    'som',
    'si',
    'sme',
    'sú',
    'k',
    'kam',
    'každ<PERSON>',
    'každ<PERSON>',
    'každ<PERSON>',
    'každ<PERSON>',
    'kde',
    'ke<PERSON>',
    'kto',
    'ktor<PERSON>',
    'ktor<PERSON>',
    'ktor<PERSON>',
    'ktor<PERSON>',
    'ktor<PERSON>',
    'ku',
    'lebo',
    'len',
    'ma',
    'ma<PERSON>',
    'má',
    'm<PERSON>te',
    'medzi',
    'mi',
    'mna',
    'mne',
    'mnou',
    'musieť',
    'môcť',
    'môj',
    'môže',
    'my',
    'na',
    'nad',
    'nám',
    'náš',
    'naši',
    'nie',
    'nech',
    'než',
    'nič',
    'niektorý',
    'nové',
    'nový',
    'nová',
    'nové',
    'noví',
    'o',
    'od',
    'odo',
    'of',
    'on',
    'ona',
    'ono',
    'oni',
    'ony',
    'po',
    'pod',
    'podľa',
    'pokiaľ',
    'potom',
    'práve',
    'pre',
    'prečo',
    'preto',
    'pretože',
    'prvý',
    'prvá',
    'prvé',
    'prví',
    'pred',
    'predo',
    'pri',
    'pýta',
    's',
    'sa',
    'so',
    'si',
    'svoje',
    'svoj',
    'svojich',
    'svojím',
    'svojími',
    'ta',
    'tak',
    'takže',
    'táto',
    'teda',
    'te',
    'tě',
    'ten',
    'tento',
    'the',
    'tieto',
    'tým',
    'týmto',
    'tiež',
    'to',
    'toto',
    'toho',
    'tohoto',
    'tom',
    'tomto',
    'tomuto',
    'toto',
    'tu',
    'tú',
    'túto',
    'tvoj',
    'ty',
    'tvojími',
    'už',
    'v',
    'vám',
    'váš',
    'vaše',
    'vo',
    'viac',
    'však',
    'všetok',
    'vy',
    'z',
    'za',
    'zo',
    'že',
];

$result =& $data;
unset($data);
return $result;
