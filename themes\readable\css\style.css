/*-------------------------
	Simple reset
--------------------------*/

* {
    margin: 0;
    padding: 0;
}

body {
    font-family: Georgia, sans-serif, <PERSON><PERSON>;
    font-size: 17px;
    line-height: 1.6;
    color: #343A3F;
    padding: 0;
    margin: 0;
}

section, footer, header, aside, nav {
    display: block;
}

img {
    border: 0 none;
    height: auto;
    max-width: 100%;
    outline: 0 none;
}

/*-------------------------
	Heading
--------------------------*/

h1, h2, h3, h4, h5, h6 {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.3;
    margin: 0.5em 0;
}

h1 {
    font-size: 28px;
}

h2 {
    font-size: 24px;
}

h3 {
    font-size: 18px;
}

h4 {
    font-size: 16px;
}

h5 {
    font-size: 14px;
}

h6 {
    font-size: 12px;
}

/*-------------------------
	Layout
--------------------------*/

#outer-wrapper {
    margin: 0;
    padding: 0;
    float: left;
    width: 100%;
}

#inner-wrapper {
    padding: 0 10px;
}

.container {
    padding: 0 20px;
}

#header-wrapper {
    background: #546673;
    position: relative;
    padding: 1em 0;
    float: left;
    width: 100%;
    color: #ffffff;
    font-family: Georgia, sans-serif;
    font-style: italic;
}

#content-wrapper {
    float: left;
    width: 100%;
    padding: 3em 0;
}

#menu, #header, #content, #footer {
    width: 800px;
    margin: 0 auto;
}

.admin #content {
    width: 980px;
    margin: 0 auto;
}

#header {
    text-align: center;
}

.hide {
    display: none;
}

/*-------------------------
	Link
--------------------------*/

a, a:visited {
    outline: none;
    color: #2E9FFF;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

.tab {
    width: 100%;
    margin: 1.5em 0 0 0;
    line-height: 1;
    border-bottom: 1px solid #E4E7EE;
}

.tab ul {
	padding:0;
	margin:0;
}

.tab  li {
    display: inline-block;
	margin:0;
	padding:0;
}

.tab a {
    background: #E4E7EE;
    border-radius: 6px 6px 0px 0;
    color: #555555;
    text-align: center;
    padding: 8px 14px;
    display: inline-block;
    margin: 0 5px;
    font-size: 12px;
    font-weight: bold;
    font-family: 'Open Sans', sans-serif;
}

/*-------------------------
	Text element
--------------------------*/

blockquote:before {
    color: #BBBBBB;
    content: "''";
    font-size: 3em;
    line-height: 0.1em;
    margin-right: 0.2em;
    vertical-align: -0.4em;
}

blockquote:after {
    color: #BBBBBB;
    content: "''";
    font-size: 3em;
    line-height: 0.1em;
    vertical-align: -0.45em;
}

blockquote {
    font-style: italic;
    margin: 1em 0 1em 1em;
}

blockquote p {
    display: inline;
}

pre {
    margin: 1em 0;
    overflow: auto;
    background: #F1F1FF;
    color: #333333;
    display: block;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    padding: 10px;
    line-height: 1.3;
}

code {
    color: #333333;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 15px;
    padding: 3px 8px;
    background: #F1F1FF;
}

pre code {
    color: #333333;
    display: block;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    padding: 0;
    line-height: 1.6;
}

.thumbnail {
    float: left;
    height: 80px;
    width: 80px;
    margin: 5px 15px 0 0;
    overflow: hidden;
    display: block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

ul, ol {
    padding-left: 30px;
    margin: 1em 0;
}

ul li, ol li {
    margin: 0.25em 0;
}

input[type=text], input[type=password] {
    padding: 4px 6px;
    font-size: 14px;
    background-color: #F6F7F9;
    border: 1px solid #CFDAE5;
}

input[type=text]:hover, input[type=password]:hover {
    background-color: #F6F7F9;
    border: 1px solid #CCCCCC;
}

input[type=submit] {
    padding: 4px 6px;
    font-size: 14px;
    cursor: pointer;
}

input.text {
    width: 75%;
    padding: 5px;
    font-size: 14px;
    background-color: #F6F7F9;
    border: 1px solid #CFDAE5;
}

input.error, textarea.error {
    border: 1px solid red;
}

textarea {
    font-size: 15px;
    font-family: Georgia, sans-serif;
    line-height: 1.4;
    background-color: #F6F7F9;
    border: 1px solid #CFDAE5;
    padding: 2%;
    width: 96%;
    min-height: 5em;
    overflow: auto;
}

textarea:hover {
    border: 1px solid #CCCCCC;
}

.error-message {
    color: red;
    background: #f4f4f4;
    padding: 10px 0;
    margin-bottom: 2em;
}

span.required {
    color: red;
}

span.help {
    font-size: 12px;
    font-style: italic;
}

.category {
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 4px;
    color: #333;
    display: block;
    font-size: 13px;
    margin: 20px 0;
    padding: 0 1em;
}

.tags a:after {
    content:", ";
}
.tags a:last-child:after {
    content:"";
}

/*-------------------------
	Table
--------------------------*/

table {
    font-size: 12px;
    border: none;
    width: 100%;
    color: #333333;
    border: 1px solid #E3E3E3;
    margin: 1em 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

table h2.title {
    margin: 5px 0;
}

th, td {
    padding: 5px 10px;
    border: none;
}

th.title {
    margin: 5px 0;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    font-weight: normal;
}

td.title {
    font-weight: normal;
    background-color: #f6f6f6;
    font-family: 'Open Sans', sans-serif;
}

th {
    background-color: #f6f6f6;
    border-bottom: 1px solid #E3E3E3;
    border-right: 1px solid #E3E3E3;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
}

td {
    background-color: #fafafa;
    border: 1px solid #E3E3E3;
    border-top: none;
    border-left: none;
}

table.post-list td a {
    margin: 0 5px;
}

/*-------------------------
	Menu
--------------------------*/

#menu-wrapper {
    font-family: 'Open Sans', sans-serif;
    color: #7E909D;
    padding: 10px 0;
    float: left;
    width: 100%;
}

#menu {
    font-weight: bold;
    font-family: 'Open Sans', sans-serif;
    color: #7E909D;
    font-size: 14px;
}

#menu ul {
    margin: 0;
    padding: 0;
}

#menu a {
    color: #7E909D;
}

#menu ul li {
    float: left;
    margin: 0 20px 0 0;
    list-style: none;
    padding-top: 3px;
}

#menu ul li.active > a {
    text-decoration: underline;
}

#menu ul li:last-child {
    margin: 0;
}

#menu ul li ul.subnav {
    display:none;
}

/*----------------------------
	Search form
-----------------------------*/

#search-form {
    position: relative;
    float: right;
}

.error-404 .post #search-form,
.error-404-search .post #search-form {
    float: none;
}

.search-input {
    margin: 0;
    padding: 4px 15px;
    font-size: 14px;
    border: 1px solid #0076a3 !important;
    background: #FFFFFF !important;
    border-top-left-radius: 5px 5px;
    border-bottom-left-radius: 5px 5px;
    width: 120px;
    color: #888888;
}

.search-button {
    margin: 0;
    padding: 4px;
    font-size: 14px;
    outline: none;
    cursor: pointer;
    text-align: center;
    text-decoration: none;
    color: #ffffff;
    border: solid 1px #546673;
    border-right: 0px;
    background: #0095cd;
    background: -webkit-gradient(linear, left top, left bottom, from(#2E9FFF), to(#2E9CCC));
    background: -moz-linear-gradient(top, #2E9FFF, #2E9CCC);
    border-top-right-radius: 5px 5px;
    border-bottom-right-radius: 5px 5px;
    width: 60px;
}

.search-button:hover {
    text-decoration: none;
    background: #007ead;
    background: -webkit-gradient(linear, left top, left bottom, from(#0095cc), to(#00678e));
    background: -moz-linear-gradient(top, #0095cc, #00678e);
}

/* Fixes submit button height problem in Firefox */
.search-button::-moz-focus-inner {
    border: 0;
}

/*-------------------------
	Post
--------------------------*/

.post {
    border-bottom: 1px solid #EBF2F6;
    padding: 1em 0;
    float: left;
    width: 100%;
}

.in-post .post {
    padding-bottom: 0;
}

p, ul {
    margin: 1em 0;
}

.breadcrumb {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    font-weight: normal;
}

.date {
    font-family: 'Open Sans', sans-serif;
    font-size: 14px;
    color: #A7A7A7;
    font-weight: normal;
    margin: 1em 0;
}

h1.blog-title, h2.blog-title {
    font-style: normal;
    font-size: 28px;
}

h1.title-post a, h2.title-index a {
    color: #4f4f4f;
    text-decoration: none;
}

h1.title-post a:hover, h2.title-index a:hover {
    color: #2E9FFF;
}

.in-post .post {
    padding-top: 0;
    border: none;
}

.in-page .border {
    padding: 0;
    margin: 0;
    border: 0;
}

.in-page .post {
    padding-top: 0;
}

.in-front .first, .in-post .post, .in-tag .first, .in-archive .first, .in-search .first, .error-404 .post, .error-404-search .post {
    padding-top: 0;
}

.in-front .first h2.title-index {
    margin-top: 0;
}

.in-profile .bio {
    font-size: 14px;
    font-style: italic;
    border-bottom: 1px solid #EBF2F6;
    padding-bottom: 1em;
    margin-bottom: 2em;
}

.post-list {
    font-size: 14px;
}

/*-------------------------
	Share box
--------------------------*/

.separator {
    font-size: 20px;
}

.share-box {
    font-size: 14px;
    float: left;
}

.author-info {
    width: 540px;
    float: left;
}

.author-info p {
    font-style: italic;
}

.share {
    width: 140px;
    margin-right: 1em;
    float: right;
}

.share a {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin: 0;
    padding: 0 5px;
    vertical-align: middle;
    -o-transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -ms-transition: all .3s;
    text-indent: -9999em;
}

.share a.twitter {
    background: url(../img/share-twitter.png) left top no-repeat;
}

.share a.facebook {
    background: url(../img/share-facebook.png) left top no-repeat;
}

.share a.googleplus {
    background: url(../img/share-googleplus.png) left top no-repeat;
}

.share a:hover {
    background-position: left -26px;
}

/*-------------------------
	Comments
--------------------------*/

.comments {
    position: relative;
    display: block;
    font-size: 16px;
    float: left;
    width: 100%;
}

.border {
    border-top: 1px solid #DFDFDF;
    margin-bottom: 15px;
    margin-top: 10px;
    padding-bottom: 15px;
    width: 100%;
    float: left;
}

.border .fb-comments, .border #disqus_thread {
    padding-top: 1.2em;
}

#disqus_thread {
    font-family: Georgia, Times, Cambria, serif;
    float: left;
    width: 100%;
}

/*-------------------------
	Raleted posts
--------------------------*/

.related {
    font-size: 14px;
    font-family: 'Open Sans', sans-serif;
    width: 100%;
    float: left;
    margin-bottom: 1em;
}

.related ul {
    margin: 0;
}

/*-------------------------
	Pagination + Postnav
--------------------------*/

.postnav {
    width: 100%;
    float: left;
    padding-bottom: 1em;
    font-family: 'Open Sans', sans-serif;
    font-size: 12px;
    font-weight: bold;
}

.postnav a {
    background: none repeat scroll 0 0 #E4E7EE;
    border-radius: 3px;
    color: #555555;
    line-height: 1;
    text-align: center;
}

.postnav a:hover {
    text-decoration: none;
    color: #333333;
}

.postnav .newer {
    float: left;
    margin-right: 1em;
    margin-bottom: 0.1em;
    padding: 10px 15px 10px 25px;
}

.postnav .older {
    float: right;
    padding: 10px 25px 10px 15px;
}

.postnav .pagination-arrow {
    display: inline-block;
    border-radius: 3px;
    color: #555 !important;
    text-decoration: none !important;
    text-transform: none;
    position: relative;
}

.postnav .pagination-arrow.newer:before,
.postnav .pagination-arrow.older:before {
    content: '';
    border: 5px solid #555;
    border-color: transparent #555 transparent transparent;
    width: 0;
    height: 0;
    position: absolute;
    left: 5px;
    top: 12px;
}

.postnav .pagination-arrow.older:before {
    left: auto;
    right: 5px;
    border-color: transparent transparent transparent #555;
}

.pager {
    width: 100%;
    float: left;
    padding: 30px 0 1em 0;
    font-family: 'Open Sans', sans-serif;
    font-size: 12px;
    font-weight: bold;
}

.pager a {
    background: none repeat scroll 0 0 #E4E7EE;
    border-radius: 3px;
    color: #555555;
    line-height: 1;
    padding: 10px 20px;
    text-align: center;
}

.pager .newer {
    float: left;
}

.pager .older {
    float: right;
}

.pager a:hover {
    text-decoration: none;
    color: #333333;
}

/*-------------------------
	Footer
--------------------------*/

#footer-wrapper {
    background: #546673;
    position: relative;
    padding: 20px 0;
    float: left;
    width: 100%;
    color: #ABB6C5;
    font-family: 'Open Sans', sans-serif;
}

#footer {
    font-size: 14px;
	text-align: center;
}

#footer a {
    color: #CBD2DC;
    font-weight: bold;
}

#footer ul {
    margin: 0.5em 0;
}

.footer-column {
    padding-bottom: 1.5em;
    float: left;
    width: 100%;
}

.column {
    float: left;
    width: 25%;
}

.recent ul {
    padding-left: 20px;
    width: 85%;
}

.archive .toggle {
    font-family: Georgia, Arial, sans-serif;
    font-size: 16px;
}

.social {
    float: right;
}

.social .inner {
    padding-left: 30px;
}

.tagcloud ul {
    padding: 0;
    margin: 0;
}

.tagcloud ul li {
    float: left;
    list-style: none;
    margin-right: 10px;
}

.copyright {
    font-size: 12px;
    float: left;
    width: 100%;
    padding-top: 1em;
}

.copyright p {
    margin: 0.5em 0;
    line-height: 1.2;
}

/*----------------------------
	The 404 page
-----------------------------*/

.message {
    padding: 50px 20px 20px 20px;
}

.message h1 {
    font-size: 36px;
    margin-bottom: 18px;
}

.message p {
    font-size: 13px;
}

.center {
    text-align: center;
}

.search-404 {
    position: relative;
    float: left;
    width: 100%;
    margin-bottom: 1.2em;
}

.search-404 #search-form {
    float: none;
}

/*----------------------------
	Media queries
-----------------------------*/

@media all and (max-width: 700px) {

    #menu, #header, #content, #footer {
        margin: 0 auto;
        width: 100%;
    }

    .admin #menu, .admin #header, .admin #content, .admin #footer {
        margin: 0 auto;
        width: 100%;
    }

    #menu-wrapper {
        text-align: center;
    }

    #menu ul li {
        float: none;
        display: inline-block;
    }

    #search-form {
        float: none;
        padding: 10px 0;
    }

    .author-info {
        width: 100%;
        float: left;
    }

    .share {
        width: 100%;
        margin-right: 0;
        padding-bottom: 1em;
    }

    .column {
        float: left;
        width: 100%;
    }

    .postnav .newer {
        float: left;
        margin-bottom: 1em;
    }

    .postnav .older {
        float: left;
    }

    .social .inner {
        padding-left: 0px;
    }

    pre {
        white-space: pre-wrap;
        word-wrap: break-word;
    }

}

/*----------------------------
	Video
-----------------------------*/

.video-wrapper {
    margin: 1em 0;
}

@media all and (min-width: 420px) {

    .video-wrapper {
        padding-top: 315px !important;
        position: relative;
        width: 100%;
        max-width: 420px !important;
        max-height: 315px !important;
    }

    .video-wrapper iframe, .video-wrapper object, .video-wrapper embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        max-width: 420px !important;
        width: 100%;
        max-height: 315px !important;
    }

}

@media all and (max-width: 420px) {

    .video-wrapper {
        position: relative;
        padding-bottom: 56.25%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }

    .video-wrapper iframe, .video-wrapper object, .video-wrapper embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
    }

}

@media all and (max-width: 560px) {

    .featured-video, .featured-audio {
        position: relative;
        padding-bottom: 56.25%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }

    .featured-video iframe, .featured-video object, .featured-video embed, .featured-audio iframe, .featured-audio object, .featured-audio embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
    }

}

/*----------------------------
        Archive
-----------------------------*/

ul.archivegroup {
    padding: 0;
    margin: 0;
}

.archivegroup .expanded ul {
    display: block;
}

.archivegroup .collapsed ul {
    display: none;
}

.archivegroup li.expanded, .archivegroup li.collapsed {
    list-style: none;
}

#more {
    display:block;
    height:1rem;
    margin-top:-1rem;
    box-shadow: none;
    border: none;
}