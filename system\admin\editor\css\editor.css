body .form-control, .wmd-input, .wmd-preview {
    font-family: Georgia, sans-serif;
}

blockquote {
    border-left: 2px dotted #888;
    padding-left: 5px;
    background: #F6F7F9;
}

.wmd-panel {
    float: left;
}

.wmd-button-bar {
    background-color: #F6F7F9;
    padding: 5px 0;
    margin-bottom: 10px;
    width: 100%;
    border: 1px solid #CFDAE5;
	border-radius:5px;
    display: block;
    float: left;
}
.dark-mode .wmd-button-bar {
	background-color: #292d32;
	border-color: #292d32 !important;
}
.wmd-button-bar:hover {
    border: 1px solid #CCCCCC;
}

.wmd-input {
    min-height: 300px;
}

.wmd-preview {
    background-color: #E4EBF1;
    float: left;
    padding: 2%;
}

.wmd-preview table {
    margin: 20px 0;
}

.wmd-preview td, .wmd-preview th {
    border-right: 1px solid #ccc;
    padding: 8px 12px;
}

.wmd-preview td:last-child, .wmd-preview th:last-child {
    border-right: none;
}

.wmd-preview td {
    border-top: 1px solid #ccc;	
}

.wmd-button-row {
    position: relative;
    margin: 0px;
    padding: 0px;
}

.wmd-spacer {
    width: 1px;
    height: 20px;
    background-color: Silver;
    list-style: none;
    float: left;
    margin: 5px;
}

.wmd-button {
    list-style: none;
    cursor: pointer;
    float: left;
}

.wmd-button > span {
    background-repeat: no-repeat;
    background-position: 0px 0px;
    display: inline-block;
}

.dark-mode .wmd-button > span.btn-light {
	color: #fff;
	background-color: #292d32;
	border-color: #292d32;
	box-shadow: none;
}
.dark-mode .wmd-button > span.btn-light:not(.disabled):hover {
	background-color: #171a1d;
	border-color: #343a40;
}
.wmd-spacer1 {
    left: 50px;
}

.wmd-spacer2 {
    left: 175px;
}

.wmd-spacer3 {
    left: 300px;
}

.wmd-prompt-background {
    background-color: Black;
}

.wmd-prompt-dialog {
    border: 1px solid #999999;
    background-color: #F5F5F5;
}

.wmd-prompt-dialog > div {
    font-size: 0.8em;
    font-family: arial, helvetica, sans-serif;
}

.wmd-prompt-dialog > form > input[type="text"] {
    border: 1px solid #999999;
    color: black;
}

.wmd-prompt-dialog > form > input[type="button"] {
    border: 1px solid #888888;
    font-family: trebuchet MS, helvetica, sans-serif;
    font-size: 0.8em;
    font-weight: bold;
}

pre {
    margin: 1em 0;
    overflow: auto;
    background: #F1F1FF;
}

pre code {
    color: #333333;
    display: block;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    padding: 5px 10px;
}

@media all and (max-width: 980px) {

    .nav {
        width: 100%;
        padding: 2%;
    }

    .wmd-panel, .wmd-preview {
        width: 96%;
        float: left;
        padding: 2%;
    }

    pre {
        white-space: pre-wrap;
        word-wrap: break-word;
    }

}

.notice {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 20px;
    background-color: #f0f9ff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.1);
    font-size: 14px;
    color: #333;
    z-index: 999;
    display: flex;
    align-items: center;
    display: none;
}

.notice.error {
  background-color: #fdd;
}