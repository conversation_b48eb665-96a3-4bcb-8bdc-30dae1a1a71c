about = "Perihal"
add_category = "Kategori Add"
add_content = "Kandungan Add"
add_link = "Pautan Tambah"
add_menu = "Menu Add"
add_new_page = "Tambah halaman baru"
add_new_post = "Tambah jawatan baru"
add_source_link_optional = "Tambah pautan sumber (pilihan)"
add_sub = "Halaman sub Add"
address_url = "Alamat (URL)"
admin = "Admin"
admin_panel_style_based_on = "Gaya panel Admin berdasarkan"
all_blog_posts = "Jawatan blog Semua"
all_cache_has_been_deleted = "Semua cache telah dipadam!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Arkib"
are_you_sure_you_want_to_delete_ = "Adakah anda pasti anda mahu memadam <strong>% s </ strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Pada masa ini anda menggunakan auto menu dihasilkan."
audio_post = "Post Audio"
audio_post_comment = "Mewujudkan post blog dengan audio yang diketengahkan"
author = "Pengarang"
author_description = "Just another HTMLy user"
back_to = "Kembali kepada"
backup = "Backup"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Dalam satu ayat, memberitahu kita lebih lanjut mengenai blog anda."
blog_theme = "Blog Tema"
blog_title = "Tajuk blog"
blog_title_placeholder = "My HTMLy Blog"
blog_posts_displayed_as = "Jawatan Blog dipaparkan sebagai"
breadcrumb_home_text = "Text rumah Breadcrumb"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Dengan menggunakan pengimport ini anda bersetuju jika suapan itu milik anda atau sekurang-kurangnya anda mempunyai kuasa untuk menerbitkan ia."
css_class_optional = "CSS Kelas (pilihan)"
cache_expiration = "Cache tamat tempoh (dalam jam)"
cache_off = "Cache off"
cache_timestamp = "Cache cap masa"
cancel = "Batal"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "Kategori"
category = "Kategori"
check_update = "Daftar update"
clear_cache = "Kosongkan cache"
comma_separated_values = "Comma Separated Values"
comment_system = "Sistem Comment"
comments = "Comments"
config = "Config"
congrats_you_have_the_latest_version_of_htmly = "Tahniah! Anda mempunyai versi terbaru HTMLy."
content = "Kandungan"
contents = "Kandungan"
copyright_line = "Garis Copyright"
copyright_line_placeholder = "(c) Nama anda."
create_backup = "Buat sandaran"
created = "Dicipta"
custom = "Custom"
custom_settings = "Tetapan Custom"
dashboard = "Dashboard"
date = "Tarikh"
date_format = "Format tarikh"
delete = "Padam"
description = "Penerangan"
disable = "Disable"
disabled = "Orang Kurang Upaya"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "Htmly"
draft = "Draf"
edit = "Edit"
edit_category = "Edit kategori"
edit_post = "Edit"
edit_profile = "Sunting profil"
enable = "Enable"
enable_blog_url = "Membolehkan blog URL"
enter_image_url = "Masukkan URL imej"
facebook_app_id = "Facebook App ID"
facebook_page = "Laman Facebook"
featured_audio = "Terutama Audio"
featured_image = "Imej Pilihan"
featured_link = "Kemudahan Link"
featured_quote = "Quote Pilihan"
featured_video = "Terutama Video"
feed_url = "URL Feed"
filename = "Nama fail"
follow = "Ikut"
for_google_site_verification_meta = "Bagi google-site-pengesahan meta"
for_msvalidate_01_meta = "Bagi msvalidate.01 meta"
front_page_displays = "Memaparkan halaman depan"
full_post = "Post Penuh"
general = "Umum"
general_settings = "Tetapan umum"
get_one_here = "Dapatkan satu di sini"
github_pre_release = "Github pra keluaran"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (warisan)"
google_search_console = "Google Search Console"
home = "Home"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Jika cuti mengosongkan kami akan petikan dari kandungan di bawah"
if_the_url_is_left_empty_we_will_use_the_page_title = "Jika cuti url kosongkan kami akan menggunakan tajuk halaman"
if_the_url_is_left_empty_we_will_use_the_post_title = "Jika cuti url kosongkan kami akan menggunakan tajuk post"
image_post = "Post Image"
image_post_comment = "Mewujudkan post blog dengan imej yang ditampilkan"
import = "Import"
import_feed = "Mula Import Feed"
import_rss = "Import RSS"
import_rss_feed_2.0 = "Import RSS Feed 2.0"
insert_image = "Insert Image"
invalid_error = "ERROR: Invalid username or password"
language = "Sistem Bahasa"
link_name = "Nama Link"
link_post = "Link post"
link_post_comment = "Mewujudkan post blog dengan link yang diketengahkan"
login = "Log masuk"
login_page = "Halaman Login"
logout = "Log keluar"
menu = "Menu"
menus = "Menu Editor"
meta_description = "Description Meta"
meta_description_character = "Meta description watak"
metatags = "Metatags"
metatags_settings = "Metatags Tetapan"
mine = "Mine"
more = "Lagi"
my_draft = "Draf Saya"
my_posts = "Jawatan saya"
name = "Nama"
newer = "Terbaru"
next = "Next"
next_post = "Post Next"
no_available_backup = "Tiada sandaran yang ada pada masa ini."
no_draft_found = "Tidak draf dijumpai"
no_posts_found = "Tiada ulasan dijumpai"
no_related_post_found = "Tiada suapan berita berkaitan dijumpai"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Nope"
not = "Tidak"
older = "Lama"
only = "Sahaja"
operations = "Operasi"
page = "Page"
page_generation_time = "Page masa generasi"
pages = "Pages"
pass_error = "Password field is required"
password = "Kata Laluan"
performance = "Prestasi"
performance_settings = "Tetapan Prestasi"
permalink = "Permalink"
popular = "Popular"
popular_posts = "Jawatan Popular"
popular_posts_widget = "Jawatan Popular widget"
popular_posts_widget_at_most = "Jawatan Popular widget paling banyak"
popular_tags = "Tag Popular"
post_by_author = "Posts by this author"
posted_in = "Posted in"
posted_on = "Posted on"
posts = "Siaran"
posts_by = "Posts by"
posts_draft = "Siaran menggubal"
posts_in_archive_page_at_most = "Siaran di halaman arkib paling banyak"
posts_in_category_page_at_most = "Siaran dalam halaman kategori paling banyak"
posts_in_front_page_show_at_most = "Siaran dalam persembahan muka depan paling banyak"
posts_in_profile_page_at_most = "Siaran di halaman profil paling banyak"
posts_in_search_result_at_most = "Siaran dalam hasil carian paling banyak"
posts_in_tag_page_at_most = "Siaran di halaman tag paling banyak"
posts_in_type_page_at_most = "Siaran di halaman jenis paling banyak"
posts_index_settings = "Siaran tetapan index"
posts_list = "Senarai Posts"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pra-release"
prev = "Tua"
prev_post = "Sebelum Post"
preview = "Preview"
profile_for = "Profile for"
proudly_powered_by = "Dengan bangganya dikuasakan oleh"
publish = "Menerbitkan"
publish_draft = "Terbitkan draf"
published = "Diterbitkan"
quote_post = "Post Quote"
quote_post_comment = "Mewujudkan blog post with quote diketengahkan"
rss_character = "Watak RSS"
rss_feeds_show_the_most_recent = "RSS suapan menunjukkan yang paling baru-baru ini"
rss_settings = "Tetapan RSS"
read_more_text = "Baca lebih lanjut text"
read_more_text_placeholder = "Baca lebih"
reading = "Membaca"
reading_settings = "Tetapan Reading"
recaptcha = "ReCAPTCHA"
recent_posts = "Jawatan baru-baru"
recent_posts_widget_at_most = "Jawatan terkini widget paling banyak"
regular_post = "Post biasa"
regular_post_comment = "Mewujudkan blog post biasa"
related_posts = "Related posts"
related_widget_posts_at_most = "Berkaitan widget jawatan paling banyak"
revert_to_draft = "Kembali kepada draf"
save = "Save"
save_config = "Simpan config"
save_edit = "Save Edit"
save_menu = "Simpan menu"
save_as_draft = "Menyimpan sebagai draf"
save_category = "Simpan kategori"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Cari"
search_for = "Carian untuk"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Secret Key"
settings = "Tetapan"
sign_in_to_start_your_session = "Log masuk untuk memulakan sesi anda"
site_key = "Laman Utama"
sitemap = "Sitemap"
slug = "Slug"
social_media = "Media Sosial"
static_page = "Halaman statik"
static_page_comment = "Membuat halaman statik"
static_pages = "Laman statik"
summary = "Ringkasan"
summary_character = "Ringkasan watak"
tag = "Tag"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Tagline"
tagline_placeholder = "Blogging Platform Databaseless PHP"
tagline_description = "Dalam beberapa perkataan, menjelaskan apa yang blog ini adalah kira-kira."
tags = "Tag"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Ini adalah kod warisan. Selalunya analisis baru dicipta menggunakan gtag.js"
this_page_doesnt_exist = "Laman ini tidak wujud!"
time = "Masa"
timezone = "Zon masa"
title = "Tajuk"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Untuk menggunakan Disqus atau Facebook komen anda perlu memberikan Disqus shortname atau Facebook App ID."
token_error = "CSRF Token not correct"
tools = "Tools"
twitter_account = "Akaun Twitter"
type_to_search = "Taip untuk mencari"
uncategorized = "Uncategorized"
uncategorized_comment = "Topik yang tidak perlu kategori, atau tidak patut ke dalam mana-mana kategori yang sedia ada yang lain"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Update"
update_available = "Kemaskini ada"
update_draft = "Update draf"
update_post = "Update post"
update_to = "Kemas kini kepada"
upload = "Muat naik"
user = "Pengguna"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Nilai sah berkisar 0,0-1,0. Lihat"
video_post = "Post Video"
video_post_comment = "Mewujudkan post blog dengan video yang diketengahkan"
view = "View"
view_post = "View"
views = "Paparan"
widget = "Widget"
widget_settings = "Widget Tetapan"
would_you_like_to_try_our = "Adakah anda ingin mencuba kami"
yes_im_in = "Ya saya setuju"
yes_not_recommended = "Ya (tidak digalakkan)"
you_dont_have_permission_to_access_this_page = "Anda tidak mempunyai kebenaran untuk mengakses halaman ini"
your_new_config_key = "Baru Anda Config Key"
your_new_value = "Nilai Baru Anda"
your_backups = "Backup Anda"
your_latest_blog_posts = "Posting blog terkini Anda"
your_recent_posts = "Jawatan baru-baru Anda"
by = "Oleh"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<U> tanda-tanda: </ u> Gunakan <code> Ctrl </ code> / <code> CMD </ code> + <code> F </ code> untuk mencari kekunci config anda atau nilai."
homepage = "Laman utama"
instead = "Dan bukannya"
item_class = "Insert kelas CSS"
item_slug = "Insert Link URL"
now = "Sekarang"
of = "of"
optional = "Pilihan"
post_your_post_slug = "/ Pos /-post-slug anda"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<U> pro tips:. </ U> Anda boleh mewujudkan utama config adat dan mencetak nilai mana-mana sahaja kunci config anda dalam template anda"
read_more = "Read more"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/ Tahun / bulan /-post-slug anda"
your_key = "Your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
