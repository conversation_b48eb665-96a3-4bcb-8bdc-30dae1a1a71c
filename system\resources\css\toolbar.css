body {
    padding-top: 30px
}

#toolbar {
    background: #666;
    box-shadow: 0 5px 15px #000;
    color: #CCC;
    font-family: Georgia, sans-serif;
    left: 0;
    margin: 0 -20px;
    padding: 0 35px;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 6000;
    border: 0;
    font-size: 15px;
    text-align: left;
    vertical-align: baseline;
    min-height: 30px
}

#toolbar ul {
    margin: 0;
    padding: 0;
}

#toolbar ul li, #toolbar ul li a {
    display:inline-block;
    list-style: none;
    margin: 0;
    height: 30px;
    line-height: 30px;
}

#toolbar a {
    color: #FFF;
    font-size: .846em;
    text-decoration: none;
    padding: 0 10px;
}

#toolbar a:hover {
    background: #333;
    border-radius: 3px;
}

table#config td:first-child {
    width: 30%
}

table#config td:last-child {
    width: 70%
}

table#config input {
    width: 100%;
    box-sizing: border-box
}

table#config tr:last-child td {
    border-bottom: none
}
#htmly-menu-toggle {
    display: none;
    background: #666;
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    font-size: 16px;
}

#htmly-menu {
    display: block;
}

#htmly-menu-toggle {
    display: none;
}

#htmly-menu-button {
    display: none;
    background: #666;
    color: white;
    padding: 10px;
    cursor: pointer;
    font-size: 16px;
}

@media all and (max-width: 550px) {
    #htmly-menu-button {
        display: block;
    }

    #htmly-menu {
        display: none;
        background: #666;
        position: absolute;
        top: 50px;
        left: 0;
        width: 100%;
        box-shadow: 0 5px 15px #000;
    }

    #htmly-menu ul {
        display: flex;
        flex-direction: column;
        text-align: center;
    }

    #htmly-menu-toggle:checked + #htmly-menu {
        display: block;
    }
}