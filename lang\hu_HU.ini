about = "About"
add_category = "Kategória hozzáadása"
add_content = "Tartalom hozzáadása"
add_link = "Link hozzáadása"
add_menu = "Menü hozzáadása"
add_new_page = "Új oldal hozzáadása"
add_new_post = "Új bejegyzés hozzáadása"
add_source_link_optional = "Forrás link hozzáadása (nem kötelező)"
add_sub = "Aloldal hozzáadása"
address_url = "Address (URL)"
admin = "Rendszergazda"
admin_panel_style_based_on = "Az adminisztrációs panel stílusa"
all_blog_posts = "Minden blogbejegyzés"
all_cache_has_been_deleted = "Az összes gyorsítótár törölve lett!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Archívum"
are_you_sure_you_want_to_delete_ = "Biztosan törli a következőt: <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Jelenleg ön automatikusan generált menüt használ."
audio_post = "Hangbejegyzés"
audio_post_comment = "Blogbejegyzés létrehozása kiemelt hanganyaggal"
author = "Szerző"
author_description = "Just another HTMLy user"
back_to = "Vissza ide"
backup = "Biztonsági másolat"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "In one paragraph, tell us more about your blog."
blog_theme = "Blog Theme"
blog_title = "Blog title"
blog_title_placeholder = "My HTMLy Blog"
blog_posts_displayed_as = "Blog posts displayed as"
breadcrumb_home_text = "Breadcrumb home text"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Ennek az importnak a használatával elfogadja, ha a hírcsatorna a tiéd, vagy legalábbis jogosult vagy közzétenni."
css_class_optional = "CSS osztály (nem kötelező)"
cache_expiration = "Cache expiration (in hours)"
cache_off = "Cache off"
cache_timestamp = "Cache timestamp"
cancel = "Mégse"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "Kategóriák"
category = "Kategória"
check_update = "Frissítés ellenőrzése"
clear_cache = "Gyorsítótár törlése"
comma_separated_values = "Vesszővel elválasztott értékek"
comment_system = "Comment system"
comments = "Comments"
config = "Beállítás"
congrats_you_have_the_latest_version_of_htmly = "Gratulálunk! Ön a HTMLy legújabb verzióját használja."
content = "Tartalom"
contents = "Tartalom"
copyright_line = "Copyright line"
copyright_line_placeholder = "(c) Your name."
create_backup = "Biztonsági másolat létrehozása"
created = "Létrehozva"
custom = "Egyedi"
custom_settings = "Egyéni beállítások"
dashboard = "Irányítópult"
date = "Dátum"
date_format = "Date Format"
delete = "Törlés"
description = "Leírás"
disable = "Disable"
disabled = "Disabled"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "Piszkozat"
edit = "Szerkesztés"
edit_category = "Kategória szerkesztése"
edit_post = "Edit"
edit_profile = "Profil szerkesztése"
enable = "Enable"
enable_blog_url = "Enable blog URL"
enter_image_url = "Írja be a kép URL-jét"
facebook_app_id = "Facebook App ID"
facebook_page = "Facebook page"
featured_audio = "Kiemelt hang"
featured_image = "Kiemelt kép"
featured_link = "Kiemelt hivatkozás"
featured_quote = "Kiemelt idézet"
featured_video = "Kiemelt videó"
feed_url = "Hírcsatorna URL"
filename = "Fájlnév"
follow = "Követés"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Front page displays"
full_post = "Full post"
general = "Általános"
general_settings = "Általános beállítások"
get_one_here = "Get one here"
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Főoldal"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Ha üresen hagyja, akkor kivonjuk az alábbi tartalomból"
if_the_url_is_left_empty_we_will_use_the_page_title = "Ha az URL üres marad, akkor az oldal címét fogjuk használni"
if_the_url_is_left_empty_we_will_use_the_post_title = "Ha az URL üres marad, akkor a bejegyzés címét fogjuk használni"
image_post = "Képes bejegyzés"
image_post_comment = "Blogbejegyzés létrehozása kiemelt képpel"
import = "Import"
import_feed = "Hírcsatorna iport elindítása"
import_rss = "RSS importálása"
import_rss_feed_2.0 = "RSS-hírcsatorna 2.0 importálása"
insert_image = "Kép beszúrása"
invalid_error = "ERROR: Invalid username or password"
language = "System Language"
link_name = "Link neve"
link_post = "Link bejegyzés"
link_post_comment = "Blogbejegyzés létrehozása kiemelt linkkel"
login = "Bejelentkezés"
login_page = "Bejelentkezés oldal"
logout = "Kijelentkezés"
menu = "Menu"
menus = "Menüszerkesztő"
meta_description = "Meta leírás"
meta_description_character = "Meta description character"
metatags = "Metacímkék"
metatags_settings = "Metacímkék beállításai"
mine = "Az enyém"
more = "Több"
my_draft = "Saját piszkozat"
my_posts = "Saját bejegyzések"
name = "Név"
newer = "Újabb"
next = "Következő"
next_post = "Következő bejegyzés"
no_available_backup = "Jelenleg nincs elérhető biztonsági másolat."
no_draft_found = "Nincs vázlat"
no_posts_found = "Nincs bejegyzés"
no_related_post_found = "Nem található kapcsolódó bejegyzés"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Nope"
not = "No"
older = "Régebbi"
only = "Csak"
operations = "Műveletek"
page = "Page"
page_generation_time = "Page generation time"
pages = "Pages"
pass_error = "Password field is required"
password = "Jelszó"
performance = "Teljesítmény"
performance_settings = "Teljesítménybeállítás"
permalink = "Permalink"
popular = "Popular"
popular_posts = "Népszerű bejegyzések"
popular_posts_widget = "Popular posts widget"
popular_posts_widget_at_most = "Popular posts widget at most"
popular_tags = "Népszerű címkék"
post_by_author = "Posts by this author"
posted_in = "Bejegyzés helye"
posted_on = "Bejegyzés ideje"
posts = "Bejegyzések"
posts_by = "Posts by"
posts_draft = "Bejegyzések vázlata"
posts_in_archive_page_at_most = "Posts in archive page at most"
posts_in_category_page_at_most = "Posts in category page at most"
posts_in_front_page_show_at_most = "Posts in front page show at most"
posts_in_profile_page_at_most = "Posts in profile page at most"
posts_in_search_result_at_most = "Posts in search result at most"
posts_in_tag_page_at_most = "Posts in tag page at most"
posts_in_type_page_at_most = "Posts in type page at most"
posts_index_settings = "Posts index settings"
posts_list = "Bejegyzések listája"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pre-release"
prev = "Elöző"
prev_post = "Elöző bejegyzés"
preview = "Előnézet"
profile_for = "Profile for"
proudly_powered_by = "Büszkén működteti"
publish = "Közzététel"
publish_draft = "Piszkozat közzététele"
published = "Közzétett"
quote_post = "Bejegyzés idézettel"
quote_post_comment = "Blogbejegyzés létrehozása kiemelt idézettel"
rss_character = "RSS character"
rss_feeds_show_the_most_recent = "RSS feeds show the most recent"
rss_settings = "RSS settings"
read_more_text = "Read more text"
read_more_text_placeholder = "Read more"
reading = "Olvasás"
reading_settings = "Olvasási beállítások"
recaptcha = "reCAPTCHA"
recent_posts = "Legutóbbi bejegyzések"
recent_posts_widget_at_most = "Recent posts widget at most"
regular_post = "Rendszeres bejegyzés"
regular_post_comment = "Rendszeres bejegyzés létrehozása"
related_posts = "Kapcsolódó bejegyzések"
related_widget_posts_at_most = "Related widget posts at most"
revert_to_draft = "Vissza a piszkozathoz"
save = "Mentés"
save_config = "Save config"
save_edit = "Szerkesztés mentése"
save_menu = "Mentés menü"
save_as_draft = "Mentés piszkozatként"
save_category = "Kategória mentése"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Keresés"
search_for = "Keresés"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Secret Key"
settings = "Beállítások"
sign_in_to_start_your_session = "Jelentkezzen be a munkamenet megkezdéséhez"
site_key = "Site Key"
sitemap = "Sitemap"
slug = "TLD utáni elérési útvonal"
social_media = "Social Media"
static_page = "Statikus oldal"
static_page_comment = "Statikus oldal létrehozása"
static_pages = "Statikus oldalak"
summary = "Summary"
summary_character = "Summary character"
tag = "Címke"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Tagline"
tagline_placeholder = "Databaseless PHP Blogging Platform"
tagline_description = "In a few words, explain what this blog is about."
tags = "Címkék"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "This is legacy code. Usually new created analytics using gtag.js"
this_page_doesnt_exist = "Ez az oldal nem létezik!"
time = "Idő"
timezone = "Timezone"
title = "Cím"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "To using Disqus or Facebook comment you need to provide Disqus shortname or Facebook App ID."
token_error = "CSRF Token not correct"
tools = "Eszközök"
twitter_account = "Twitter account"
type_to_search = "Írja be a kereséshez"
uncategorized = "Nincs kategorizálva"
uncategorized_comment = "Olyan témák, amelyekhez nincs szükség kategóriára, vagy amelyek nem illenek más létező kategóriába"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Frissítés"
update_available = "Elérhető frissítés"
update_draft = "Piszkozat frissítése"
update_post = "Bejegyzés frissítése"
update_to = "Frissítés"
upload = "Feltöltés"
user = "Felhasználó"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Valid values range from 0.0 to 1.0. See"
video_post = "Videó bejegyzés"
video_post_comment = "Blogbejegyzés létrehozása kiemelt videóval"
view = "Nézet"
view_post = "View"
views = "Nézetek"
widget = "Widget"
widget_settings = "Widget beállítások"
would_you_like_to_try_our = "Szeretné kipróbálni a mi "
yes_im_in = "Yes I'm in"
yes_not_recommended = "Yes (not recommended)"
you_dont_have_permission_to_access_this_page = "Nincs engedélyed az oldal elérésére"
your_new_config_key = "Your New Config Key"
your_new_value = "Your New Value"
your_backups = "A biztonsági másolatok"
your_latest_blog_posts = "Your latest blog posts"
your_recent_posts = "Legutóbbi bejegyzéseid"
by = "által"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>hint:</u> Use <code>Ctrl</code>/<code>CMD</code> + <code>F</code> to search for your config key or value."
homepage = "kezdőlap"
instead = "helyett"
item_class = "CSS osztály beszúrása"
item_slug = "Link URL beszúrása"
now = "most"
of = "of"
optional = "választható"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>pro tips:</u> You can creating custom config key and print out your config key value anywhere in your template."
read_more = "tovább"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
