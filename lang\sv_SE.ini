about = "Om"
add_category = "Ny kategori"
add_content = "Nytt"
add_link = "Add link"
add_menu = "Add menu"
add_new_page = "Add new page"
add_new_post = "Add new post"
add_source_link_optional = "Add source link (optional)"
add_sub = "Ny undersida"
address_url = "Address (URL)"
admin = "Admin"
admin_panel_style_based_on = "Admin panel style based on"
all_blog_posts = "Alla blogginlägg"
all_cache_has_been_deleted = "All cache has been deleted !"
all_posts_tagged = "All posts tagged"
archive = "Arkiv"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Arkiv"
are_you_sure_you_want_to_delete_ = "Är du säker på att du vill radera <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "At the moment you are using auto generated menu."
audio_post = "Audioinlägg"
audio_post_comment = "Skapa inlägg med ljudklipp"
author = "Författare"
author_description = "Just another HTMLy user"
back_to = "Back to"
backup = "Säkerhetskopiera"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "In one paragraph, tell us more about your blog."
blog_theme = "Blog Theme"
blog_title = "Blog title"
blog_title_placeholder = "My HTMLy Blog"
blog_posts_displayed_as = "Blog posts displayed as"
breadcrumb_home_text = "Breadcrumb home text"
by = "av"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "By using this importer you are agree if the feed is yours or at least you have the authority to publish it."
css_class_optional = "CSS Class (optional)"
cache_expiration = "Cache expiration (in hours)"
cache_off = "Cache off"
cache_timestamp = "Cache timestamp"
cancel = "Avbryt"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "Kategorier"
category = "Kategori"
check_update = "Check update"
clear_cache = "Rensa cache"
comma_separated_values = "Comma separated values"
comment_system = "Comment system"
comments = "kommentarer"
config = "Konfiguration"
congrats_you_have_the_latest_version_of_htmly = "Congrats! You have the latest version of HTMLy."
content = "Content"
contents = "Innehåll"
continue_reading = "Läs vidare"
copyright_line = "Copyright line"
copyright_line_placeholder = "(c) Your name."
create_backup = "Create backup"
created = "Skapad"
custom = "Custom"
custom_settings = "Custom Settings"
dashboard = "Dashboard"
date = "Date"
date_format = "Date Format"
delete = "Radera"
description = "Beskrivning"
design_by = "Designad av"
disable = "Disable"
disabled = "Disabled"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "Utkast"
edit = "Redigera"
edit_category = "Edit category"
edit_post = "Edit"
edit_profile = "Redigera profil"
enable = "Enable"
enable_blog_url = "Enable blog URL"
enter_image_url = "Enter image URL"
facebook_app_id = "Facebook App ID"
facebook_page = "Facebook page"
featured_audio = "Featured Audio"
featured_image = "Featured Image"
featured_link = "Featured Link"
featured_quote = "Featured Quote"
featured_video = "Featured Video"
feed_url = "Feed URL"
filename = "Filnamn"
follow = "Följ"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Front page displays"
full_post = "Full post"
general = "General"
general_settings = "General Settings"
get_one_here = "Get one here"
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Home"
if_left_empty_we_will_excerpt_it_from_the_content_below = "If leave empty we will excerpt it from the content below"
if_the_url_is_left_empty_we_will_use_the_page_title = "If the url leave empty we will use the page title"
if_the_url_is_left_empty_we_will_use_the_post_title = "If the url leave empty we will use the post title"
image_post = "Bildinlägg"
image_post_comment = "Skapa inlägg med bild"
import = "Importera"
import_feed = "Start Import Feed"
import_rss = "Import RSS"
import_rss_feed_2.0 = "Import RSS Feed 2.0"
insert_image = "Insert Image"
invalid_error = "ERROR: Invalid username or password"
language = "System Language"
link_name = "Link name"
link_post = "Länkinlägg"
link_post_comment = "Skapa inlägg med länk"
login = "Login"
login_page = "Inloggningssida"
logout = "Logga ut"
menu = "Menu"
menus = "Menu Editor"
meta_description = "Meta-beskrivning"
meta_description_character = "Meta description character"
metatags = "Metatags"
metatags_settings = "Metatags Settings"
mine = "Mina"
more = "Mer"
my_draft = "Mina utkast"
my_posts = "Mina inlägg"
name = "Name"
newer = "Nyare"
newer_posts = "Nyare inlägg"
next = "Nästa"
next_post = "Nästa inlägg"
no_available_backup = "No available backup at this time."
no_draft_found = "Inga utkast funna"
no_newer_posts = "Inga nyare inlägg"
no_older_posts = "Inga äldre inlägg"
no_posts_found = "Inga inlägg funna"
no_related_post_found = "Inga relaterade inlägg funna"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Nope"
not = "No"
older = "Äldre"
older_posts = "Äldre inlägg"
only = "Only"
operations = "Åtgärder"
page = "Page"
page_generation_time = "Page generation time"
pages = "Pages"
pass_error = "Password field is required"
password = "Password"
performance = "Performance"
performance_settings = "Performance Settings"
permalink = "Permalink"
popular = "Popular"
popular_posts = "Populära inlägg"
popular_posts_widget = "Popular posts widget"
popular_posts_widget_at_most = "Popular posts widget at most"
popular_tags = "Populära etiketter"
post_by_author = "Posts by this author"
posted_in = "Upplagd i"
posted_on = "Upplagd på"
posts = "Inlägg"
posts_by = "Posts by"
posts_draft = "Posts draft"
posts_in_archive_page_at_most = "Posts in archive page at most"
posts_in_category_page_at_most = "Posts in category page at most"
posts_in_front_page_show_at_most = "Posts in front page show at most"
posts_in_profile_page_at_most = "Posts in profile page at most"
posts_in_search_result_at_most = "Posts in search result at most"
posts_in_tag_page_at_most = "Posts in tag page at most"
posts_in_type_page_at_most = "Posts in type page at most"
posts_index_settings = "Posts index settings"
posts_list = "Posts list"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pre-release"
prev = "Föregående"
prev_post = "Föregående inlägg"
preview = "Preview"
previous = "Föregående"
profile_for = "Profile for"
proudly_powered_by = "Proudly powered by"
publish = "Publicera"
publish_draft = "Publicera utkast"
published = "Publicerad"
published_by = "Publicerad av"
quote_post = "Citatinlägg"
quote_post_comment = "Skapa inlägg med citat"
rss_character = "RSS character"
rss_feeds_show_the_most_recent = "RSS feeds show the most recent"
rss_settings = "RSS settings"
read_more = "läs mer"
read_more_text = "Read more text"
read_more_text_placeholder = "Read more"
reading = "Reading"
reading_settings = "Reading Settings"
recaptcha = "reCAPTCHA"
recent_comments = "Senaste kommentarer"
recent_posts = "Senaste inlägg"
recent_posts_widget_at_most = "Recent posts widget at most"
regular_post = "Vanligt inlägg"
regular_post_comment = "Skapa vanligt inlägg"
related_posts = "Relaterade inlägg"
related_widget_posts_at_most = "Related widget posts at most"
return_to_home = "Tillbaka till startsidan"
revert_to_draft = "Återgå till utkast"
save = "Spara"
save_config = "Save config"
save_edit = "Save Edit"
save_menu = "Save menu"
save_as_draft = "Spara som utkast"
save_category = "Save category"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Sök"
search_for = "Sök efter"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Secret Key"
settings = "Settings"
share_this_post = "Dela inlägg"
sign_in_to_start_your_session = "Sign in to start your session"
site_key = "Site Key"
sitemap = "Sitemap"
slug = "Slug"
social_media = "Social Media"
static_page = "Statisk sida"
static_page_comment = "Skapa statisk sida"
static_pages = "Statiska sidor"
summary = "Summary"
summary_character = "Summary character"
tag = "Etikett"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Tagline"
tagline_placeholder = "Databaseless PHP Blogging Platform"
tagline_description = "In a few words, explain what this blog is about."
tags = "Etiketter"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "This is legacy code. Usually new created analytics using gtag.js"
this_page_doesnt_exist = "This page doesn't exist !"
time = "Time"
timezone = "Timezone"
title = "Titel"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "To using Disqus or Facebook comment you need to provide Disqus shortname or Facebook App ID."
token_error = "CSRF Token not correct"
tools = "Tools"
twitter_account = "Twitter account"
type_to_search = "Type to search"
uncategorized = "Ingen kategori"
uncategorized_comment = "Inlägg som inte behöver någon kategori eller inte passar i någon av övriga kategorier."
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Uppdatera"
update_available = "Update Available"
update_draft = "Uppdatera utkast"
update_post = "Uppdatera inlägg"
update_to = "Update to"
upload = "Upload"
user = "User"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Valid values range from 0.0 to 1.0. See"
video_post = "Videoinlägg"
video_post_comment = "Skapa inlägg med video"
view = "Visa"
view_post = "View"
views = "Visningar"
widget = "Widget"
widget_settings = "Widget Settings"
would_you_like_to_try_our = "Would you like to try our "
yes_im_in = "Yes I'm in"
yes_not_recommended = "Yes (not recommended)"
you_dont_have_permission_to_access_this_page = "You don't have permission to access this page"
your_new_config_key = "Your New Config Key"
your_new_value = "Your New Value"
your_backups = "Your backups"
your_latest_blog_posts = "Your latest blog posts"
your_recent_posts = "Dina senaste inlägg"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>hint:</u> Use <code>Ctrl</code>/<code>CMD</code> + <code>F</code> to search for your config key or value."
homepage = "homepage"
instead = "instead"
item_class = "Insert CSS class"
item_slug = "Insert Link URL"
now = "now"
of = "of"
optional = "optional"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>pro tips:</u> You can creating custom config key and print out your config key value anywhere in your template."
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
