*,
:after,
:before {
  --tw-border-spacing-x:0;
  --tw-border-spacing-y:0;
  --tw-translate-x:0;
  --tw-translate-y:0;
  --tw-rotate:0;
  --tw-skew-x:0;
  --tw-skew-y:0;
  --tw-scale-x:1;
  --tw-scale-y:1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness:proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width:0px;
  --tw-ring-offset-color:#fff;
  --tw-ring-color:rgba(59,130,246,.5);
  --tw-ring-offset-shadow:0 0 #0000;
  --tw-ring-shadow:0 0 #0000;
  --tw-shadow:0 0 #0000;
  --tw-shadow-colored:0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: 
}
::backdrop {
  --tw-border-spacing-x:0;
  --tw-border-spacing-y:0;
  --tw-translate-x:0;
  --tw-translate-y:0;
  --tw-rotate:0;
  --tw-skew-x:0;
  --tw-skew-y:0;
  --tw-scale-x:1;
  --tw-scale-y:1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness:proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width:0px;
  --tw-ring-offset-color:#fff;
  --tw-ring-color:rgba(59,130,246,.5);
  --tw-ring-offset-shadow:0 0 #0000;
  --tw-ring-shadow:0 0 #0000;
  --tw-shadow:0 0 #0000;
  --tw-shadow-colored:0 0 #0000;
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: 
}
/*
! tailwindcss v3.4.14 | MIT License | https://tailwindcss.com
*/*,
:after,
:before {
  box-sizing:border-box;
  border:0 solid #e5e7eb
}
:after,
:before {
  --tw-content:""
}
:host,
html {
  line-height:1.5;
  -webkit-text-size-adjust:100%;
  -moz-tab-size:4;
  -o-tab-size:4;
  tab-size:4;
  font-family:var(--font-space-grotesk),ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  font-feature-settings:normal;
  font-variation-settings:normal;
  -webkit-tap-highlight-color:transparent
}
body {
  margin:0;
  line-height:inherit
}
hr {
  height:0;
  color:inherit;
  border-top-width:1px
}
abbr:where([title]) {
  -webkit-text-decoration:underline dotted;
  text-decoration:underline dotted
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size:inherit;
  font-weight:inherit
}
a {
  color:inherit;
  text-decoration:inherit
}
b,
strong {
  font-weight:bolder
}
code,
kbd,
pre,
samp {
  font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;
  font-feature-settings:normal;
  font-variation-settings:normal;
  font-size:1em
}
small {
  font-size:80%
}
sub,
sup {
  font-size:75%;
  line-height:0;
  position:relative;
  vertical-align:baseline
}
sub {
  bottom:-.25em
}
sup {
  top:-.5em
}
table {
  text-indent:0;
  border-color:inherit;
  border-collapse:collapse
}
button,
input,
optgroup,
select,
textarea {
  font-family:inherit;
  font-feature-settings:inherit;
  font-variation-settings:inherit;
  font-size:100%;
  font-weight:inherit;
  line-height:inherit;
  letter-spacing:inherit;
  color:inherit;
  margin:0;
  padding:0
}
button,
select {
  text-transform:none
}
button,
input:where([type=button]),
input:where([type=reset]),
input:where([type=submit]) {
  -webkit-appearance:button;
  background-color:transparent;
  background-image:none
}
:-moz-focusring {
  outline:auto
}
:-moz-ui-invalid {
  box-shadow:none
}
progress {
  vertical-align:baseline
}
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height:auto
}
[type=search] {
  -webkit-appearance:textfield;
  outline-offset:-2px
}
::-webkit-search-decoration {
  -webkit-appearance:none
}
::-webkit-file-upload-button {
  -webkit-appearance:button;
  font:inherit
}
summary {
  display:list-item
}
blockquote,
dd,
dl,
figure,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
p,
pre {
  margin:0
}
fieldset {
  margin:0
}
fieldset,
legend {
  padding:0
}
menu,
ol,
ul {
  list-style:none;
  margin:0;
  padding:0
}
dialog {
  padding:0
}
textarea {
  resize:vertical
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  color:#9ca3af
}
input::placeholder,
textarea::placeholder {
  color:#9ca3af
}
[role=button],
button {
  cursor:pointer
}
:disabled {
  cursor:default
}
audio,
canvas,
embed,
iframe,
img,
object,
svg,
video {
  display:block;
  vertical-align:middle
}
img,
video {
  max-width:100%;
  height:auto
}
[hidden]:where(:not([hidden=until-found])) {
  display:none
}
[multiple],
[type=date],
[type=datetime-local],
[type=email],
[type=month],
[type=number],
[type=password],
[type=search],
[type=tel],
[type=text],
[type=time],
[type=url],
[type=week],
input:where(:not([type])),
select,
textarea {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  background-color:#fff;
  border-color:#6b7280;
  border-width:1px;
  border-radius:0;
  padding:.5rem .75rem;
  font-size:1rem;
  line-height:1.5rem;
  --tw-shadow:0 0 #0000
}
[multiple]:focus,
[type=date]:focus,
[type=datetime-local]:focus,
[type=email]:focus,
[type=month]:focus,
[type=number]:focus,
[type=password]:focus,
[type=search]:focus,
[type=tel]:focus,
[type=text]:focus,
[type=time]:focus,
[type=url]:focus,
[type=week]:focus,
input:where(:not([type])):focus,
select:focus,
textarea:focus {
  outline:2px solid transparent;
  outline-offset:2px;
  --tw-ring-inset:var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width:0px;
  --tw-ring-offset-color:#fff;
  --tw-ring-color:#2563eb;
  --tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);
  border-color:#2563eb
}
input::-moz-placeholder,
textarea::-moz-placeholder {
  color:#6b7280;
  opacity:1
}
input::placeholder,
textarea::placeholder {
  color:#6b7280;
  opacity:1
}
::-webkit-datetime-edit-fields-wrapper {
  padding:0
}
::-webkit-date-and-time-value {
  min-height:1.5em;
  text-align:inherit
}
::-webkit-datetime-edit {
  display:inline-flex
}
::-webkit-datetime-edit,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-meridiem-field,
::-webkit-datetime-edit-millisecond-field,
::-webkit-datetime-edit-minute-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-second-field,
::-webkit-datetime-edit-year-field {
  padding-top:0;
  padding-bottom:0
}
select {
  background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position:right .5rem center;
  background-repeat:no-repeat;
  background-size:1.5em 1.5em;
  padding-right:2.5rem;
  -webkit-print-color-adjust:exact;
  print-color-adjust:exact
}
[multiple],
[size]:where(select:not([size="1"])) {
  background-image:none;
  background-position:0 0;
  background-repeat:unset;
  background-size:initial;
  padding-right:.75rem;
  -webkit-print-color-adjust:unset;
  print-color-adjust:unset
}
[type=checkbox],
[type=radio] {
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  padding:0;
  -webkit-print-color-adjust:exact;
  print-color-adjust:exact;
  display:inline-block;
  vertical-align:middle;
  background-origin:border-box;
  -webkit-user-select:none;
  -moz-user-select:none;
  user-select:none;
  flex-shrink:0;
  height:1rem;
  width:1rem;
  color:#2563eb;
  background-color:#fff;
  border-color:#6b7280;
  border-width:1px;
  --tw-shadow:0 0 #0000
}
[type=checkbox] {
  border-radius:0
}
[type=radio] {
  border-radius:100%
}
[type=checkbox]:focus,
[type=radio]:focus {
  outline:2px solid transparent;
  outline-offset:2px;
  --tw-ring-inset:var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width:2px;
  --tw-ring-offset-color:#fff;
  --tw-ring-color:#2563eb;
  --tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)
}
[type=checkbox]:checked,
[type=radio]:checked {
  border-color:transparent;
  background-color:currentColor;
  background-size:100% 100%;
  background-position:50%;
  background-repeat:no-repeat
}
[type=checkbox]:checked {
  background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e")
}
@media (forced-colors:active) {
  [type=checkbox]:checked {
    -webkit-appearance:auto;
    -moz-appearance:auto;
    appearance:auto
  }
}
[type=radio]:checked {
  background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e")
}
@media (forced-colors:active) {
  [type=radio]:checked {
    -webkit-appearance:auto;
    -moz-appearance:auto;
    appearance:auto
  }
}
[type=checkbox]:checked:focus,
[type=checkbox]:checked:hover,
[type=radio]:checked:focus,
[type=radio]:checked:hover {
  border-color:transparent;
  background-color:currentColor
}
[type=checkbox]:indeterminate {
  background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color:transparent;
  background-color:currentColor;
  background-size:100% 100%;
  background-position:50%;
  background-repeat:no-repeat
}
@media (forced-colors:active) {
  [type=checkbox]:indeterminate {
    -webkit-appearance:auto;
    -moz-appearance:auto;
    appearance:auto
  }
}
[type=checkbox]:indeterminate:focus,
[type=checkbox]:indeterminate:hover {
  border-color:transparent;
  background-color:currentColor
}
[type=file] {
  background:unset;
  border-color:inherit;
  border-width:0;
  border-radius:0;
  padding:0;
  font-size:unset;
  line-height:inherit
}
[type=file]:focus {
  outline:1px solid ButtonText;
  outline:1px auto -webkit-focus-ring-color
}
.container {
  width:100%
}
@media (min-width:640px) {
  .container {
    max-width:640px
  }
}
@media (min-width:768px) {
  .container {
    max-width:768px
  }
}
@media (min-width:1024px) {
  .container {
    max-width:1024px
  }
}
@media (min-width:1280px) {
  .container {
    max-width:1280px
  }
}
@media (min-width:1536px) {
  .container {
    max-width:1536px
  }
}
.prose {
  color:var(--tw-prose-body);
  max-width:65ch
}
.prose :where(p):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:1.25em;
  margin-bottom:1.25em
}
.prose :where([class~=lead]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-lead);
  font-size:1.25em;
  line-height:1.6;
  margin-top:1.2em;
  margin-bottom:1.2em
}
.prose :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#ec4899;
  text-decoration:underline;
  font-weight:500
}
.prose :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)):hover {
  color:#db2777
}
.prose :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) code {
  color:#f472b6
}
.prose :where(strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-bold);
  font-weight:600
}
.prose :where(a strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(blockquote strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(thead th strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(ol):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:decimal;
  margin-top:1.25em;
  margin-bottom:1.25em;
  padding-inline-start:1.625em
}
.prose :where(ol[type=A]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:upper-alpha
}
.prose :where(ol[type=a]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:lower-alpha
}
.prose :where(ol[type=A s]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:upper-alpha
}
.prose :where(ol[type=a s]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:lower-alpha
}
.prose :where(ol[type=I]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:upper-roman
}
.prose :where(ol[type=i]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:lower-roman
}
.prose :where(ol[type=I s]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:upper-roman
}
.prose :where(ol[type=i s]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:lower-roman
}
.prose :where(ol[type="1"]):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:decimal
}
.prose :where(ul):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  list-style-type:disc;
  margin-top:1.25em;
  margin-bottom:1.25em;
  padding-inline-start:1.625em
}
.prose :where(ol>li):not(:where([class~=not-prose],
[class~=not-prose] *))::marker {
  font-weight:400;
  color:var(--tw-prose-counters)
}
.prose :where(ul>li):not(:where([class~=not-prose],
[class~=not-prose] *))::marker {
  color:var(--tw-prose-bullets)
}
.prose :where(dt):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:600;
  margin-top:1.25em
}
.prose :where(hr):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  border-color:var(--tw-prose-hr);
  border-top-width:1px;
  margin-top:3em;
  margin-bottom:3em
}
.prose :where(blockquote):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:500;
  font-style:italic;
  color:var(--tw-prose-quotes);
  border-inline-start-width:.25rem;
  border-inline-start-color:var(--tw-prose-quote-borders);
  quotes:"\201C""\201D""\2018""\2019";
  margin-top:1.6em;
  margin-bottom:1.6em;
  padding-inline-start:1em
}
.prose :where(blockquote p:first-of-type):not(:where([class~=not-prose],
[class~=not-prose] *)):before {
  content:open-quote
}
.prose :where(blockquote p:last-of-type):not(:where([class~=not-prose],
[class~=not-prose] *)):after {
  content:close-quote
}
.prose :where(h1):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:800;
  font-size:2.25em;
  margin-top:0;
  margin-bottom:.8888889em;
  line-height:1.1111111
}
.prose :where(h1 strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:900;
  color:inherit
}
.prose :where(h2):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:700;
  font-size:1.5em;
  margin-top:2em;
  margin-bottom:1em;
  line-height:1.3333333
}
.prose :where(h2 strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:800;
  color:inherit
}
.prose :where(h3):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:600;
  font-size:1.25em;
  margin-top:1.6em;
  margin-bottom:.6em;
  line-height:1.6
}
.prose :where(h3 strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:700;
  color:inherit
}
.prose :where(h4):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:600;
  margin-top:1.5em;
  margin-bottom:.5em;
  line-height:1.5
}
.prose :where(h4 strong):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:700;
  color:inherit
}
.prose :where(img):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:2em;
  margin-bottom:2em
}
.prose :where(picture):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  display:block;
  margin-top:2em;
  margin-bottom:2em
}
.prose :where(video):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:2em;
  margin-bottom:2em
}
.prose :where(kbd):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:500;
  font-family:inherit;
  color:var(--tw-prose-kbd);
  box-shadow:0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%),0 3px 0 rgb(var(--tw-prose-kbd-shadows)/10%);
  font-size:.875em;
  border-radius:.3125rem;
  padding-top:.1875em;
  padding-inline-end:.375em;
  padding-bottom:.1875em;
  padding-inline-start:.375em
}
.prose :where(code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#6366f1;
  font-weight:600;
  font-size:.875em
}
.prose :where(code):not(:where([class~=not-prose],
[class~=not-prose] *)):before {
  content:"`"
}
.prose :where(code):not(:where([class~=not-prose],
[class~=not-prose] *)):after {
  content:"`"
}
.prose :where(a code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(h1 code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(h2 code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit;
  font-size:.875em
}
.prose :where(h3 code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit;
  font-size:.9em
}
.prose :where(h4 code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(blockquote code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(thead th code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:inherit
}
.prose :where(pre):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-pre-code);
  background-color:var(--tw-prose-pre-bg);
  overflow-x:auto;
  font-weight:400;
  font-size:.875em;
  line-height:1.7142857;
  margin-top:1.7142857em;
  margin-bottom:1.7142857em;
  border-radius:.375rem;
  padding-top:.8571429em;
  padding-inline-end:1.1428571em;
  padding-bottom:.8571429em;
  padding-inline-start:1.1428571em
}
.prose :where(pre code):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  background-color:transparent;
  border-width:0;
  border-radius:0;
  padding:0;
  font-weight:inherit;
  color:inherit;
  font-size:inherit;
  font-family:inherit;
  line-height:inherit
}
.prose :where(pre code):not(:where([class~=not-prose],
[class~=not-prose] *)):before {
  content:none
}
.prose :where(pre code):not(:where([class~=not-prose],
[class~=not-prose] *)):after {
  content:none
}
.prose :where(table):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  width:100%;
  table-layout:auto;
  margin-top:2em;
  margin-bottom:2em;
  font-size:.875em;
  line-height:1.7142857
}
.prose :where(thead):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  border-bottom-width:1px;
  border-bottom-color:var(--tw-prose-th-borders)
}
.prose :where(thead th):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-headings);
  font-weight:600;
  vertical-align:bottom;
  padding-inline-end:.5714286em;
  padding-bottom:.5714286em;
  padding-inline-start:.5714286em
}
.prose :where(tbody tr):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  border-bottom-width:1px;
  border-bottom-color:var(--tw-prose-td-borders)
}
.prose :where(tbody tr:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  border-bottom-width:0
}
.prose :where(tbody td):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  vertical-align:baseline
}
.prose :where(tfoot):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  border-top-width:1px;
  border-top-color:var(--tw-prose-th-borders)
}
.prose :where(tfoot td):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  vertical-align:top
}
.prose :where(th,
td):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  text-align:start
}
.prose :where(figure>*):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0;
  margin-bottom:0
}
.prose :where(figcaption):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:var(--tw-prose-captions);
  font-size:.875em;
  line-height:1.4285714;
  margin-top:.8571429em
}
.prose {
  --tw-prose-body:#374151;
  --tw-prose-headings:#111827;
  --tw-prose-lead:#4b5563;
  --tw-prose-links:#111827;
  --tw-prose-bold:#111827;
  --tw-prose-counters:#6b7280;
  --tw-prose-bullets:#d1d5db;
  --tw-prose-hr:#e5e7eb;
  --tw-prose-quotes:#111827;
  --tw-prose-quote-borders:#e5e7eb;
  --tw-prose-captions:#6b7280;
  --tw-prose-kbd:#111827;
  --tw-prose-kbd-shadows:17 24 39;
  --tw-prose-code:#111827;
  --tw-prose-pre-code:#e5e7eb;
  --tw-prose-pre-bg:#1f2937;
  --tw-prose-th-borders:#d1d5db;
  --tw-prose-td-borders:#e5e7eb;
  --tw-prose-invert-body:#d1d5db;
  --tw-prose-invert-headings:#fff;
  --tw-prose-invert-lead:#9ca3af;
  --tw-prose-invert-links:#fff;
  --tw-prose-invert-bold:#fff;
  --tw-prose-invert-counters:#9ca3af;
  --tw-prose-invert-bullets:#4b5563;
  --tw-prose-invert-hr:#374151;
  --tw-prose-invert-quotes:#f3f4f6;
  --tw-prose-invert-quote-borders:#374151;
  --tw-prose-invert-captions:#9ca3af;
  --tw-prose-invert-kbd:#fff;
  --tw-prose-invert-kbd-shadows:255 255 255;
  --tw-prose-invert-code:#fff;
  --tw-prose-invert-pre-code:#d1d5db;
  --tw-prose-invert-pre-bg:rgba(0,0,0,.5);
  --tw-prose-invert-th-borders:#4b5563;
  --tw-prose-invert-td-borders:#374151;
  font-size:1rem;
  line-height:1.75
}
.prose :where(picture>img):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0;
  margin-bottom:0
}
.prose :where(li):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:.5em;
  margin-bottom:.5em
}
.prose :where(ol>li):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-start:.375em
}
.prose :where(ul>li):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-start:.375em
}
.prose :where(.prose>ul>li p):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:.75em;
  margin-bottom:.75em
}
.prose :where(.prose>ul>li>p:first-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:1.25em
}
.prose :where(.prose>ul>li>p:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-bottom:1.25em
}
.prose :where(.prose>ol>li>p:first-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:1.25em
}
.prose :where(.prose>ol>li>p:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-bottom:1.25em
}
.prose :where(ul ul,
ul ol,
ol ul,
ol ol):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:.75em;
  margin-bottom:.75em
}
.prose :where(dl):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:1.25em;
  margin-bottom:1.25em
}
.prose :where(dd):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:.5em;
  padding-inline-start:1.625em
}
.prose :where(hr+*):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0
}
.prose :where(h2+*):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0
}
.prose :where(h3+*):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0
}
.prose :where(h4+*):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0
}
.prose :where(thead th:first-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-start:0
}
.prose :where(thead th:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-end:0
}
.prose :where(tbody td,
tfoot td):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-top:.5714286em;
  padding-inline-end:.5714286em;
  padding-bottom:.5714286em;
  padding-inline-start:.5714286em
}
.prose :where(tbody td:first-child,
tfoot td:first-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-start:0
}
.prose :where(tbody td:last-child,
tfoot td:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  padding-inline-end:0
}
.prose :where(figure):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:2em;
  margin-bottom:2em
}
.prose :where(.prose>:first-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-top:0
}
.prose :where(.prose>:last-child):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  margin-bottom:0
}
.prose :where(h1,
h2):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  font-weight:700;
  letter-spacing:-.025em
}
.prose-invert {
  --tw-prose-body:var(--tw-prose-invert-body);
  --tw-prose-headings:var(--tw-prose-invert-headings);
  --tw-prose-lead:var(--tw-prose-invert-lead);
  --tw-prose-links:var(--tw-prose-invert-links);
  --tw-prose-bold:var(--tw-prose-invert-bold);
  --tw-prose-counters:var(--tw-prose-invert-counters);
  --tw-prose-bullets:var(--tw-prose-invert-bullets);
  --tw-prose-hr:var(--tw-prose-invert-hr);
  --tw-prose-quotes:var(--tw-prose-invert-quotes);
  --tw-prose-quote-borders:var(--tw-prose-invert-quote-borders);
  --tw-prose-captions:var(--tw-prose-invert-captions);
  --tw-prose-kbd:var(--tw-prose-invert-kbd);
  --tw-prose-kbd-shadows:var(--tw-prose-invert-kbd-shadows);
  --tw-prose-code:var(--tw-prose-invert-code);
  --tw-prose-pre-code:var(--tw-prose-invert-pre-code);
  --tw-prose-pre-bg:var(--tw-prose-invert-pre-bg);
  --tw-prose-th-borders:var(--tw-prose-invert-th-borders);
  --tw-prose-td-borders:var(--tw-prose-invert-td-borders)
}
.prose-invert :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#ec4899
}
.prose-invert :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)):hover {
  color:#f472b6
}
.prose-invert :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) code {
  color:#f472b6
}
.prose-invert :where(h1,
h2,
h3,
h4,
h5,
h6):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#f3f4f6
}
.sr-only {
  position:absolute;
  width:1px;
  height:1px;
  padding:0;
  margin:-1px;
  overflow:hidden;
  clip:rect(0,0,0,0);
  white-space:nowrap;
  border-width:0
}
.\!collapse {
  visibility:collapse!important
}
.collapse {
  visibility:collapse
}
.static {
  position:static
}
.fixed {
  position:fixed
}
.absolute {
  position:absolute
}
.relative {
  position:relative
}
.sticky {
  position:sticky
}
.inset-0 {
  inset:0
}
.bottom-8 {
  bottom:2rem
}
.left-0 {
  left:0
}
.right-0 {
  right:0
}
.right-2 {
  right:.5rem
}
.right-3 {
  right:.75rem
}
.right-4 {
  right:1rem
}
.right-8 {
  right:2rem
}
.top-0 {
  top:0
}
.top-2 {
  top:.5rem
}
.top-3 {
  top:.75rem
}
.top-7 {
  top:1.75rem
}
.z-50 {
  z-index:50
}
.z-60 {
  z-index:60
}
.z-70 {
  z-index:70
}
.z-80 {
  z-index:80
}
.float-left {
  float:left
}
.-m-4 {
  margin:-1rem
}
.-mx-2 {
  margin-left:-.5rem;
  margin-right:-.5rem
}
.-mx-4 {
  margin-left:-1rem;
  margin-right:-1rem
}
.-mx-6 {
  margin-left:-1.5rem;
  margin-right:-1.5rem
}
.mx-auto {
  margin-left:auto;
  margin-right:auto
}
.my-1 {
  margin-top:.25rem;
  margin-bottom:.25rem
}
.my-3 {
  margin-top:.75rem;
  margin-bottom:.75rem
}
.-ml-2 {
  margin-left:-.5rem
}
.mb-2 {
  margin-bottom:.5rem
}
.mb-3 {
  margin-bottom:.75rem
}
.mb-4 {
  margin-bottom:1rem
}
.mb-8 {
  margin-bottom:2rem
}
.mb-auto {
  margin-bottom:auto
}
.ml-6 {
  margin-left:1.5rem
}
.ml-\[calc\(-50vw\+50\%\)\] {
  margin-left:calc(-50vw + 50%)
}
.mr-2 {
  margin-right:.5rem
}
.mr-3 {
  margin-right:.75rem
}
.mr-4 {
  margin-right:1rem
}
.mr-5 {
  margin-right:1.25rem
}
.mr-\[calc\(-50vw\+50\%\)\] {
  margin-right:calc(-50vw + 50%)
}
.mt-16 {
  margin-top:4rem
}
.mt-2 {
  margin-top:.5rem
}
.mt-6 {
  margin-top:1.5rem
}
.mt-8 {
  margin-top:2rem
}
.block {
  display:block
}
.inline-block {
  display:inline-block
}
.inline {
  display:inline
}
.flex {
  display:flex
}
.table {
  display:table
}
.grid {
  display:grid
}
.contents {
  display:contents
}
.hidden {
  display:none
}
.aspect-\[2\/1\] {
  aspect-ratio:2/1
}
.h-10 {
  height:2.5rem
}
.h-16 {
  height:4rem
}
.h-48 {
  height:12rem
}
.h-5 {
  height:1.25rem
}
.h-6 {
  height:1.5rem
}
.h-7 {
  height:1.75rem
}
.h-8 {
  height:2rem
}
.h-full {
  height:100%
}
.h-screen {
  height:100vh
}
.max-h-screen {
  max-height:100vh
}
.w-10 {
  width:2.5rem
}
.w-16 {
  width:4rem
}
.w-32 {
  width:8rem
}
.w-4 {
  width:1rem
}
.w-48 {
  width:12rem
}
.w-5 {
  width:1.25rem
}
.w-6 {
  width:1.5rem
}
.w-72 {
  width:18rem
}
.w-8 {
  width:2rem
}
.w-full {
  width:100%
}
.min-w-\[280px\] {
  min-width:280px
}
.max-w-3xl {
  max-width:48rem
}
.max-w-40 {
  max-width:10rem
}
.max-w-\[280px\] {
  max-width:280px
}
.max-w-\[544px\] {
  max-width:544px
}
.max-w-lg {
  max-width:32rem
}
.max-w-md {
  max-width:28rem
}
.max-w-none {
  max-width:none
}
.max-w-xl {
  max-width:36rem
}
.basis-0 {
  flex-basis:0px
}
.origin-top-right {
  transform-origin:top right
}
.translate-x-0 {
  --tw-translate-x:0px
}
.translate-x-0,
.translate-x-full {
  transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}
.translate-x-full {
  --tw-translate-x:100%
}
.scale-100 {
  --tw-scale-x:1;
  --tw-scale-y:1
}
.scale-100,
.scale-95 {
  transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}
.scale-95 {
  --tw-scale-x:.95;
  --tw-scale-y:.95
}
.transform {
  transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}
.cursor-auto {
  cursor:auto
}
.cursor-default {
  cursor:default
}
.cursor-pointer {
  cursor:pointer
}
.grid-rows-\[auto_1fr\] {
  grid-template-rows:auto 1fr
}
.flex-row {
  flex-direction:row
}
.flex-col {
  flex-direction:column
}
.flex-wrap {
  flex-wrap:wrap
}
.items-start {
  align-items:flex-start
}
.items-center {
  align-items:center
}
.justify-start {
  justify-content:flex-start
}
.justify-end {
  justify-content:flex-end
}
.justify-center {
  justify-content:center
}
.justify-between {
  justify-content:space-between
}
.gap-3 {
  gap:.75rem
}
.gap-4 {
  gap:1rem
}
.gap-x-2 {
  -moz-column-gap:.5rem;
  column-gap:.5rem
}
.space-x-2>:not([hidden])~:not([hidden]) {
  --tw-space-x-reverse:0;
  margin-right:calc(.5rem * var(--tw-space-x-reverse));
  margin-left:calc(.5rem * calc(1 - var(--tw-space-x-reverse)))
}
.space-x-3>:not([hidden])~:not([hidden]) {
  --tw-space-x-reverse:0;
  margin-right:calc(.75rem * var(--tw-space-x-reverse));
  margin-left:calc(.75rem * calc(1 - var(--tw-space-x-reverse)))
}
.space-x-4>:not([hidden])~:not([hidden]) {
  --tw-space-x-reverse:0;
  margin-right:calc(1rem * var(--tw-space-x-reverse));
  margin-left:calc(1rem * calc(1 - var(--tw-space-x-reverse)))
}
.space-y-1>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(.25rem * var(--tw-space-y-reverse))
}
.space-y-10>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(2.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(2.5rem * var(--tw-space-y-reverse))
}
.space-y-2>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(.5rem * var(--tw-space-y-reverse))
}
.space-y-3>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(.75rem * var(--tw-space-y-reverse))
}
.space-y-5>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(1.25rem * var(--tw-space-y-reverse))
}
.space-y-6>:not([hidden])~:not([hidden]) {
  --tw-space-y-reverse:0;
  margin-top:calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom:calc(1.5rem * var(--tw-space-y-reverse))
}
.divide-y>:not([hidden])~:not([hidden]) {
  --tw-divide-y-reverse:0;
  border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width:calc(1px * var(--tw-divide-y-reverse))
}
.divide-gray-100>:not([hidden])~:not([hidden]) {
  --tw-divide-opacity:1;
  border-color:rgb(243 244 246/var(--tw-divide-opacity))
}
.divide-gray-200>:not([hidden])~:not([hidden]) {
  --tw-divide-opacity:1;
  border-color:rgb(229 231 235/var(--tw-divide-opacity))
}
.self-center {
  align-self:center
}
.overflow-auto {
  overflow:auto
}
.overflow-hidden {
  overflow:hidden
}
.overflow-x-auto {
  overflow-x:auto
}
.overflow-y-auto {
  overflow-y:auto
}
.scroll-smooth {
  scroll-behavior:smooth
}
.whitespace-nowrap {
  white-space:nowrap
}
.break-words {
  overflow-wrap:break-word
}
.rounded {
  border-radius:.25rem
}
.rounded-2xl {
  border-radius:1rem
}
.rounded-full {
  border-radius:9999px
}
.rounded-lg {
  border-radius:.5rem
}
.rounded-md {
  border-radius:.375rem
}
.border {
  border-width:1px
}
.border-2 {
  border-width:2px
}
.border-b {
  border-bottom-width:1px
}
.border-l-4 {
  border-left-width:4px
}
.border-t {
  border-top-width:1px
}
.border-gray-100 {
  --tw-border-opacity:1;
  border-color:rgb(243 244 246/var(--tw-border-opacity))
}
.border-gray-200 {
  --tw-border-opacity:1;
  border-color:rgb(229 231 235/var(--tw-border-opacity))
}
.border-gray-300 {
  --tw-border-opacity:1;
  border-color:rgb(209 213 219/var(--tw-border-opacity))
}
.border-gray-400 {
  --tw-border-opacity:1;
  border-color:rgb(156 163 175/var(--tw-border-opacity))
}
.border-green-400 {
  --tw-border-opacity:1;
  border-color:rgb(74 222 128/var(--tw-border-opacity))
}
.border-primary-500 {
  --tw-border-opacity:1;
  border-color:rgb(236 72 153/var(--tw-border-opacity))
}
.border-transparent {
  border-color:transparent
}
.border-opacity-0 {
  --tw-border-opacity:0
}
.border-opacity-60 {
  --tw-border-opacity:0.6
}
.bg-black\/25 {
  background-color:rgba(0,0,0,.25)
}
.bg-blue-600 {
  --tw-bg-opacity:1;
  background-color:rgb(37 99 235/var(--tw-bg-opacity))
}
.bg-gray-100 {
  --tw-bg-opacity:1;
  background-color:rgb(243 244 246/var(--tw-bg-opacity))
}
.bg-gray-200 {
  --tw-bg-opacity:1;
  background-color:rgb(229 231 235/var(--tw-bg-opacity))
}
.bg-gray-300\/50 {
  background-color:rgba(209,213,219,.5)
}
.bg-gray-50 {
  --tw-bg-opacity:1;
  background-color:rgb(249 250 251/var(--tw-bg-opacity))
}
.bg-gray-700 {
  --tw-bg-opacity:1;
  background-color:rgb(55 65 81/var(--tw-bg-opacity))
}
.bg-green-500 {
  --tw-bg-opacity:1;
  background-color:rgb(34 197 94/var(--tw-bg-opacity))
}
.bg-primary-500 {
  --tw-bg-opacity:1;
  background-color:rgb(236 72 153/var(--tw-bg-opacity))
}
.bg-primary-600 {
  --tw-bg-opacity:1;
  background-color:rgb(219 39 119/var(--tw-bg-opacity))
}
.bg-red-500 {
  --tw-bg-opacity:1;
  background-color:rgb(239 68 68/var(--tw-bg-opacity))
}
.bg-transparent {
  background-color:transparent
}
.bg-white {
  --tw-bg-opacity:1;
  background-color:rgb(255 255 255/var(--tw-bg-opacity))
}
.fill-current {
  fill:currentColor
}
.object-cover {
  -o-object-fit:cover;
  object-fit:cover
}
.object-center {
  -o-object-position:center;
  object-position:center
}
.p-1 {
  padding:.25rem
}
.p-2 {
  padding:.5rem
}
.p-4 {
  padding:1rem
}
.p-6 {
  padding:1.5rem
}
.px-1\.5 {
  padding-left:.375rem;
  padding-right:.375rem
}
.px-2 {
  padding-left:.5rem;
  padding-right:.5rem
}
.px-3 {
  padding-left:.75rem;
  padding-right:.75rem
}
.px-4 {
  padding-left:1rem;
  padding-right:1rem
}
.px-6 {
  padding-left:1.5rem;
  padding-right:1.5rem
}
.py-10 {
  padding-top:2.5rem;
  padding-bottom:2.5rem
}
.py-12 {
  padding-top:3rem;
  padding-bottom:3rem
}
.py-2 {
  padding-top:.5rem;
  padding-bottom:.5rem
}
.py-4 {
  padding-top:1rem;
  padding-bottom:1rem
}
.py-5 {
  padding-top:1.25rem;
  padding-bottom:1.25rem
}
.py-8 {
  padding-top:2rem;
  padding-bottom:2rem
}
.pb-1 {
  padding-bottom:.25rem
}
.pb-10 {
  padding-bottom:2.5rem
}
.pb-2 {
  padding-bottom:.5rem
}
.pb-6 {
  padding-bottom:1.5rem
}
.pb-8 {
  padding-bottom:2rem
}
.pl-12 {
  padding-left:3rem
}
.pl-4 {
  padding-left:1rem
}
.pl-\[calc\(100vw-100\%\)\] {
  padding-left:calc(100vw - 100%)
}
.pr-4 {
  padding-right:1rem
}

.pt-10 {
  padding-top:2.5rem
}
.pt-2 {
  padding-top:.5rem
}
.pt-3 {
  padding-top:.75rem
}
.pt-4 {
  padding-top:1rem
}
.pt-5 {
  padding-top:1.25rem
}
.pt-6 {
  padding-top:1.5rem
}
.pt-8 {
  padding-top:2rem
}
.text-left {
  text-align:left
}
.text-center {
  text-align:center
}
.text-right {
  text-align:right
}
.align-middle {
  vertical-align:middle
}
.font-sans {
  font-family:var(--font-space-grotesk),ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"
}
.text-2xl {
  font-size:1.5rem;
  line-height:2rem
}
.text-3xl {
  font-size:1.875rem;
  line-height:2.25rem
}
.text-6xl {
  font-size:3.75rem;
  line-height:1
}
.text-base {
  font-size:1rem;
  line-height:1.5rem
}
.text-lg {
  font-size:1.125rem;
  line-height:1.75rem
}
.text-sm {
  font-size:.875rem;
  line-height:1.25rem
}
.text-xl {
  font-size:1.25rem;
  line-height:1.75rem
}
.text-xs {
  font-size:.75rem;
  line-height:1rem
}
.font-bold {
  font-weight:700
}
.font-extrabold {
  font-weight:800
}
.font-medium {
  font-weight:500
}
.font-semibold {
  font-weight:600
}
.uppercase {
  text-transform:uppercase
}
.italic {
  font-style:italic
}
.leading-4 {
  line-height:1rem
}
.leading-5 {
  line-height:1.25rem
}
.leading-6 {
  line-height:1.5rem
}
.leading-7 {
  line-height:1.75rem
}
.leading-8 {
  line-height:2rem
}
.leading-9 {
  line-height:2.25rem
}
.leading-normal {
  line-height:1.5
}
.tracking-tight {
  letter-spacing:-.025em
}
.tracking-wide {
  letter-spacing:.025em
}
.tracking-widest {
  letter-spacing:.1em
}
.text-black {
  --tw-text-opacity:1;
  color:rgb(0 0 0/var(--tw-text-opacity))
}
.text-gray-100 {
  --tw-text-opacity:1;
  color:rgb(243 244 246/var(--tw-text-opacity))
}
.text-gray-200 {
  --tw-text-opacity:1;
  color:rgb(229 231 235/var(--tw-text-opacity))
}
.text-gray-300 {
  --tw-text-opacity:1;
  color:rgb(209 213 219/var(--tw-text-opacity))
}
.text-gray-400 {
  --tw-text-opacity:1;
  color:rgb(156 163 175/var(--tw-text-opacity))
}
.text-gray-500 {
  --tw-text-opacity:1;
  color:rgb(107 114 128/var(--tw-text-opacity))
}
.text-gray-600 {
  --tw-text-opacity:1;
  color:rgb(75 85 99/var(--tw-text-opacity))
}
.text-gray-700 {
  --tw-text-opacity:1;
  color:rgb(55 65 81/var(--tw-text-opacity))
}
.text-gray-800 {
  --tw-text-opacity:1;
  color:rgb(31 41 55/var(--tw-text-opacity))
}
.text-gray-900 {
  --tw-text-opacity:1;
  color:rgb(17 24 39/var(--tw-text-opacity))
}
.text-green-400 {
  --tw-text-opacity:1;
  color:rgb(74 222 128/var(--tw-text-opacity))
}
.text-primary-500 {
  --tw-text-opacity:1;
  color:rgb(236 72 153/var(--tw-text-opacity))
}
.text-primary-600 {
  --tw-text-opacity:1;
  color:rgb(219 39 119/var(--tw-text-opacity))
}
.text-red-500 {
  --tw-text-opacity:1;
  color:rgb(239 68 68/var(--tw-text-opacity))
}
.text-white {
  --tw-text-opacity:1;
  color:rgb(255 255 255/var(--tw-text-opacity))
}
.antialiased {
  -webkit-font-smoothing:antialiased;
  -moz-osx-font-smoothing:grayscale
}
.placeholder-gray-400::-moz-placeholder {
  --tw-placeholder-opacity:1;
  color:rgb(156 163 175/var(--tw-placeholder-opacity))
}
.placeholder-gray-400::placeholder {
  --tw-placeholder-opacity:1;
  color:rgb(156 163 175/var(--tw-placeholder-opacity))
}
.opacity-0 {
  opacity:0
}
.opacity-100 {
  opacity:1
}
.opacity-95 {
  opacity:.95
}
.shadow {
  --tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);
  --tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color)
}
.shadow,
.shadow-lg {
  box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)
}
.shadow-lg {
  --tw-shadow:0 10px 15px -3px rgba(0,0,0,.1),0 4px 6px -4px rgba(0,0,0,.1);
  --tw-shadow-colored:0 10px 15px -3px var(--tw-shadow-color),0 4px 6px -4px var(--tw-shadow-color)
}
.shadow-md {
  --tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);
  --tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color)
}
.shadow-md,
.shadow-sm {
  box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)
}
.shadow-sm {
  --tw-shadow:0 1px 2px 0 rgba(0,0,0,.05);
  --tw-shadow-colored:0 1px 2px 0 var(--tw-shadow-color)
}
.outline {
  outline-style:solid
}
.outline-0 {
  outline-width:0
}
.ring-1 {
  --tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)
}
.ring-black {
  --tw-ring-opacity:1;
  --tw-ring-color:rgb(0 0 0/var(--tw-ring-opacity))
}
.ring-opacity-5 {
  --tw-ring-opacity:0.05
}
.filter {
  filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}
.backdrop-blur {
  --tw-backdrop-blur:blur(8px)
}
.backdrop-blur,
.backdrop-filter {
  -webkit-backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter:var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia)
}
.transition {
  transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;
  transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;
  transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;
  transition-timing-function:cubic-bezier(.4,0,.2,1);
  transition-duration:.15s
}
.transition-all {
  transition-property:all;
  transition-timing-function:cubic-bezier(.4,0,.2,1);
  transition-duration:.15s
}
.transition-colors {
  transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;
  transition-timing-function:cubic-bezier(.4,0,.2,1);
  transition-duration:.15s
}
.duration-100 {
  transition-duration:.1s
}
.duration-150 {
  transition-duration:.15s
}
.duration-200 {
  transition-duration:.2s
}
.duration-300 {
  transition-duration:.3s
}
.duration-75 {
  transition-duration:75ms
}
.ease-in {
  transition-timing-function:cubic-bezier(.4,0,1,1)
}
.ease-in-out {
  transition-timing-function:cubic-bezier(.4,0,.2,1)
}
.ease-out {
  transition-timing-function:cubic-bezier(0,0,.2,1)
}
.task-list-item:before {
  display:none
}
.task-list-item {
  list-style-type:none
}
.footnotes {
  margin-top:3rem;
  border-top-width:1px;
  --tw-border-opacity:1;
  border-color:rgb(229 231 235/var(--tw-border-opacity));
  padding-top:2rem
}
.footnotes:is(.dark *) {
  --tw-border-opacity:1;
  border-color:rgb(55 65 81/var(--tw-border-opacity))
}
.data-footnote-backref {
  text-decoration-line:none
}
.csl-entry {
  margin-top:1.25rem;
  margin-bottom:1.25rem
}
.no-scrollbar::-webkit-scrollbar {
  display:none
}
.no-scrollbar {
  -ms-overflow-style:none;
  scrollbar-width:none
}
input:-webkit-autofill,
input:-webkit-autofill:focus {
  -webkit-transition:background-color 600000s 0s,color 600000s 0s;
  transition:background-color 600000s 0s,color 600000s 0s
}
.katex-display {
  overflow:auto hidden
}
.content-header-link {
  opacity:0;
  margin-left:-24px;
  padding-right:4px
}
.content-header-link:hover,
.content-header:hover .content-header-link {
  opacity:1
}
.linkicon {
  display:inline-block;
  vertical-align:middle
}
.dark\:prose-invert:is(.dark *) {
  --tw-prose-body:var(--tw-prose-invert-body);
  --tw-prose-headings:var(--tw-prose-invert-headings);
  --tw-prose-lead:var(--tw-prose-invert-lead);
  --tw-prose-links:var(--tw-prose-invert-links);
  --tw-prose-bold:var(--tw-prose-invert-bold);
  --tw-prose-counters:var(--tw-prose-invert-counters);
  --tw-prose-bullets:var(--tw-prose-invert-bullets);
  --tw-prose-hr:var(--tw-prose-invert-hr);
  --tw-prose-quotes:var(--tw-prose-invert-quotes);
  --tw-prose-quote-borders:var(--tw-prose-invert-quote-borders);
  --tw-prose-captions:var(--tw-prose-invert-captions);
  --tw-prose-kbd:var(--tw-prose-invert-kbd);
  --tw-prose-kbd-shadows:var(--tw-prose-invert-kbd-shadows);
  --tw-prose-code:var(--tw-prose-invert-code);
  --tw-prose-pre-code:var(--tw-prose-invert-pre-code);
  --tw-prose-pre-bg:var(--tw-prose-invert-pre-bg);
  --tw-prose-th-borders:var(--tw-prose-invert-th-borders);
  --tw-prose-td-borders:var(--tw-prose-invert-td-borders)
}
.dark\:prose-invert:is(.dark *) :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#ec4899
}
.dark\:prose-invert:is(.dark *) :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)):hover {
  color:#f472b6
}
.dark\:prose-invert:is(.dark *) :where(a):not(:where([class~=not-prose],
[class~=not-prose] *)) code {
  color:#f472b6
}
.dark\:prose-invert:is(.dark *) :where(h1,
h2,
h3,
h4,
h5,
h6):not(:where([class~=not-prose],
[class~=not-prose] *)) {
  color:#f3f4f6
}
.hover\:bg-blue-700:hover {
  --tw-bg-opacity:1;
  background-color:rgb(29 78 216/var(--tw-bg-opacity))
}
.hover\:bg-gray-300:hover {
  --tw-bg-opacity:1;
  background-color:rgb(209 213 219/var(--tw-bg-opacity))
}
.hover\:bg-primary-700:hover {
  --tw-bg-opacity:1;
  background-color:rgb(190 24 93/var(--tw-bg-opacity))
}
.hover\:text-gray-600:hover {
  --tw-text-opacity:1;
  color:rgb(75 85 99/var(--tw-text-opacity))
}
.hover\:text-primary-500:hover {
  --tw-text-opacity:1;
  color:rgb(236 72 153/var(--tw-text-opacity))
}
.hover\:text-primary-600:hover {
  --tw-text-opacity:1;
  color:rgb(219 39 119/var(--tw-text-opacity))
}
.focus\:border-green-400:focus {
  --tw-border-opacity:1;
  border-color:rgb(74 222 128/var(--tw-border-opacity))
}
.focus\:border-primary-500:focus {
  --tw-border-opacity:1;
  border-color:rgb(236 72 153/var(--tw-border-opacity))
}
.focus\:border-transparent:focus {
  border-color:transparent
}
.focus\:outline-none:focus {
  outline:2px solid transparent;
  outline-offset:2px
}
.focus\:ring-2:focus {
  --tw-ring-offset-shadow:var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow:var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow:var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow,0 0 #0000)
}
.focus\:ring-primary-500:focus {
  --tw-ring-opacity:1;
  --tw-ring-color:rgb(236 72 153/var(--tw-ring-opacity))
}
.focus\:ring-primary-600:focus {
  --tw-ring-opacity:1;
  --tw-ring-color:rgb(219 39 119/var(--tw-ring-opacity))
}
.focus\:ring-offset-2:focus {
  --tw-ring-offset-width:2px
}
.disabled\:opacity-50:disabled {
  opacity:.5
}
.dark\:divide-gray-700:is(.dark *)>:not([hidden])~:not([hidden]) {
  --tw-divide-opacity:1;
  border-color:rgb(55 65 81/var(--tw-divide-opacity))
}
.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity:1;
  border-color:rgb(55 65 81/var(--tw-border-opacity))
}
.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity:1;
  border-color:rgb(31 41 55/var(--tw-border-opacity))
}
.dark\:border-gray-900:is(.dark *) {
  --tw-border-opacity:1;
  border-color:rgb(17 24 39/var(--tw-border-opacity))
}
.dark\:bg-black:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(0 0 0/var(--tw-bg-opacity))
}
.dark\:bg-black\/50:is(.dark *) {
  background-color:rgba(0,0,0,.5)
}
.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(55 65 81/var(--tw-bg-opacity))
}
.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(31 41 55/var(--tw-bg-opacity))
}
.dark\:bg-gray-900:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(17 24 39/var(--tw-bg-opacity))
}
.dark\:bg-gray-900\/70:is(.dark *) {
  background-color:rgba(17,24,39,.7)
}
.dark\:bg-gray-950:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(3 7 18/var(--tw-bg-opacity))
}
.dark\:text-gray-100:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(243 244 246/var(--tw-text-opacity))
}
.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(229 231 235/var(--tw-text-opacity))
}
.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(209 213 219/var(--tw-text-opacity))
}
.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(156 163 175/var(--tw-text-opacity))
}
.dark\:text-gray-600:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(75 85 99/var(--tw-text-opacity))
}
.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(248 113 113/var(--tw-text-opacity))
}
.dark\:text-white:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(255 255 255/var(--tw-text-opacity))
}
.dark\:placeholder-gray-500:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity:1;
  color:rgb(107 114 128/var(--tw-placeholder-opacity))
}
.dark\:placeholder-gray-500:is(.dark *)::placeholder {
  --tw-placeholder-opacity:1;
  color:rgb(107 114 128/var(--tw-placeholder-opacity))
}
.dark\:opacity-\[0\.98\]:is(.dark *) {
  opacity:.98
}
.dark\:shadow-gray-800\/40:is(.dark *) {
  --tw-shadow-color:rgba(31,41,55,.4);
  --tw-shadow:var(--tw-shadow-colored)
}
.dark\:ring-offset-black:is(.dark *) {
  --tw-ring-offset-color:#000
}
.dark\:hover\:bg-blue-500:hover:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(59 130 246/var(--tw-bg-opacity))
}
.dark\:hover\:bg-gray-600:hover:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(75 85 99/var(--tw-bg-opacity))
}
.dark\:hover\:bg-primary-400:hover:is(.dark *) {
  --tw-bg-opacity:1;
  background-color:rgb(244 114 182/var(--tw-bg-opacity))
}
.dark\:hover\:text-primary-400:hover:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(244 114 182/var(--tw-text-opacity))
}
.dark\:hover\:text-primary-500:hover:is(.dark *) {
  --tw-text-opacity:1;
  color:rgb(236 72 153/var(--tw-text-opacity))
}
@media (min-width:640px) {
  .sm\:ml-3 {
    margin-left:.75rem
  }
  .sm\:mt-0 {
    margin-top:0
  }
  .sm\:block {
    display:block
  }
  .sm\:flex {
    display:flex
  }
  .sm\:hidden {
    display:none
  }
  .sm\:w-96 {
    width:24rem
  }
  .sm\:flex-row {
    flex-direction:row
  }
  .sm\:justify-between {
    justify-content:space-between
  }
  .sm\:space-x-12>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse:0;
    margin-right:calc(3rem * var(--tw-space-x-reverse));
    margin-left:calc(3rem * calc(1 - var(--tw-space-x-reverse)))
  }
  .sm\:space-x-24>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse:0;
    margin-right:calc(6rem * var(--tw-space-x-reverse));
    margin-left:calc(6rem * calc(1 - var(--tw-space-x-reverse)))
  }
  .sm\:space-x-6>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse:0;
    margin-right:calc(1.5rem * var(--tw-space-x-reverse));
    margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
  }
  .sm\:px-14 {
    padding-left:3.5rem;
    padding-right:3.5rem
  }
  .sm\:px-6 {
    padding-left:1.5rem;
    padding-right:1.5rem
  }
  .sm\:py-0 {
    padding-top:0;
    padding-bottom:0
  }
  .sm\:py-8 {
    padding-top:2rem;
    padding-bottom:2rem
  }
  .sm\:text-4xl {
    font-size:2.25rem;
    line-height:2.5rem
  }
  .sm\:text-base {
    font-size:1rem;
    line-height:1.5rem
  }
  .sm\:leading-10 {
    line-height:2.5rem
  }
}
@media (min-width:768px) {
  .md\:-mx-8 {
    margin-left:-2rem;
    margin-right:-2rem
  }
  .md\:mt-24 {
    margin-top:6rem
  }
  .md\:flex {
    display:flex
  }
  .md\:hidden {
    display:none
  }
  .md\:h-36 {
    height:9rem
  }
  .md\:w-1\/2 {
    width:50%
  }
  .md\:max-w-72 {
    max-width:18rem
  }
  .md\:flex-row {
    flex-direction:row
  }
  .md\:items-center {
    align-items:center
  }
  .md\:justify-center {
    justify-content:center
  }
  .md\:space-x-6>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse:0;
    margin-right:calc(1.5rem * var(--tw-space-x-reverse));
    margin-left:calc(1.5rem * calc(1 - var(--tw-space-x-reverse)))
  }
  .md\:space-y-5>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse:0;
    margin-top:calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom:calc(1.25rem * var(--tw-space-y-reverse))
  }
  .md\:divide-y-0>:not([hidden])~:not([hidden]) {
    --tw-divide-y-reverse:0;
    border-top-width:calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width:calc(0px * var(--tw-divide-y-reverse))
  }
  .md\:border-r-2 {
    border-right-width:2px
  }
  .md\:px-6 {
    padding-left:1.5rem;
    padding-right:1.5rem
  }
  .md\:text-2xl {
    font-size:1.5rem;
    line-height:2rem
  }
  .md\:text-5xl {
    font-size:3rem;
    line-height:1
  }
  .md\:text-6xl {
    font-size:3.75rem;
    line-height:1
  }
  .md\:text-8xl {
    font-size:6rem;
    line-height:1
  }
  .md\:leading-14 {
    line-height:3.5rem
  }
}
@media (min-width:1024px) {
  .lg\:h-48 {
    height:12rem
  }
  .lg\:max-w-96 {
    max-width:24rem
  }
}
@media (min-width:1280px) {
  .xl\:col-span-2 {
    grid-column:span 2/span 2
  }
  .xl\:col-span-3 {
    grid-column:span 3/span 3
  }
  .xl\:col-start-1 {
    grid-column-start:1
  }
  .xl\:row-span-2 {
    grid-row:span 2/span 2
  }
  .xl\:row-start-2 {
    grid-row-start:2
  }
  .xl\:-mx-2 {
    margin-left:-.5rem;
    margin-right:-.5rem
  }
  .xl\:my-1 {
    margin-top:.25rem;
    margin-bottom:.25rem
  }
  .xl\:block {
    display:block
  }
  .xl\:grid {
    display:grid
  }
  .xl\:w-1\/2 {
    width:50%
  }
  .xl\:max-w-5xl {
    max-width:64rem
  }
  .xl\:grid-cols-3 {
    grid-template-columns:repeat(3,minmax(0,1fr))
  }
  .xl\:grid-cols-4 {
    grid-template-columns:repeat(4,minmax(0,1fr))
  }
  .xl\:items-baseline {
    align-items:baseline
  }
  .xl\:gap-x-6 {
    -moz-column-gap:1.5rem;
    column-gap:1.5rem
  }
  .xl\:gap-x-8 {
    -moz-column-gap:2rem;
    column-gap:2rem
  }
  .xl\:space-x-0>:not([hidden])~:not([hidden]) {
    --tw-space-x-reverse:0;
    margin-right:calc(0px * var(--tw-space-x-reverse));
    margin-left:calc(0px * calc(1 - var(--tw-space-x-reverse)))
  }
  .xl\:space-y-0>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse:0;
    margin-top:calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom:calc(0px * var(--tw-space-y-reverse))
  }
  .xl\:space-y-8>:not([hidden])~:not([hidden]) {
    --tw-space-y-reverse:0;
    margin-top:calc(2rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom:calc(2rem * var(--tw-space-y-reverse))
  }
  .xl\:divide-y>:not([hidden])~:not([hidden]) {
    --tw-divide-y-reverse:0;
    border-top-width:calc(1px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width:calc(1px * var(--tw-divide-y-reverse))
  }
  .xl\:divide-y-0>:not([hidden])~:not([hidden]) {
    --tw-divide-y-reverse:0;
    border-top-width:calc(0px * calc(1 - var(--tw-divide-y-reverse)));
    border-bottom-width:calc(0px * var(--tw-divide-y-reverse))
  }
  .xl\:divide-gray-200>:not([hidden])~:not([hidden]) {
    --tw-divide-opacity:1;
    border-color:rgb(229 231 235/var(--tw-divide-opacity))
  }
  .xl\:border-b {
    border-bottom-width:1px
  }
  .xl\:border-gray-200 {
    --tw-border-opacity:1;
    border-color:rgb(229 231 235/var(--tw-border-opacity))
  }
  .xl\:px-0 {
    padding-left:0;
    padding-right:0
  }
  .xl\:px-2 {
    padding-left:.5rem;
    padding-right:.5rem
  }
  .xl\:py-8 {
    padding-top:2rem;
    padding-bottom:2rem
  }
  .xl\:pb-0 {
    padding-bottom:0
  }
  .xl\:pb-6 {
    padding-bottom:1.5rem
  }
  .xl\:pt-11 {
    padding-top:2.75rem
  }
  .xl\:pt-8 {
    padding-top:2rem
  }
  .xl\:dark\:divide-gray-700:is(.dark *)>:not([hidden])~:not([hidden]) {
    --tw-divide-opacity:1;
    border-color:rgb(55 65 81/var(--tw-divide-opacity))
  }
  .xl\:dark\:border-gray-700:is(.dark *) {
    --tw-border-opacity:1;
    border-color:rgb(55 65 81/var(--tw-border-opacity))
  }
}
@media (min-width:1536px) {
  .\32xl\:-mx-24 {
    margin-left:-6rem;
    margin-right:-6rem
  }
}
