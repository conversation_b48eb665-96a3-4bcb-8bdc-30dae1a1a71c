about = "Пра сайт"
add_category = "Дадаць катэгорыю"
add_content = "Дадаць артыкул"
add_link = "Дадаць спасылку"
add_menu = "Дадаць меню"
add_new_page = "Дадаць новую старонку"
add_new_post = "Дадаць новы артыкул"
add_source_link_optional = "Дадаць спасылку на крыніцу (неабавязкова)"
add_sub = "Дадаць падстаронку"
address_url = "Адрас (URL)"
admin = "Адміністратар"
admin_panel_style_based_on = "Стыль адмін-панэлі заснаваны на"
all_blog_posts = "Усе артыкулы"
all_cache_has_been_deleted = "Увесь кэш быў ачышчаны!"
all_posts_tagged = "Усе артыкулы з пазнакай"
archive_for = "Архіў за"
archive_page_for = "Архіўная старонка для"
archives = "Архівы"
are_you_sure_you_want_to_delete_ = "Вы ўпэўнены, што жадаеце выдаліць <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Зараз вы карыстаецеся аўтаматычна згенераваным меню"
audio_post = "Артыкул з аўдыё"
audio_post_comment = "Стварыць артыкул з аўдыё"
author = "Аўтар"
author_description = "Яшчэ адзін карыстальнік HTMLy"
back_to = "Вярнуцца да"
backup = "Рэзервовае капіраванне"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Раскажыце больш пра свой блог (адзін абзац)"
blog_theme = "Тэма блогу"
blog_title = "Загаловак блогу"
blog_title_placeholder = "Мой блог на HTMLy"
blog_posts_displayed_as = "Артыкулы ў блогу адлюстроўваюцца як"
breadcrumb_home_text = "Тэкст для галоўнай старонкі ў хлебных крошках"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Карыстаючыся гэтым імпарцёрам, вы пацвярджаеце, што стужка належыць вам ці што вы маеце права на яе публікацыю"
css_class_optional = "CSS клас (неабавязкова)"
cache_expiration = "Тэрмін дзеяння кэшу (у гадзінах)"
cache_off = "Кэш адключаны"
cache_timestamp = "Часовая адзнака кэшу"
cancel = "Скасаваць"
cannot_read_feed_content = "Немагчыма прачытаць змесціва стужкі"
captcha_error = "reCaptcha няправільная"
categories = "Катэгорыі"
category = "Катэгорыя"
check_update = "Праверыць абнаўленні"
clear_cache = "Ачысціць кэш"
comma_separated_values = "Значэнні праз коску"
comment_system = "Абярыце сістэму каментарыяў"
comments = "Каментарыі"
config = "Канфігурацыя"
congrats_you_have_the_latest_version_of_htmly = "Віншуем! У вас апошняя версія HTMLy"
content = "Змест"
contents = "Змесціва"
copyright_line = "Аўтарскія правы"
copyright_line_placeholder = "(c) Ваша імя"
create_backup = "Стварыць рэзервовую копію"
created = "Створана"
custom = "Карыстальніцкае"
custom_settings = "Карыстальніцкія налады"
dashboard = "Панэль кіравання"
date = "Дата"
date_format = "Фармат даты"
delete = "Выдаліць"
description = "Апісанне"
disable = "Адключыць"
disabled = "Адключана"
disqus_shortname = "Імя Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Чарнавік"
edit = "Рэдагаваць"
edit_category = "Рэдагаваць катэгорыю"
edit_post = "Рэдагаваць"
edit_profile = "Рэдагаваць профіль"
enable = "Уключыць"
enable_blog_url = "Уключыць спасылку blog"
enter_image_url = "Увядзіце URL выявы"
facebook_app_id = "Facebook App ID"
facebook_page = "Старонка Facebook"
featured_audio = "Аўдыё"
featured_image = "Выява"
featured_link = "Спасылка"
featured_quote = "Цытата"
featured_video = "Відэа"
feed_url = "Адрас стужкі"
filename = "Імя файла"
follow = "Падпісацца"
for_google_site_verification_meta = "Для google-site-verification (meta-тэг)"
for_msvalidate_01_meta = "Для msvalidate.01 (meta-тэг)"
front_page_displays = "Адлюстроўваць на галоўнай старонцы"
full_post = "Увесь артыкул"
general = "Агульнае"
general_settings = "Агульныя налады"
get_one_here = "Атрымаць тут"
github_pre_release = "Папярэдні выпуск на Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (устарэлы)"
google_search_console = "Google Search Console"
home = "Галоўная"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Калі пакінуць пустым, вытрымка будзе з асноўнага тэксту"
if_the_url_is_left_empty_we_will_use_the_page_title = "Калі адрас пакінуць пустым, будзе выкарыстаны загаловак старонкі"
if_the_url_is_left_empty_we_will_use_the_post_title = "Калі адрас пакінуць пустым, будзе выкарыстаны загаловак артыкула"
image_post = "Артыкул з выявай"
image_post_comment = "Стварыць артыкул з выявай"
import = "Імпарт"
import_feed = "Запусціць імпарт стужкі"
import_rss = "Імпарт RSS"
import_rss_feed_2.0 = "Імпарт RSS Feed 2.0"
insert_image = "Уставіць выяву"
invalid_error = "ПАМЫЛКА: Няправільнае імя карыстальніка ці пароль"
language = "Мова сістэмы"
link_name = "Назва спасылкі"
link_post = "Артыкул са спасылкай"
link_post_comment = "Стварыць артыкул са спасылкай"
login = "Увайсці"
login_page = "Старонка ўваходу"
logout = "Выйсці"
menu = "Меню"
menus = "Рэдактар меню"
meta_description = "Мета-апісанне"
meta_description_character = "Колькасць сімвалаў у мета-апісанні"
metatags = "Мета-тэгі"
metatags_settings = "Налады мета-тэгаў"
mine = "Маё"
more = "Больш"
my_draft = "Мой чарнавік"
my_posts = "Мае артыкулы"
name = "Імя"
newer = "Навейшае"
next = "Наступнае"
next_post = "Наступны артыкул"
no_available_backup = "Рэзервовыя копіі адсутнічаюць"
no_draft_found = "Чарнавікі не знойдзены"
no_newer_posts = "Няма новых артыкулаў"
no_posts_found = "Артыкулы не знойдзены"
no_related_post_found = "Падобныя артыкулы не знойдзены"
no_scheduled_posts_found = "Запланаваныя артыкулы не знойдзены!"
no_search_results = "Няма вынікаў пошуку"
nope = "Не"
not = "Не"
older = "Старэйшае"
only = "Толькі"
operations = "Аперацыі"
page = "Старонка"
page_generation_time = "Час генерацыі старонкі"
pages = "Старонкі"
pass_error = "Увядзіце пароль"
password = "Пароль"
performance = "Прадукцыйнасць"
performance_settings = "Налады прадукцыйнасці"
permalink = "Пастаянная спасылка"
popular = "Папулярнае"
popular_posts = "Папулярныя артыкулы"
popular_posts_widget = "Віджэт папулярных артыкулаў"
popular_posts_widget_at_most = "Максімальная колькасць папулярных артыкулаў"
popular_tags = "Папулярныя пазнакі"
post_by_author = "Артыкулы аўтара"
posted_in = "Апублікавана ў"
posted_on = "Апублікавана"
posts = "Артыкулы"
posts_by = "Артыкулы аўтара"
posts_draft = "Чарнавікі"
posts_in_archive_page_at_most = "Колькасць артыкулаў на архіўных старонках, не больш"
posts_in_category_page_at_most = "Колькасць артыкулаў на старонцы катэгорый, не больш"
posts_in_front_page_show_at_most = "Колькасць артыкулаў на галоўнай, не больш"
posts_in_profile_page_at_most = "Колькасць артыкулаў на старонках профілю карыстальніка, не больш"
posts_in_search_result_at_most = "Колькасць артыкулаў у выніках пошуку, не больш"
posts_in_tag_page_at_most = "Колькасць артыкулаў на старонках для кожнай пазнакі, не больш"
posts_in_type_page_at_most = "Колькасць артыкулаў на старонках для кожнага тыпу запісаў, не больш"
posts_index_settings = "Налады колькасці артыкулаў"
posts_list = "Спіс артыкулаў"
posts_tagged = "Артыкулы з пазнакай"
posts_with_type = "Артыкулы з тыпам"
pre_release = "Папярэдні выпуск"
prev = "Назад"
prev_post = "Папярэдні артыкул"
preview = "Перадпрагляд"
profile_for = "Профіль для"
proudly_powered_by = "Працуе на"
publish = "Апублікаваць"
publish_draft = "Апублікаваць чарнавік"
published = "Апублікавана"
quote_post = "Артыкул з цытатай"
quote_post_comment = "Стварыць артыкул з цытатай"
rss_character = "Колькасць сімвалаў у RSS"
rss_feeds_show_the_most_recent = "Колькасць апошніх навін у RSS-стужцы"
rss_settings = "Налады RSS"
read_more_text = "Тэкст для «Чытаць далей»"
read_more_text_placeholder = "Падрабязней"
reading = "Чытанне"
reading_settings = "Налады чытання"
recaptcha = "reCAPTCHA"
recent_posts = "Новыя артыкулы"
recent_posts_widget_at_most = "Колькасць новых артыкулаў, не больш"
regular_post = "Звычайны артыкул"
regular_post_comment = "Стварыць звычайны артыкул"
related_posts = "Падобныя артыкулы"
related_widget_posts_at_most = "Колькасць падобных артыкулаў, не больш"
revert_to_draft = "Пераключыць у чарнавік"
save = "Захаваць"
save_config = "Захаваць налады"
save_edit = "Захаваць"
save_menu = "Захаваць меню"
save_as_draft = "Захаваць як чарнавік"
save_category = "Захаваць катэгорыю"
scheduled = "Запланавана"
scheduled_posts = "Запланаваныя артыкулы"
scheduled_tips = "Калі вы публікуеце артыкул з будучай датай або часам, ён трапляе ў спіс запланаваных"
search = "Пошук"
search_for = "Шукаць"
search_results_for = "Вынікі пошуку для"
search_results_not_found = "Па вашым запыце нічога не знойдзена!"
secret_key = "Сакрэтны ключ"
settings = "Налады"
sign_in_to_start_your_session = "Увайдзіце, каб пачаць сесію"
site_key = "Ключ сайта"
sitemap = "Карта сайта"
slug = "ЧПС"
social_media = "Сацыяльныя сеткі"
static_page = "Статычная старонка"
static_page_comment = "Стварыць статычную старонку"
static_pages = "Статычныя старонкі"
summary = "Вытрымка"
summary_character = "Колькасць сімвалаў у вытрымцы"
tag = "Пазнака"
tagcloud_widget_at_most = "TagCloud — не больш"
tagline = "Падзагаловак"
tagline_placeholder = "Платформа для блогаў на PHP без базы дадзеных"
tagline_description = "У некалькіх словах раскажыце, пра што гэты блог"
tags = "Пазнакі"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Гэта састарэлы код. Звычайна новая аналітыка ствараецца з выкарыстаннем gtag.js"
this_page_doesnt_exist = "Гэтая старонка не існуе!"
time = "Час"
timezone = "Часавы пояс"
title = "Загаловак"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Каб выкарыстоўваць каментары Disqus або Facebook, неабходна задаць Disqus shortname або Facebook App ID."
token_error = "Няправільны токен"
tools = "Інструменты"
twitter_account = "Акаўнт у Twitter"
type_to_search = "Пачніце ўводзіць для пошуку"
uncategorized = "Без катэгорыі"
uncategorized_comment = "Артыкулы, якія не маюць патрэбы ў катэгорыі або не падыходзяць ні пад адну існуючую."
universal_analytics = "Універсальная (gtag.js)"
unknown_feed_format = "Невядомы фармат стужкі"
update = "Абнавіць"
update_available = "Даступна абнаўленне"
update_draft = "Абнавіць чарнавік"
update_post = "Абнавіць артыкул"
update_to = "Абнавіць да"
upload = "Загрузіць"
user = "Карыстальнік"
user_error = "Увядзіце імя карыстальніка"
valid_values_range_from_0_to_1.0._see = "Допушчальныя значэнні — ад 0.0 да 1.0. Глядзіце: "
video_post = "Артыкул з відэа"
video_post_comment = "Стварыць артыкул з відэа"
view = "Прагляд"
view_post = "Прагляд артыкула"
views = "Прагляды"
widget = "Віджэт"
widget_settings = "Кіраванне віджэтамі"
would_you_like_to_try_our = "Жадаеце паспрабаваць наш "
yes_im_in = "Так, я з вамі"
yes_not_recommended = "Так (не рэкамендуецца)"
you_dont_have_permission_to_access_this_page = "У вас няма доступу да гэтай старонкі"
your_new_config_key = "Ваш новы ключ канфігурацыі"
your_new_value = "Значэнне ключа"
your_backups = "Вашы рэзервовыя копіі"
your_latest_blog_posts = "Вашы апошнія артыкулы"
your_recent_posts = "Вашы нядаўнія артыкулы"
by = "ад"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>парада:</u> каб знайсці канфігурацыйны ключ або значэнне, скарыстайцеся спалучэннем клавіш <code>Ctrl</code>/<code>CMD</code> + <code>F</code>"
homepage = "галоўная"
instead = "замест"
item_class = "Дадайце CSS клас"
item_slug = "Увядзіце ЧПС (slug)"
now = "зараз"
of = "з"
optional = "неабавязкова"
post_your_post_slug = "/post/пастаянная-спасылка-на-артыкул"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>парада:</u> вы можаце стварыць уласны канфігурацыйны ключ і выводзіць яго значэнне ў любым месцы шаблона"
read_more = "Чытаць далей"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/год/месяц/пастаянная-спасылка-на-артыкул"
your_key = "ваш.ключ"
summary_behavior = "Паводзіны вытрымкі"
default = "Па змаўчанні"
check_shortcode = "Праверыць шорткод"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "У рэжыме вытрымкі: ці правяраць спачатку шорткод перад абразаннем зместу да x знакаў"
manage_users = "Кіраванне карыстальнікамі"
add_user = "Дадаць карыстальніка"
username = "Імя карыстальніка"
role = "Роля"
change_password = "Змяніць пароль"
config_mfa = "Наладзіць MFA"
mfacode = "Код MFA"
verify_code = "Праверыць код MFA"
verify_password = "Праверыць дзеючы пароль"
manualsetupkey = "Вы можаце таксама ўвесці ключ уручную"
mfa_error = "Код MFA няправільны"
disablemfa = "Адключыць MFA"
enable_auto_save = "Уключыць аўтазахаванне"
explain_autosave = "Калі ўключана, новы змест або чарнавік будзе захоўвацца аўтаматычна кожныя 60 секунд."
login_protect_system = "Сістэма абароны ўваходу"
cloudflare_info = "Глядзіце дакументацыю Cloudflare аб Turnstile: "
mfa_config = "Шматфактарная аўтэнтыфікацыя (MFA)"
set_mfa_globally = "Усталяваць статус MFA"
explain_mfa = "Калі ўключана, MFA даступна ўсім карыстальнікам. Калі выключана, MFA не выкарыстоўваецца і поле не паказваецца на старонцы ўваходу."
set_version_publicly = "Бачнасць версіі"
explain_version = "Па змаўчанні версія HTMLy бачная ў зыходным кодзе, але некаторыя адміністратары могуць пажадаць яе схаваць."
focus_mode = "Рэжым фокусу"
writing = "Напісанне"
writing_settings = "Налады напісання"
security = "Бяспека"
security_settings = "Налады бяспекі"
msg_error_field_req_username = "Поле «Імя карыстальніка» абавязковае."
msg_error_field_req_password = "Поле «Пароль» абавязковае."
msg_error_field_req_title = "Поле «Загаловак» абавязковае."
msg_error_field_req_content = "Поле «Змест» абавязковае."
msg_error_field_req_tag = "Поле «Пазнака» абавязковае."
msg_error_field_req_image = "Поле «Выява» абавязковае."
msg_error_field_req_video = "Поле «Відэа» абавязковае."
msg_error_field_req_link = "Поле «Спасылка» абавязковае."
msg_error_field_req_quote = "Поле «Цытата» абавязковае."
msg_error_field_req_audio = "Поле «Аўдыё» абавязковае."
msg_error_field_req_feedurl = "Трэба задаць URL стужкі."
rss_feeds_description_select = "Апісанне RSS"
rss_description_body = "Цела артыкула"
rss_description_meta = "Мета-апісанне артыкула"
admin_theme = "Тэма адміністратара"
admin_theme_light = "Светлая"
admin_theme_dark = "Цёмная"
search_index = "Індэкс пошуку"
fulltext_search = "Поўнатэкставы пошук"
add_search_index = "Дадаць артыкулы ў індэкс пошуку"
clear_search_index = "Ачысціць індэкс пошуку"
unindexed_posts = "Гэтыя артыкулы не былі праіндэксаваны"
indexed_posts = "Артыкулы праіндэксаваны"
custom_fields = "Карыстальніцкія палі"
