/*
Theme Name: Twenty Fifteen
Description: Global Styles for older IE versions (previous to IE9).
*/

body,
button,
input,
select,
textarea {
	font-size: 19px;
	line-height: 1.6842;
}

button,
input {
	line-height: normal;
}

p,
address,
pre,
hr,
ul,
ol,
dl,
dd,
table {
	margin-bottom: 1.6842em;
}

ul,
ol {
	margin-left: 0;
}

li > ul,
li > ol,
blockquote > ul,
blockquote > ol {
	margin-left: 1.3333em;
}

blockquote {
	border-color: inherit;
	border-style: solid;
	border-width: 0 0 0 4px;
	font-size: 22px;
	line-height: 1.8182;
	margin-bottom: 1.8182em;
	margin-left: -1.0909em;
	padding-left: 0.9091em;
}

blockquote > blockquote {
	margin-left: 0;
}

blockquote p {
	margin-bottom: 1.8182em;
}

blockquote cite,
blockquote small {
	font-size: 19px;
	line-height: 1.6842;
}

pre {
	line-height: 1.2632;
}

.entry-content img,
.entry-summary img,
.page-content img,
.comment-content img,
.widget img {
	max-width: 660px;
}

img.size-full,
img.size-large,
img.header-image,
img.wp-post-image,
img[class*="align"],
img[class*="wp-image-"],
img[class*="attachment-"] {
	height: auto;
	width: auto; /* Prevent stretching of full-size and large-size images with height and width attributes in IE8 */
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"],
.post-password-form input[type="submit"],
.widecolumn #submit,
.widecolumn .mu_register input[type="submit"] {
	font-size: 16px;
	padding: 0.8125em 1.625em;
}

input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
textarea {
	padding: 0.5278em;
}

.main-navigation {
	font-size: 16px;
	line-height: 1.5;
	margin: 9.0909%;
}

.main-navigation ul ul {
	border-bottom: 0;
	border-top: 0;
	margin-left: 1em;
}

.main-navigation a {
	padding: 0.75em 0;
}

.main-navigation .menu-item-has-children > a {
	padding-right: 48px;
}

.main-navigation .menu-item-description {
	font-size: 13px;
	line-height: 1.8462;
	margin-top: 0;
}

.social-navigation {
	margin: 9.0909%;
	max-width: 660px;
	padding-top: 0;
}

.social-navigation ul {
	margin-bottom: -1.2632em;
}

.social-navigation a {
	width: 2.5263em;
	height: 2.5263em;
}

.secondary-toggle {
	margin-top: -32px;
	right: 7.6897%;
	width: 64px;
	height: 64px;
}

.secondary-toggle:before {
	line-height: 64px;
}

.post-password-form label,
.post-navigation .meta-nav,
.comment-navigation,
.image-navigation,
.author-heading,
.author-bio,
.entry-footer,
.page-links a,
.page-links span,
.comment-metadata,
.pingback .edit-link,
.comment-list .reply,
.comment-notes,
.comment-awaiting-moderation,
.logged-in-as,
.comment-form label,
.form-allowed-tags,
.site-info,
.wp-caption-text,
.gallery-caption,
.entry-caption,
.widecolumn label,
.widecolumn .mu_register label {
	font-size: 16px;
}

.post-navigation .post-title {
	font-size: 24px;
	line-height: 1.1667;
}

.pagination .nav-links {
	min-height: 3.3684em;
}

.pagination .page-numbers {
	line-height: 3.3684em;
	padding: 0 0.8421em;
}

.pagination .prev,
.pagination .next {
	padding: 0;
	width: 64px;
	height: 64px;
}

.pagination .prev:before,
.pagination .next:before {
	line-height: 64px;
	width: 64px;
	height: 64px;
}

.image-navigation a {
	display: block;
	margin-bottom: 2em;
}

.image-navigation .nav-previous,
.comment-navigation .nav-previous {
	float: left;
	width: 50%;
}
.image-navigation .nav-next,
.comment-navigation .nav-next {
	float: right;
	text-align: right;
	width: 50%;
}

.image-navigation .nav-previous a:before,
.image-navigation .nav-next a:after,
.comment-navigation .nav-previous a:before,
.comment-navigation .nav-next a:after {
	font-size: 24px;
	top: -1px;
}

blockquote.alignleft,
.wp-caption.alignleft,
img.alignleft {
	margin: 0.4211em 1.6842em 1.6842em 0;
}

blockquote.alignright,
.wp-caption.alignright,
img.alignright {
	margin: 0.4211em 0 1.6842em 1.6842em;
}

blockquote.aligncenter,
.wp-caption.aligncenter,
img.aligncenter {
	margin-top: 0.4211em;
	margin-bottom: 1.6842em;
}

.site-header {
	border-top: 1px solid transparent;
	border-bottom: 1px solid transparent;
	padding: 0;
}

.secondary {
	background-color: #fff;
	margin: 0 auto;
	max-width: 807px;
	padding: 0;
}

.site-main {
	padding: 7.6923% 0;
}

.site-content {
	margin: 0 auto;
	max-width: 954px;
}

.site-branding {
	background-color: inherit;
	margin: 0 auto;
	max-width: 954px;
	padding: 0;
}

.site-title {
	font-size: 32px;
	line-height: 1.25;
	margin: 7.6897% 7.6897% 0;
}

.site-description {
	background-color: inherit;
	display: block;
	filter: alpha(opacity=70);
	font-size: 16px;
	margin: 0.5em 7.6897% 7.6897%;
}

.sidebar {
	position: static !important;
}

.widget-area {
	clear: both;
	margin: 9.0909% 9.0909% 0;
	max-width: 660px;
}

.widget {
	font-size: 16px;
	margin: 0 0 11.1111%;
}

.widget p,
.widget address,
.widget hr,
.widget ul,
.widget ol,
.widget dl,
.widget dd,
.widget table,
.widget pre {
	margin-bottom: 1.5em;
}

.widget li > ul,
.widget li > ol {
	margin-bottom: 0;
}

.widget blockquote {
	font-size: 19px;
	line-height: 1.6842;
	margin-bottom: 1.6842em;
	margin-left: -1.2632em;
	padding-left: 1.0526em;
}

.widget blockquote > blockquote {
	margin-left: 0;
}

.widget blockquote p {
	margin-bottom: 1.6842em;
}

.widget blockquote cite,
.widget blockquote small {
	font-size: 16px;
	line-height: 1.5;
}

.widget pre {
	line-height: 1.5;
	padding: 0.75em;
}

.widget button,
.widget input,
.widget select,
.widget textarea {
	line-height: 1.5;
}

.widget button,
.widget input {
	line-height: normal;
}

.widget button,
.widget input[type="button"],
.widget input[type="reset"],
.widget input[type="submit"] {
	font-size: 16px;
	padding: 0.8125em 1.625em;
}

.widget input[type="text"],
.widget input[type="email"],
.widget input[type="url"],
.widget input[type="password"],
.widget input[type="search"],
.widget textarea {
	padding: 0.75em;
}

.widget-title {
	margin: 0 0 1.5em;
}

.widget_calendar td,
.widget_calendar th {
	line-height: 2.9375;
}

.widget_calendar caption {
	margin: 0 0 1.5em;
}

.widget_archive li,
.widget_categories li,
.widget_links li,
.widget_meta li,
.widget_nav_menu li,
.widget_pages li,
.widget_recent_comments li,
.widget_recent_entries li {
	padding: 0.7188em 0;
}

.widget_categories .children,
.widget_nav_menu .sub-menu,
.widget_pages .children {
	margin: 0.7188em 0 0 1em;
	padding-top: 0.7188em;
}

.widget_rss li {
	margin-bottom: 1.5em;
}

.widget_rss .rss-date,
.widget_rss cite {
	font-size: 13px;
	line-height: 1.8462;
}

.widget .wp-caption-text,
.widget .gallery-caption {
	line-height: 1.5;
	padding: 0.5em 0;
}

.hentry,
.page-header,
.page-content {
	margin: 0 7.6923%;
}

.hentry + .hentry,
.page-header + .hentry,
.page-header + .page-content {
	margin-top: 7.6923%;
}

.post-thumbnail {
	margin-bottom: 2.9474em;
}

.entry-header {
	padding: 0 9.0909%;
}

.entry-title,
.widecolumn h2 {
	font-size: 39px;
	line-height: 1.2308;
	margin-bottom: 1.2308em;
}

.entry-content,
.entry-summary {
	padding: 0 9.0909% 9.0909%;
}

.entry-content h1,
.entry-summary h1,
.page-content h1,
.comment-content h1 {
	font-size: 39px;
	line-height: 1.2308;
	margin-top: 1.641em;
	margin-bottom: 0.8205em;
}

.entry-content h2,
.entry-summary h2,
.page-content h2,
.comment-content h2 {
	font-size: 32px;
	line-height: 1.25;
	margin-top: 2em;
	margin-bottom: 1em;
}

.entry-content h3,
.entry-summary h3,
.page-content h3,
.comment-content h3 {
	font-size: 27px;
	line-height: 1.1852;
	margin-top: 2.3704em;
	margin-bottom: 1.1852em;
}

.entry-content h4,
.entry-summary h4,
.page-content h4,
.comment-content h4 {
	font-size: 22px;
	line-height: 1.4545;
	margin-top: 2.9091em;
	margin-bottom: 1.4545em;
}

.entry-content h5,
.entry-content h6,
.entry-summary h5,
.entry-summary h6,
.page-content h5,
.page-content h6,
.comment-content h5,
.comment-content h6 {
	font-size: 19px;
	line-height: 1.2632;
	margin-top: 3.3684em;
	margin-bottom: 1.6842em;
}

.entry-content .more-link:after {
	font-size: 24px;
	top: 3px;
}

.author-info {
	margin: 0 9.0909%;
	padding: 9.0909% 0;
}

.author-info .avatar {
	margin: 0 1.6842em 1.6842em 0;
	width: 56px;
	height: 56px;
}

.author-link:after {
	font-size: 24px;
	top: 0;
}

.entry-footer {
	padding: 4.5454% 9.0909%;
}

.posted-on:before,
.byline:before,
.cat-links:before,
.tags-links:before,
.comments-link:before,
.entry-format:before,
.edit-link:before,
.full-size-link:before {
	top: 4px;
}

.updated {
	display: none;
}

.updated.published {
	display: inline;
}

.page-header {
	border-color: inherit;
	border-style: solid;
	border-width: 0 0 0 7px;
	padding: 3.8461% 7.6923%;
}

.page-title,
.taxonomy-description {
	margin-left: -7px;
}

.taxonomy-description {
	padding-top: 0.4211em;
}

.page-title,
.comments-title,
.comment-reply-title,
.post-navigation .post-title {
	font-size: 27px;
	line-height: 1.1852;
}

.page-content {
	padding: 7.6923%;
}

.page-links {
	margin-bottom: 1.4736em;
}

.page-links a,
.page-links > span {
	margin: 0 0.25em 0.25em 0;
}

.format-aside .entry-title,
.format-image .entry-title,
.format-video .entry-title,
.format-quote .entry-title,
.format-gallery .entry-title,
.format-status .entry-title,
.format-link .entry-title,
.format-audio .entry-title,
.format-chat .entry-title {
	font-size: 22px;
	line-height: 1.4545;
	margin-bottom: 32px;
}

.format-link .entry-title a:after {
	top: 0.125em;
}

.comments-title {
	margin-bottom: 1.4545em;
}

.comment-list article,
.comment-list .pingback,
.comment-list .trackback {
	padding: 1.6842em 0;
}

.comment-list + .comment-respond,
.comment-navigation + .comment-respond {
	padding-top: 1.6842em;
}

.comment-list .children > li {
	padding-left: 1.4737em;
}

.comment-meta {
	position: relative;
}

.comment-author {
	margin-bottom: 0;
	padding-left: 4.6315em;
}

.comment-author .avatar {
	margin: 0;
	position: absolute;
	top: 3px;
	left: 0;
	width: 56px;
	height: 56px;
}

.comment-metadata {
	line-height: 2;
	padding-left: 5.5em;
}

.comment-metadata .edit-link:before,
.pingback .edit-link:before {
	top: 8px;
}

.bypostauthor > article .fn:after {
	top: 8px;
	left: 6px;
}

.comment-content ul,
.comment-content ol {
	margin: 0 0 1.6842em 0;
}

.comment-content li > ul,
.comment-content li > ol,
.comment-content blockquote > ul,
.comment-content blockquote > ol {
	margin-left: 1.3333em;
}

.comment-list .reply a {
	padding: 0.4375em 0.875em;
}

.comment-form,
.no-comments {
	padding-top: 1.6842em;
}

.comment-reply-title small a:before {
	top: -1px;
}

.comment-list .reply {
	margin-top: 0;
}

.site-footer {
	border-top: 1px solid transparent;
	border-bottom: 1px solid transparent;
	margin: 0 auto;
	max-width: 806px;
	padding: 0;
}

.site-info {
	margin: 4.5454% 9.0909%;
}

.post-navigation {
	border-top: 0;
	margin: 7.6923% 7.6923% 0;
}

.post-navigation a {
	padding: 4.5454% 9.0909%;
}

.pagination {
	border-top: 0;
	margin: 7.6923% 7.6923% 0;
	padding: 0;
}

.pagination .page-numbers {
	display: inline-block;
}

.pagination .meta-nav {
	display: none;
}

.image-navigation {
	padding: 0 9.0909%;
}

.comments-area {
	border-top: 0;
	margin: 7.6923% 7.6923% 0;
}

embed,
iframe,
object,
video {
	margin-bottom: 1.6842em;
}

.wp-audio-shortcode,
.wp-video,
.wp-playlist.wp-audio-playlist {
	font-size: 19px;
	margin-bottom: 1.6842em;
}

.wp-caption,
.gallery {
	margin-bottom: 1.6842em;
}

.wp-caption-text,
.gallery-caption {
	padding: 0.5em 0;
}

.widecolumn {
	margin: 7.6923%;
}

.widecolumn .mu_alert {
	margin-bottom: 1.6842em;
}

.widecolumn p {
	margin: 1.6842em 0;
}

.widecolumn p + h2 {
	margin-top: 1.641em;
}

.widecolumn #key,
.widecolumn .mu_register #blog_title,
.widecolumn .mu_register #user_email,
.widecolumn .mu_register #blogname,
.widecolumn .mu_register #user_name {
	font-size: 19px;
}

.widecolumn .mu_register #blog_title,
.widecolumn .mu_register #user_email,
.widecolumn .mu_register #user_name {
	margin: 0 0 0.421em;
}


/**
 * RTL
 */

.rtl ul,
.rtl ol {
	margin-right: 0;
	margin-left: auto;
}

.rtl li > ul,
.rtl li > ol,
.rtl blockquote > ul,
.rtl blockquote > ol {
	margin-right: 1.3333em;
	margin-left: auto;
}

.rtl blockquote {
	border-width: 0 4px 0 0;
	margin-right: -1.0909em;
	margin-left: auto;
	padding-right: 0.9091em;
	padding-left: 0;
}

.rtl blockquote > blockquote {
	margin-right: 0;
	margin-left: auto;
}

.rtl .main-navigation ul ul {
	margin-right: 1em;
	margin-left: auto;
}

.rtl .main-navigation .menu-item-has-children > a {
	padding-right: 0;
	padding-left: 48px;
}

.rtl .secondary-toggle {
	right: auto;
	left: 7.6897%;
}

.rtl .image-navigation .nav-previous,
.rtl .comment-navigation .nav-previous {
	float: right;
}

.rtl .image-navigation .nav-next,
.rtl .comment-navigation .nav-next {
	float: left;
	text-align: left;
}

.rtl blockquote.alignright,
.rtl .wp-caption.alignright
.rtl img.alignright {
	margin: 0.4211em 0 1.6842em 1.6842em;
}

.rtl blockquote.alignleft,
.rtl .wp-caption.alignleft,
.rtl img.alignleft {
	margin: 0.4211em 1.6842em 1.6842em 0;
}

.rtl .widget blockquote {
	margin-right: -1.2632em;
	margin-left: auto;
	padding-right: 1.0526em;
	padding-left: 0;
}

.rtl .widget blockquote > blockquote {
	margin-right: 0;
	margin-left: auto;
}

.rtl .widget_categories .children,
.rtl .widget_nav_menu .sub-menu,
.rtl .widget_pages .children {
	margin: 0.7188em 1em 0 0;
}

.rtl .page-links a,
.rtl .page-links > span {
	margin: 0 0 0.25em 0.25em;
}

.rtl .author-info .avatar {
	margin: 0 0 1.6842em 1.6842em;
}

.rtl .page-header {
	border-width: 0 7px 0 0;
}

.rtl .page-title,
.rtl .taxonomy-description {
	margin-right: -7px;
	margin-left: auto;
}

.rtl .comment-list .children > li {
	padding-right: 1.4737em;
	padding-left: 0;
}

.rtl .comment-author {
	padding-right: 4.6315em;
	padding-left: 0;
}

.rtl .comment-author .avatar {
	right: 0;
	left: auto;
}

.rtl .comment-content ul,
.rtl .comment-content ol {
	margin-right: 0;
	margin-left: auto;
}

.rtl .comment-content li > ul,
.rtl .comment-content li > ol,
.rtl .comment-content blockquote > ul,
.rtl .comment-content blockquote > ol {
	margin-right: 1.3333em;
	margin-left: auto;
}

.rtl .comment-metadata {
	padding-right: 5.5em;
	padding-left: 0;
}

.rtl .bypostauthor > article .fn:after {
	right: 6px;
	left: auto;
}
