<?php

// latvian

static $data = [
    'aiz',
    'ap',
    'apak<PERSON>',
    'apak<PERSON><PERSON>',
    'ar',
    'arī',
    'aug<PERSON><PERSON>',
    'bet',
    'bez',
    'bija',
    'biji',
    'biju',
    'bijām',
    'bijāt',
    'būs',
    'būsi',
    'būsiet',
    'būsim',
    'būt',
    'būšu',
    'caur',
    'diem<PERSON><PERSON>l',
    'diezin',
    'droši',
    'dēļ',
    'esam',
    'esat',
    'esi',
    'esmu',
    'gan',
    'gar',
    'iekam',
    'iekams',
    'iekām',
    'iekā<PERSON>',
    'iekš',
    'iek<PERSON><PERSON>',
    'ik',
    'ir',
    'it',
    'itin',
    'iz',
    'ja',
    'jau',
    'jeb',
    'jebšu',
    'jel',
    'jo',
    'jā',
    'ka',
    'kamēr',
    'kaut',
    'kolīdz',
    'kop<PERSON>',
    'kā',
    'kļuva',
    'kļuvi',
    'kļuvu',
    'kļuv<PERSON>m',
    'kļuvāt',
    'kļ<PERSON><PERSON>',
    'kļ<PERSON><PERSON>',
    'kļūsiet',
    'kļūsim',
    'kļūst',
    'kļūstam',
    'kļūstat',
    'kļūsti',
    'kļūstu',
    'kļūt',
    'kļūšu',
    'labad',
    'lai',
    'lejpus',
    'līdz',
    'līdzko',
    'ne',
    'nebūt',
    'nedz',
    'nekā',
    'nevis',
    'nezin',
    'no',
    'nu',
    'nē',
    'otrpus',
    'pa',
    'par',
    'pat',
    'pie',
    'pirms',
    'pret',
    'priekš',
    'pār',
    'pēc',
    'starp',
    'tad',
    'tak',
    'tapi',
    'taps',
    'tapsi',
    'tapsiet',
    'tapsim',
    'tapt',
    'tapāt',
    'tapšu',
    'taču',
    'te',
    'tiec',
    'tiek',
    'tiekam',
    'tiekat',
    'tieku',
    'tik',
    'tika',
    'tikai',
    'tiki',
    'tikko',
    'tiklab',
    'tiklīdz',
    'tiks',
    'tiksiet',
    'tiksim',
    'tikt',
    'tiku',
    'tikvien',
    'tikām',
    'tikāt',
    'tikšu',
    'tomēr',
    'topat',
    'turpretim',
    'turpretī',
    'tā',
    'tādēļ',
    'tālab',
    'tāpēc',
    'un',
    'uz',
    'vai',
    'var',
    'varat',
    'varēja',
    'varēji',
    'varēju',
    'varējām',
    'varējāt',
    'varēs',
    'varēsi',
    'varēsiet',
    'varēsim',
    'varēt',
    'varēšu',
    'vien',
    'virs',
    'virspus',
    'vis',
    'viņpus',
    'zem',
    'ārpus',
    'šaipus',
];

$result =& $data;
unset($data);
return $result;
