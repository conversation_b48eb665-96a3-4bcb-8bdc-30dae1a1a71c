~~~
<Fenced>
~~~

Code block starting and ending with empty lines:
~~~


<Fenced>


~~~

Indented code block containing fenced code block sample:

	~~~
	<Fenced>
	~~~

Fenced code block with indented code block sample:

~~~
Some text

	Indented code block sample code
~~~

Fenced code block with long markers:

~~~~~~~~~~~~~~~~~~
Fenced
~~~~~~~~~~~~~~~~~~

Fenced code block with fenced code block markers of different length in it:

~~~~
In code block
~~~
Still in code block
~~~~~
Still in code block
~~~~

Fenced code block with Markdown header and horizontal rule:

~~~
#test
---
~~~

Fenced code block with link definitions, footnote definition and 
abbreviation definitions:

~~~
[example]: http://example.com/

[^1]: Footnote def

*[HTML]: HyperText Markup Language
~~~

* In a list item with smalish indent:

  ~~~~~
  #!/bin/sh
  #
  # Preload driver binary
  LD_PRELOAD=libusb-driver.so $0.bin $*
  ~~~~~
  
With HTML content.
  
~~~~~
<b>bold</b>
~~~~~

Bug with block level elements in this case:
~~~
  <div>
  </div>
~~~

Indented code block of a fenced code block:

    ~~~
	haha!
	~~~

With class:
  
~~~~~html
<b>bold</b>
~~~~~
  
~~~~~ html
<b>bold</b>
~~~~~
  
~~~~~.html
<b>bold</b>
~~~~~
  
~~~~~ .html
<b>bold</b>
~~~~~

With extra attribute block:

~~~~~{.html}
<b>bold</b>
~~~~~
  
~~~~~ {.html #codeid}
<b>bold</b>
~~~~~

~~~~~ .html{.bold}
<div>
~~~~~
  
~~~~~ .html {#codeid}
</div>
~~~~~