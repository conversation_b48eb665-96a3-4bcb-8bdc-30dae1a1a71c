about = "Σχετικά"
add_category = "Προσθήκη Κατηγορίας"
add_content = "Προσθήκη Περιεχομένου"
add_link = "Προσθήκη συνδέσμου"
add_menu = "Προσθήκη μενού"
add_new_page = "Προσθήκη νέας σελίδας"
add_new_post = "Προσθήκη νέας ανάρτησης"
add_source_link_optional = "Προσθήκη συνδέσμου πηγής (προαιρετικό)"
add_sub = "Προσθήκη Υποσελίδας"
address_url = "Διεύθυνση (URL)"
admin = "Διαχειριστής"
admin_panel_style_based_on = "Στιλ πίνακα διαχειριστή με βάση"
all_blog_posts = "Όλες οι αναρτήσεις ιστολογίου"
all_cache_has_been_deleted = "Όλη η προσωρινή μνήμη έχει διαγραφεί !"
all_posts_tagged = "Όλες οι αναρτήσεις με ετικέτα"
archive_for = "Αρχείο για"
archive_page_for = "Αρχειοθέτηση σελίδας για"
archives = "Αρχειοθέτηση"
are_you_sure_you_want_to_delete_ = "Είστε βέβαιοι ότι θέλετε να διαγράψετε <strong>%s</strong>;"
at_the_moment_you_are_using_auto_generated_menu = "Αυτή τη στιγμή χρησιμοποιείτε το μενού που δημιουργείται αυτόματα."
audio_post = "Ηχητική ανάρτηση"
audio_post_comment = "Δημιουργία ανάρτησης ιστολογίου με επιλεγμένο ήχο"
author = "Συγγραφέας"
author_description = " "
back_to = "Επιστροφή στο"
backup = "Αντίγραφα ασφαλείας"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Σε μία παράγραφο, πείτε μας περισσότερα για το ιστολόγιό σας."
blog_theme = "Θέμα ιστολογίου"
blog_title = "Τίτλος ιστολογίου"
blog_title_placeholder = "Το ιστολόγιό μου HTMLy"
blog_posts_displayed_as = "Οι αναρτήσεις ιστολογίου εμφανίζονται ως"
breadcrumb_home_text = "Αρχικό κείμενο ψωμιού"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Χρησιμοποιώντας αυτόν τον εισαγωγέα συμφωνείτε εάν η ροή είναι δική σας ή τουλάχιστον έχετε την εξουσία να τη δημοσιεύσετε."
css_class_optional = "Κλάση CSS (προαιρετικό)"
cache_expiration = "Λήξη προσωρινής μνήμης (σε ώρες)"
cache_off = "Απενεργοποίηση προσωρινής μνήμης"
cache_timestamp = "Cache timestamp"
cancel = "Άκυρο"
cannot_read_feed_content = "Δεν είναι δυνατή η ανάγνωση της ροής"
captcha_error = "το reCaptcha δεν είναι σωστό"
categories = "Κατηγορίες"
category = "Κατηγορία"
check_update = "Έλεγχος έκδοσης"
clear_cache = "Εκκαθάριση προσωρινής μνήμης"
comma_separated_values = "Τιμές διαχωρισμένες με κόμμα"
comment_system = "Σύστημα σχολίων"
comments = "Σχόλια"
config = "Διαμόρφωση"
congrats_you_have_the_latest_version_of_htmly = "Συγχαρητήρια! Έχετε την πιο πρόσφατη έκδοση του HTMLy."
content = "Περιεχόμενο"
contents = "Περιεχόμενα"
copyright_line = "Γραμμή πνευματικών δικαιωμάτων"
copyright_line_placeholder = "(γ) Το όνομά σας."
create_backup = "Δημιουργία αντιγράφου ασφαλείας"
created = "Δημιουργήθηκε"
custom = "Προσαρμοσμένο"
custom_settings = "Προσαρμοσμένες ρυθμίσεις"
dashboard = "Πίνακας ελέγχου"
date = "Ημερομηνία"
date_format = "Μορφή ημερομηνίας"
delete = "Διαγραφή"
description = "Περιγραφή"
disable = "Απενεργοποίηση"
disabled = "Απενεργοποιημένο"
disqus_shortname = "Σύντομο όνομα Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Προσχέδιο"
edit = "Επεξεργασία"
edit_category = "Επεξεργασία κατηγορίας"
edit_post = "Επεξεργασία"
edit_profile = "Επεξεργασία προφίλ"
enable = "Ενεργοποίηση"
enable_blog_url = "Ενεργοποίηση διεύθυνσης URL blog"
enter_image_url = "Εισαγωγή διεύθυνσης URL εικόνας"
facebook_app_id = "Αναγνωριστικό εφαρμογής Facebook"
facebook_page = "Σελίδα Facebook"
featured_audio = "Επιλεγμένος ήχος"
featured_image = "Επιλεγμένη εικόνα"
featured_link = "Επιλεγμένος σύνδεσμος"
featured_quote = "Επιλεγμένη προσφορά"
featured_video = "Επιλεγμένο βίντεο"
feed_url = "Διεύθυνση URL ροής"
filename = "Όνομα αρχείου"
follow = "Ακολούθησε"
for_google_site_verification_meta = "Για meta google-site-verification"
for_msvalidate_01_meta = "Για meta msvalidate.01"
front_page_displays = "Εμφάνιση πρώτης σελίδας"
full_post = "Πλήρης ανάρτηση"
general = "Γενικά"
general_settings = "Γενικές ρυθμίσεις"
get_one_here = "Αποκτήστε ένα εδώ"
github_pre_release = "Github προέκδοση"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (κληρονομιά)"
google_search_console = "Google Search Console"
home = "Αρχική"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Αν αφήσουμε κενό, θα το αποσπάσουμε από το παρακάτω περιεχόμενο"
if_the_url_is_left_empty_we_will_use_the_page_title = "Εάν το url μείνει κενό, θα χρησιμοποιήσουμε τον τίτλο της σελίδας"
if_the_url_is_left_empty_we_will_use_the_post_title = "Εάν το url μείνει κενό, θα χρησιμοποιήσουμε τον τίτλο της ανάρτησης"
image_post = "Ανάρτηση εικόνας"
image_post_comment = "Δημιουργία ανάρτησης ιστολογίου με επιλεγμένη εικόνα"
import = "Εισαγωγή"
import_feed = "Έναρξη εισαγωγής ροής"
import_rss = "Εισαγωγή RSS"
import_rss_feed_2.0 = "Εισαγωγή RSS Feed 2.0"
insert_image = "Εισαγωγή εικόνας"
invalid_error = "ΣΦΑΛΜΑ: Μη έγκυρο όνομα χρήστη ή κωδικός πρόσβασης"
language = "Γλώσσα συστήματος"
link_name = "Όνομα συνδέσμου"
link_post = "Ανάρτηση συνδέσμου"
link_post_comment = "Δημιουργία ανάρτησης ιστολογίου με επιλεγμένο σύνδεσμο"
login = "Είσοδος"
login_page = "Σελίδα σύνδεσης"
logout = "Αποσύνδεση"
menu = "Μενού"
menus = "Επεξεργαστής μενού"
meta_description = "Meta περιγραφή"
meta_description_character = "Χαρακτήρας μετα περιγραφής"
metatags = "Metatags"
metatags_settings = "Ρυθμίσεις Metatags"
mine = "Δικά μου"
more = "Περισσότερα"
my_draft = "Τα προσχέδιά μου"
my_posts = "Οι αναρτήσεις μου"
name = "Όνομα"
newer = "Νεότερα"
next = "Επόμενο"
next_post = "Επόμενο post"
no_available_backup = "Δεν υπάρχει διαθέσιμο αντίγραφο ασφαλείας αυτή τη στιγμή."
no_draft_found = "Δεν βρέθηκε προσχέδιο"
no_posts_found = "Δεν βρέθηκαν δημοσιεύσεις"
no_related_post_found = "Δεν βρέθηκε σχετική ανάρτηση"
no_scheduled_posts_found = "Δεν υπάρχουν προγραμματισμένες αναρτήσεις!"
no_search_results = "Δεν υπάρχουν αποτελέσματα αναζήτησης"
nope = "Όχι"
not = "Όχι"
older = "Παλαιότερα"
only = "Μόνο"
operations = "Λειτουργίες"
page = "Σελίδα"
page_generation_time = "Χρόνος δημιουργίας σελίδας"
pages = "Σελίδες"
pass_error = "Απαιτείται το πεδίο κωδικού πρόσβασης"
password = "Κωδικός πρόσβασης"
performance = "Απόδοση"
performance_settings = "Ρυθμίσεις απόδοσης"
permalink = "Permalink"
popular = "Δημοφιλές"
popular_posts = "Δημοφιλείς αναρτήσεις"
popular_posts_widget = "Γραφικό στοιχείο δημοφιλών αναρτήσεων"
popular_posts_widget_at_most = "Γραφικό στοιχείο δημοφιλών αναρτήσεων το πολύ"
popular_tags = "Δημοφιλείς ετικέτες"
post_by_author = "Αναρτήσεις από αυτόν τον συγγραφέα"
posted_in = "Δημοσιεύτηκε στο"
posted_on = "Δημοσιεύτηκε στις"
posts = "Αναρτήσεις"
posts_by = "Αναρτήσεις από"
posts_draft = "Προσχέδιο αναρτήσεων"
posts_in_archive_page_at_most = "Το πολύ δημοσιεύσεις στη σελίδα αρχείου"
posts_in_category_page_at_most = "Το πολύ δημοσιεύσεις στη σελίδα κατηγορίας"
posts_in_front_page_show_at_most = "Οι αναρτήσεις στην πρώτη σελίδα εμφανίζονται το πολύ"
posts_in_profile_page_at_most = "Το πολύ δημοσιεύσεις στη σελίδα προφίλ"
posts_in_search_result_at_most = "Το πολύ δημοσιεύσεις στο αποτέλεσμα αναζήτησης"
posts_in_tag_page_at_most = "Το πολύ δημοσιεύσεις στη σελίδα ετικέτας"
posts_in_type_page_at_most = "Το πολύ δημοσιεύσεις στη σελίδα τύπου"
posts_index_settings = "Ρυθμίσεις ευρετηρίου αναρτήσεων"
posts_list = "Λίστα αναρτήσεων"
posts_tagged = "Δημοσιεύσεις με ετικέτα"
posts_with_type = "Δημοσιεύσεις με τύπο"
pre_release = "Προκυκλοφορία"
prev = "Προηγούμενο"
prev_post = "Προηγούμενη ανάρτηση"
preview = "Προεπισκόπηση"
profile_for = "Προφίλ για"
proudly_powered_by = "Περήφανα τροφοδοτείται από"
publish = "Δημοσίευση"
publish_draft = "Δημοσίευση προχείρου"
published = "Δημοσιεύτηκε"
quote_post = "Παράθεση ανάρτησης"
quote_post_comment = "Δημιουργία ανάρτησης ιστολογίου με επιλεγμένο απόσπασμα"
rss_character = "χαρακτήρας RSS"
rss_feeds_show_the_most_recent = "Οι ροές RSS εμφανίζουν τις πιο πρόσφατες"
rss_settings = "Ρυθμίσεις RSS"
read_more_text = "Διαβάστε περισσότερα κείμενο"
read_more_text_placeholder = "Διαβάστε περισσότερα"
reading = "Διαβάζοντας"
reading_settings = "Ρυθμίσεις ανάγνωσης"
recaptcha = "reCAPTCHA"
recent_posts = "Πρόσφατες δημοσιεύσεις"
recent_posts_widget_at_most = "Γραφικό στοιχείο πρόσφατων αναρτήσεων το πολύ"
regular_post = "Κανονική ανάρτηση"
regular_post_comment = "Δημιουργία κανονικής ανάρτησης ιστολογίου"
related_posts = "Σχετικές αναρτήσεις"
related_widget_posts_at_most = "Σχετικές αναρτήσεις widget το πολύ"
revert_to_draft = "Επαναφορά στο πρόχειρο"
save = "Αποθήκευση"
save_config = "Αποθήκευση διαμόρφωσης"
save_edit = "Αποθήκευση επεξεργασίας"
save_menu = "Αποθήκευση μενού"
save_as_draft = "Αποθήκευση ως πρόχειρο"
save_category = "Αποθήκευση κατηγορίας"
scheduled = "Προγραμματισμένες"
scheduled_posts = "Προγραμματισμένες αναρτήσεις"
scheduled_tips = "Δημοσιεύοντας μια ανάρτηση με μελλοντική ημερομηνία ή ώρα, θα μεταφερθεί στις προγραμματισμένες αναρτήσεις"
search = "Αναζήτηση"
search_for = "Αναζήτηση"
search_results_for = "Αποτελέσματα αναζήτησης για"
search_results_not_found = "Δεν βρέθηκαν αποτελέσματα αναζήτησης!"
secret_key = "Μυστικό κλειδί"
settings = "Ρυθμίσεις"
sign_in_to_start_your_session = "Συνδεθείτε για επεξεργασία περιεχομένου"
site_key = "Κλειδί τοποθεσίας"
sitemap = "Χάρτης ιστότοπου"
slug = "Slug"
social_media = "Μέσα κοινωνικής δικτύωσης"
static_page = "Στατική σελίδα"
static_page_comment = "Δημιουργία στατικής σελίδας"
static_pages = "Στατικές σελίδες"
summary = "Σύνοψη"
summary_character = "Σύνοψη χαρακτήρα"
tag = "Ετικέτα"
tagcloud_widget_at_most = "TagCloud το πολύ"
tagline = "Tagline"
tagline_placeholder = "Πλατφόρμα ιστολογίων PHP χωρίς βάση δεδομένων"
tagline_description = "Με λίγα λόγια, εξηγήστε τι είναι αυτό το ιστολόγιο."
tags = "Ετικέτες"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Αυτός είναι κώδικας παλαιού τύπου. Συνήθως νέα αναλυτικά στοιχεία που δημιουργούνται χρησιμοποιώντας gtag.js"
this_page_doesnt_exist = "Αυτή η σελίδα δεν υπάρχει!"
time = "Ώρα"
timezone = "Ζώνη ώρας"
title = "Τίτλος"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Για να χρησιμοποιήσετε τα σχόλια του Disqus ή του Facebook, πρέπει να δώσετε το σύντομο όνομα του Disqus ή το αναγνωριστικό εφαρμογής του Facebook."
token_error = "Το CSRF Token δεν είναι σωστό"
tools = "Εργαλεία"
twitter_account = "Λογαριασμός Twitter"
type_to_search = "Aναζήτηση"
uncategorized = "Χωρίς κατηγορία"
uncategorized_comment = "Θέματα που δεν χρειάζονται κατηγορία ή δεν ταιριάζουν σε καμία άλλη υπάρχουσα κατηγορία"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Άγνωστη μορφή ροής"
update = "Ενημέρωση"
update_available = "Ενημέρωση διαθεσιμότητας"
update_draft = "Ενημέρωση σχεδίου"
update_post = "Ενημέρωση ανάρτησης"
update_to = "Ενημέρωση σε"
upload = "Μεταφόρτωση"
user = "Χρήστης"
user_error = "Απαιτείται πεδίο χρήστη"
valid_values_range_from_0_to_1.0._see = "Οι έγκυρες τιμές κυμαίνονται από 0,0 έως 1,0. Δείτε"
video_post = "Βίντεο ανάρτηση"
video_post_comment = "Δημιουργία ανάρτησης ιστολογίου με επιλεγμένο βίντεο"
view = "Προβολή"
view_post = "Προβολή"
views = "Προβολές"
widget = "Widget"
widget_settings = "Ρυθμίσεις widget"
would_you_like_to_try_our = "Θα θέλατε να δοκιμάσετε το "
yes_im_in = "Ναι, είμαι μέσα"
yes_not_recommended = "Ναι (δεν συνιστάται)"
you_dont_have_permission_to_access_this_page = "Δεν έχετε άδεια πρόσβασης σε αυτήν τη σελίδα"
your_new_config_key = "Το νέο κλειδί διαμόρφωσης"
your_new_value = "Η νέα σας αξία"
your_backups = "Τα αντίγραφα ασφαλείας σας (Backup)"
your_latest_blog_posts = "Οι πιο πρόσφατες αναρτήσεις ιστολογίου σας"
your_recent_posts = "Οι πρόσφατες αναρτήσεις σας"
by = "από"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>hint:</u> Χρησιμοποιήστε <code>Ctrl</code>/<code>CMD</code> + <code>F</code> για να αναζητήσετε το κλειδί ή την τιμή διαμόρφωσης."
homepage = "αρχική σελίδα"
instead = "αντί"
item_class = "Εισαγωγή κλάσης CSS"
item_slug = "Εισαγωγή URL συνδέσμου"
now = "τώρα"
of = "από"
optional = "προαιρετικό"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>επαγγελματικές συμβουλές:</u> Μπορείτε να δημιουργήσετε προσαρμοσμένα κλειδιά διαμόρφωσης και να εκτυπώσετε την τιμή του κλειδιού διαμόρφωσης οπουδήποτε στο πρότυπό σας."
read_more = "διαβάστε περισσότερα"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
security = "Security"
security_settings = "Security Settings"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
