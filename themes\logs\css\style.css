/*-------------------------
        Simple reset
--------------------------*/

* {
    margin: 0;
    padding: 0;
}

body {
    font-size: 17px;
    line-height: 1.6em;
    font-family: Georgia, sans-serif;
    background: #F7F7F7;
    color: #444444;
}

section, footer, header, aside, nav {
    display: block;
}

img {
    border: 0 none;
    height: auto;
    max-width: 100%;
    outline: 0 none;
}

.hide {
    display: none;
}

/*-------------------------
        Heading
--------------------------*/

h1, h2, h3, h4, h5, h6 {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.3;
    margin: 0.5em 0;
    font-weight: 400;
}

h1 {
    font-size: 28px;
}

h2 {
    font-size: 24px;
}

h3 {
    font-size: 18px;
}

h4 {
    font-size: 16px;
}

h5 {
    font-size: 14px;
}

h6 {
    font-size: 12px;
}

/*-------------------------
        Layout
--------------------------*/

#cover {
    padding: 0 0 20px 0;
    float: left;
    width: 100%;
}

#header-wrapper {
    float: left;
    width: 100%;
    position: relative;
}

#header {
    position: relative;
    padding: 0 15px;
    margin: 0 auto;
}

#main-wrapper {
    float: left;
    width: 100%;
    background: #ffffff;
    position: relative;
    border-top: 1px solid #DFDFDF;
    border-bottom: 1px solid #DFDFDF;
}

#main {
    position: relative;
    padding: 0;
    margin: 0 auto;
    background: #ffffff;
    overflow: hidden;
    padding: 30px 15px;
}

.responsive {
    width: 950px;
}

/*-------------------------
        Link
--------------------------*/

a {
    color: #21759B;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
    color: #D54E21;
}

.tab {
    width: 100%;
    margin: 1.5em 0 0 0;
    line-height: 1;
    border-bottom: 1px solid #E4E7EE;
}

.tab ul {
	padding:0;
	margin:0;
}

.tab  li {
    display: inline-block;
	margin:0;
	padding:0;
}

.tab a {
    background: #E4E7EE;
    border-radius: 6px 6px 0px 0;
    color: #555555;
    text-align: center;
    padding: 8px 14px;
    display: inline-block;
    margin: 0 5px;
    font-size: 12px;
    font-weight: bold;
    font-family: 'Open Sans', sans-serif;
}

.tags a {
    margin-right:3px;
}

/*-------------------------
        Text element
--------------------------*/

p {
    margin: 1em 0;
}

blockquote:before {
    color: #BBBBBB;
    content: "“";
    font-size: 3em;
    line-height: 0.1em;
    margin-right: 0.2em;
    vertical-align: -0.4em;
}

blockquote:after {
    color: #BBBBBB;
    content: "”";
    font-size: 3em;
    line-height: 0.1em;
    vertical-align: -0.45em;
}

blockquote {
    font-style: italic;
    margin: 1em 0 1em 1em;
}

blockquote p {
    display: inline;
}

pre {
    margin: 1em 0;
    overflow: auto;
    background: #F1F1FF;
    color: #333333;
    display: block;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    padding: 10px;
    line-height: 1.3;
}

code {
    color: #333333;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 15px;
    padding: 3px 8px;
    background: #F1F1FF;
}

pre code {
    color: #333333;
    display: block;
    font-family: Consolas, Menlo, Monaco, Lucida Console, Liberation Mono, DejaVu Sans Mono, Bitstream Vera Sans Mono, Courier New, monospace, serif;
    font-size: 14px;
    padding: 0;
    line-height: 1.6;
}

.thumbnail {
    float: left;
    height: 80px;
    width: 80px;
    margin: 5px 15px 0 0;
    overflow: hidden;
    display: block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
}

ul, ol {
    padding-left: 30px;
    margin: 1em 0;
}

ul li, ol li {
    margin: 0.25em 0;
}

.category {
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 4px;
    color: #333;
    display: block;
    font-size: 13px;
    margin: 20px 0;
    padding: 0 1em;
}

.tags a:after {
    content:", ";
}
.tags a:last-child:after {
    content:"";
}

/*-------------------------
        Header
--------------------------*/

#branding {
    text-align: left;
    position: relative;
    width: 100%;
    float: left;
    margin: 1em 0;
    text-shadow: 0 1px 0 #ffffff;
}

#branding a {
    text-decoration: none;
    color: #000000;
}

#branding p {
    margin: 0;
    font-style: italic;
    margin-top: 5px;
}

#branding h1, #branding h2 {
    font-size: 36px;
    font-family: Georgia, sans-serif;
    margin: 0;
}

/*-------------------------
        Menu
--------------------------*/

#menu-wrapper {
    float: left;
    width: 100%;
    position: relative;
    text-align: left;
    font-family: 'Open Sans', sans-serif;
    font-size: 15px;
    text-shadow: 0 1px 0 #ffffff;
    padding-bottom: 7px;
}

#menu {
    margin: 0 auto;
    padding: 0 15px;
}

#menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

#menu ul li {
    padding: 0 10px;
    float: left;
    margin: 0;
}

#menu ul li.first {
    padding-left: 0;
}

#menu ul li.last {
    padding-right: 0;
}

#menu a {
    text-decoration: none;
    float: left;
}

#menu a:hover {
    text-decoration: underline;
}

#menu ul li.active > a {
    text-decoration: underline;
}

#menu ul li ul.subnav {
    display:none;
}

/*-------------------------
        Search
--------------------------*/

#search-form {
    float: right;
}
.error-404 .post #search-form,
.error-404-search .post #search-form {
    float: none;
}

.search-input {
    border: 1px solid #E5E5E5;
    border-top-color: gainsboro;
    color: #666;
    display: inline-block;
    line-height: normal;
    height: 22px;
    padding: 0 3px;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
}

.search-input:hover {
    border: 1px solid #b9b9b9;
    border-top: 1px solid #a0a0a0;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, .1)
}

.search-input:focus {
    border: 1px solid rgb(77, 144, 254);
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3) inset;
    outline: medium none;
}

.search-button {
    background-color: #5e94ff;
    border: 1px solid #5e94ff;
    -moz-border-radius: 2px;
    -webkit-border-radius: 2px;
    border-radius: 2px;
    -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    box-shadow: 0 1px 1px rgba(0, 0, 0, .2);
    color: #fff;
    display: inline-block;
    font-family: "open sans", arial, sans-serif;
    font-size: 13px;
    font-weight: 600;
    line-height: 1.54;
    padding: 0;
    margin: 0;
    text-align: center;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, .2);
    -moz-transition: all .218s ease-in;
    -webkit-transition: all .218s ease-in;
    transition: all .218s ease-in;
    cursor: pointer;
    height: 23px;
    padding: 0 3px;
}

/*-------------------------
        Content
--------------------------*/

#content {
    width: 700px;
    float: left;
    padding-right: 50px;
}

.breadcrumb {
    color: #999999;
    font-size: 15px;
    margin-bottom: 1em;
    font-family: 'Open Sans', sans-serif;
}

.breadcrumb a {
    color: #777777;
    border-bottom: 1px solid #DFDFDF;
}

.breadcrumb a:hover {
    border-bottom: 1px solid #D54E21;
    color: #D54E21;
}

.post {
    margin: 0 0 24px 0;
    overflow: hidden;
    border-bottom: 1px solid #DFDFDF;
}

.post a, .post-list a, .pager a, .postnav a {
    border-bottom: 1px solid #DFDFDF;
}

.post a:hover, .post-list a:hover, .pager a:hover, .postnav a:hover {
    border-bottom: 1px solid #D54E21;
}

.date {
    color: #999999;
    font-size: 13px;
    font-family: 'Open Sans', sans-serif;
    margin: 1em 0;
}

.date a {
    color: #777777;
}

.date a:hover {
    border-bottom: 1px solid #D54E21;
    color: #D54E21;
}

#main strong {
    color: #454545;
}

#main h1 a, #main h2 a {
    border: none;
}

.post.first .title-index {
    margin-top: 0;
}

.main {
    margin-bottom: 1.5em;
}

.in-post .post, .in-page .post, .in-subpage .post {
    border: none;
}

.title-post {
    margin-top: 0;
}

.border {
    border-top: 1px solid #DFDFDF;
    margin-bottom: 15px;
    margin-top: 10px;
    padding-bottom: 15px;
    width: 100%;
    float: left;
}

.profile {
    border-bottom: 1px solid #DFDFDF;
}

.post-list {
    font-size: 14px;
    border-bottom: 1px solid #DFDFDF;
    margin-bottom: 1em;
    padding-bottom: 1em;
}

/*-------------------------
        Share box
--------------------------*/

.separator {
    font-size: 20px;
}

.share-box {
    font-size: 14px;
    float: left;
    line-height: 1.5;
}

.author-info {
    width: 540px;
    float: left;
}

.share {
    width: 140px;
    margin-left: 1em;
    float: right;
}

.share a {
    display: inline-block;
    width: 18px;
    height: 18px;
    margin: 0;
    padding: 0 5px;
    vertical-align: middle;
    -o-transition: all .3s;
    -moz-transition: all .3s;
    -webkit-transition: all .3s;
    -ms-transition: all .3s;
    text-indent: -9999em;
    border: none;
}

.share a.twitter {
    background: url(../../../system/resources/images/share-twitter.png) left top no-repeat;
}

.share a.facebook {
    background: url(../../../system/resources/images/share-facebook.png) left top no-repeat;
}

.share a:hover {
    background-position: left -26px;
    border: none;
}

.related {
    font-size: 15px;
    line-height: 1.5;
}

.pager, .postnav {
    font-size: 15px;
    position:relative;
}

.pager {
    text-align:center;
	margin-bottom: 1em;
}

.pager .newer {
    position:absolute;
    left: 0;
}

.postnav .newer {
    float: left;
}

.pager .older {
    position:absolute;
    right:0;
}

.postnav .older {
    float: right;
}

.border .fb-comments, .border #disqus_thread {
    padding-top: 1.2em;
}

#disqus_thread {
    float: left;
    font-family: Georgia, Times, Cambria, serif;
    width: 100%;
}

/*-------------------------
        Sidebar
--------------------------*/

#sidebar {
    float: left;
    color: #5c5a54;
    height: 100%;
    width: 200px;
    font-size: 15px;
    line-height: 1.4;
}

#sidebar h2, #sidebar h3, #sidebar h4 {
    color: #000000;
    border-bottom: 1px solid #DFDFDF;
    margin: 0 0 1em 0;
    padding-bottom: 5px;
}

#sidebar ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

#sidebar .archivegroup ul.month {
    margin: 0.25em 0 0.25em 1.3em;
}

#sidebar .archivegroup ul.month li {
    margin: 0;
}

.archivegroup li.expanded, .archivegroup li.collapsed {
    margin: 0;
}

.about, .social, .archive, .comments, .tagcloud, .recent, .popular, .category-list {
    margin-bottom: 2em;
}

#sidebar .recent ul, #sidebar .popular ul {
    margin: 0.25em 0 0.25em 1.3em;
}

#sidebar .recent li, #sidebar .popular li {
    list-style-type: circle;
}

/*-------------------------
        Copyright
--------------------------*/

#copyright-wrapper {
    margin: 20px 0 0 0;
    float: left;
    width: 100%;
    position: relative;
    font-size: 14px;
    line-height: 1.5;
}

#copyright {
    text-align: center;
    color: #909090;
    text-shadow: 0 1px 0 #ffffff;
    text-align: center;
    margin: 0 auto;
    padding: 0 15px;
}

#copyright p {
    margin: 0;
}

/*----------------------------
        The 404 page
-----------------------------*/

.message {
    padding: 50px 20px 20px 20px;
}

.message h1 {
    font-size: 36px;
    margin-bottom: 18px;
}

.message p {
    font-size: 13px;
}

.center {
    text-align: center;
}

.search-404 {
    position: relative;
    float: left;
    width: 100%;
    margin-bottom: 1.2em;
}

.search-404 #search-form {
    float: none;
}

/*-------------------------
        Table
--------------------------*/

table {
    font-size: 12px;
    border: none;
    width: 100%;
    color: #333333;
    border: 1px solid #E3E3E3;
    margin: 1em 0;
    white-space: pre-wrap;
    word-wrap: break-word;
}

table h2.title {
    margin: 5px 0;
}

th, td {
    padding: 5px 10px;
    border: none;
}

th.title {
    margin: 5px 0;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
    font-weight: normal;
}

td.title {
    font-weight: normal;
    background-color: #f6f6f6;
    font-family: 'Open Sans', sans-serif;
}

th {
    background-color: #f6f6f6;
    border-bottom: 1px solid #E3E3E3;
    border-right: 1px solid #E3E3E3;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
}

td {
    background-color: #fafafa;
    border: 1px solid #E3E3E3;
    border-top: none;
    border-left: none;
}

table.post-list td a {
    margin: 0 5px;
}

/*----------------------------
        Media queries
-----------------------------*/

@media all and (max-width: 1024px) {
    .responsive {
        width: 95%;
    }

    #content {
        width: 73%;
        padding-right: 5%;
    }

    .author-info {
        width: 68%;
    }

    .share {
        width: 28%;
        margin-left: 4%;
    }

    #sidebar {
        width: 22%;
    }
}

@media all and (max-width: 640px) {

    .responsive {
        width: 94%;
    }

    #content {
        width: 100%;
        padding-right: 0;
    }

    .author-info, .related, .share-box {
        width: 100%;
    }

    .share {
        width: 100%;
        margin-left: 0;
        margin-bottom: 1em;
    }

    #sidebar {
        width: 100%;
    }

    .tagcloud ul li {
        float: left;
        list-style: none outside none;
        margin-right: 10px;
    }

    .pager .newer, .postnav .newer {
        float: left;
        margin-bottom: 1em;
    }

    .pager .older, .postnav .older {
        float: left;
        margin-bottom: 1em;
    }

    #branding, #menu-wrapper {
        text-align: center;
    }

    #menu-wrapper ul {
        width: 100%;
        text-align: center;
        float: left;
    }

    #menu-wrapper ul li {
        float: none;
        display: inline-block;
        vertical-align: top;
    }

    #menu a {
        float: none;
    }

    #search-form {
        float: left;
        width: 100%;
        margin-bottom: 1em;
    }

    pre {
        white-space: pre-wrap;
        word-wrap: break-word;
    }

}

@media all and (max-width: 360px) {
    .responsive {
        width: 92%;
    }
}

@media all and (max-width: 320px) {
    .responsive {
        width: 90%;
    }
}

/*----------------------------
        Video
-----------------------------*/

.video-wrapper {
    margin: 1em 0;
}

@media all and (min-width: 420px) {

    .video-wrapper {
        padding-top: 315px !important;
        position: relative;
        width: 100%;
        max-width: 420px !important;
        max-height: 315px !important;
    }

    .video-wrapper iframe, .video-wrapper object, .video-wrapper embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        max-width: 420px !important;
        width: 100%;
        max-height: 315px !important;
    }

}

@media all and (max-width: 420px) {

    .video-wrapper {
        position: relative;
        padding-bottom: 56.25%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }

    .video-wrapper iframe, .video-wrapper object, .video-wrapper embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
    }

}

@media all and (max-width: 560px) {

    .featured-video, .featured-audio {
        position: relative;
        padding-bottom: 56.25%;
        padding-top: 30px;
        height: 0;
        overflow: hidden;
    }

    .featured-video iframe, .featured-video object, .featured-video embed, .featured-audio iframe, .featured-audio object, .featured-audio embed {
        height: 100%;
        left: 0;
        position: absolute;
        top: 0;
        width: 100%;
    }

}

/*----------------------------
        Archive
-----------------------------*/

ul.archivegroup {
    padding: 0;
    margin: 0;
}

.archivegroup .expanded ul {
    display: block;
}

.archivegroup .collapsed ul {
    display: none;
}

.archivegroup li.expanded, .archivegroup li.collapsed {
    list-style: none;
}

#more {
    display:block;
    height:1rem;
    margin-top:-1rem;
    box-shadow: none;
    border: none;
}