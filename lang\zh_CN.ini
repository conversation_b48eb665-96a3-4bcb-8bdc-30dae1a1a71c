about = "关于"
add_category = "添加分类"
add_content = "添加内容"
add_link = "添加链接"
add_menu = "添加菜单"
add_new_page = "添加新页面"
add_new_post = "添加新文章"
add_source_link_optional = "添加源链接（可选）"
add_sub = "添加子页面"
address_url = "地址（URL）"
admin = "管理员"
admin_panel_style_based_on = "管理面板风格基于"
all_blog_posts = "所有博客文章"
all_cache_has_been_deleted = "所有缓存已删除！"
all_posts_tagged = "所有带指定标签的文章："
archive_for = "归档："
archive_page_for = "归档："
archives = "存档"
are_you_sure_you_want_to_delete_ = "你确定要删除<strong>%s</strong>？"
at_the_moment_you_are_using_auto_generated_menu = "你正在使用自动创建的菜单"
audio_post = "音频文章"
audio_post_comment = "创建有特色音频的文章"
author = "作者"
author_description = "又一个 HTMLy 用户"
back_to = "回到"
backup = "备份"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "用一篇文章告诉我们更多关于你博客的信息"
blog_theme = "博客主题"
blog_title = "博客标题"
blog_title_placeholder = "我的 HTMLy 博客"
blog_posts_displayed_as = "文章显示为"
breadcrumb_home_text = "“主页”链接文本"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "使用这个导入器即代表你同意要导入的Feed是你的，或者至少你有权力发布它"
css_class_optional = "CSS类（可选）"
cache_expiration = "缓存过期时间（小时）"
cache_off = "关闭缓存"
cache_timestamp = "缓存时间戳"
cancel = "取消"
cannot_read_feed_content = "不能读取订阅源内容"
captcha_error = "reCaptcha不正确"
categories = "分类"
category = "分类"
check_update = "检查更新"
clear_cache = "清除缓存"
comma_separated_values = "用逗号分隔值"
comment_system = "评论系统"
comments = "评论"
config = "配置"
congrats_you_have_the_latest_version_of_htmly = "恭喜！你正在使用最新版 HTMLy"
content = "内容"
contents = "内容"
copyright_line = "版权信息"
copyright_line_placeholder = "(c) 你的组织"
create_backup = "创建备份"
created = "已创建"
custom = "自定义"
custom_settings = "自定义设置"
dashboard = "仪表板"
date = "日期"
date_format = "日期格式"
delete = "删除"
description = "描述"
disable = "禁用"
disabled = "已禁用"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "草稿"
edit = "编辑"
edit_category = "编辑分类"
edit_post = "编辑"
edit_profile = "编辑个人信息"
enable = "启用"
enable_blog_url = "启用 blog URL"
enter_image_url = "输入图片URL"
facebook_app_id = "Facebook App ID"
facebook_page = "Facebook页面"
featured_audio = "特色音频"
featured_image = "特色图片"
featured_link = "特色链接"
featured_quote = "特色引用"
featured_video = "特色视频"
feed_url = "Feed URL"
filename = "文件名"
follow = "跟随"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "主页显示"
full_post = "全文"
general = "通用"
general_settings = "通用设置"
get_one_here = "获取自"
github_pre_release = "Github预发布版本"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (传统)"
google_search_console = "Google Search Console"
home = "主页"
if_left_empty_we_will_excerpt_it_from_the_content_below = "如果留空，从下面的文章内容中摘录"
if_the_url_is_left_empty_we_will_use_the_page_title = "如果留空，使用页面标题作为URL"
if_the_url_is_left_empty_we_will_use_the_post_title = "如果留空，使用文章标题作为URL"
image_post = "图片文章"
image_post_comment = "创建有特色图片的文章"
import = "导入"
import_feed = "开始导入Feed"
import_rss = "导入RSS"
import_rss_feed_2.0 = "导入RSS Feed 2.0"
insert_image = "插入图片"
invalid_error = "用户名或密码错误"
language = "系统语言"
link_name = "链接名称"
link_post = "链接文章"
link_post_comment = "创建有特色链接的文章"
login = "登录"
login_page = "登录页面"
logout = "登出"
menu = "菜单"
menus = "菜单编辑器"
meta_description = "元数据描述"
meta_description_character = "元描述字数"
metatags = "元标签"
metatags_settings = "元标签设置"
mine = "我的"
more = "更多"
my_draft = "我的草稿"
my_posts = "我的文章"
name = "名称"
newer = "较新"
next = "下一个"
next_post = "下一个文章"
no_available_backup = "现在没有可用备份"
no_draft_found = "没有找到草稿"
no_posts_found = "没有找到文章"
no_related_post_found = "没有找到相关文章"
no_scheduled_posts_found = "没有计划发布的文章！"
no_search_results = "没有搜索结果"
nope = "否"
not = "否"
older = "较旧"
only = "只有"
operations = "操作"
page = "页面"
page_generation_time = "页面创建时间"
pages = "页面"
pass_error = "密码输入框没有内容"
password = "密码"
performance = "性能"
performance_settings = "性能设置"
permalink = "外链位置"
popular = "热门"
popular_posts = "热门文章"
popular_posts_widget = "热门文章组件"
popular_posts_widget_at_most = "“热门”组件最多文章数"
popular_tags = "热门标签"
post_by_author = "这个作者的文章"
posted_in = "发布于"
posted_on = "发布于"
posts = "文章"
posts_by = "文章来自"
posts_draft = "文章草稿"
posts_in_archive_page_at_most = "归档页最多文章数"
posts_in_category_page_at_most = "分类页最多文章数"
posts_in_front_page_show_at_most = "主页最多文章数"
posts_in_profile_page_at_most = "个人资料页最多文章数"
posts_in_search_result_at_most = "搜索结果页最多文章数"
posts_in_tag_page_at_most = "标签页最多文章数"
posts_in_type_page_at_most = "类型页最多文章数"
posts_index_settings = "文章索引设置"
posts_list = "文章列表"
posts_tagged = "带指定标签的文章："
posts_with_type = "指定类型的文章："
pre_release = "接收预发布版本"
prev = "旧"
prev_post = "上一个文章"
preview = "预览"
profile_for = "个人资料："
proudly_powered_by = "自豪的采用"
publish = "发布"
publish_draft = "发布草稿"
published = "发布"
quote_post = "引用文章"
quote_post_comment = "创建有特色引用的文章"
rss_character = "RSS字数"
rss_feeds_show_the_most_recent = "RSS feed显示最近文章数"
rss_settings = "RSS设置"
read_more_text = "“查看全文”文本"
read_more_text_placeholder = "阅读全文"
reading = "阅读"
reading_settings = "阅读设置"
recaptcha = "reCAPTCHA"
recent_posts = "最近文章"
recent_posts_widget_at_most = "“最近”组件最多文章数"
regular_post = "普通文章"
regular_post_comment = "创建普通文章"
related_posts = "相关文章"
related_widget_posts_at_most = "“相关”组件最多文章数"
revert_to_draft = "转换成草稿"
save = "保存"
save_config = "保存配置"
save_edit = "保存编辑"
save_menu = "保存菜单"
save_as_draft = "作为草稿保存"
save_category = "保存分类"
scheduled = "计划发布"
scheduled_posts = "计划发布的文章"
scheduled_tips = "设定一个未来的时间和日期以启动计划发布"
search = "搜索"
search_for = "搜索"
search_results_for = "搜索结果："
search_results_not_found = "没有找到搜索结果"
secret_key = "Secret Key"
settings = "设置"
sign_in_to_start_your_session = "登录以开始你的会话"
site_key = "Site Key"
sitemap = "站点地图"
slug = "外链"
social_media = "社交媒体"
static_page = "静态页面"
static_page_comment = "创建静态页面"
static_pages = "静态页面"
summary = "摘要"
summary_character = "摘要字数"
tag = "标签"
tagcloud_widget_at_most = "云标签最多标签数"
tagline = "标语"
tagline_placeholder = "无需数据库的PHP博客平台"
tagline_description = "用几个字简述这个博客是关于什么的"
tags = "标签"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "通常情况下新创建的统计使用gtag.js"
this_page_doesnt_exist = "这个页面不存在！"
time = "时间"
timezone = "时区"
title = "标题"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "要使用Disqus或Facebook评论，你需要提供你的Disqus shortname或你的Facebook App ID"
token_error = "CSRF Token不正确"
tools = "工具"
twitter_account = "Twitter账号"
type_to_search = "输入搜索内容"
uncategorized = "未分类"
uncategorized_comment = "不需要分类的或者不适合任何分类的文章"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "订阅源格式未知"
update = "更新"
update_available = "更新可用"
update_draft = "更新草稿"
update_post = "更新文章"
update_to = "更新到"
upload = "上传"
user = "用户"
user_error = "用户输入框没有内容"
valid_values_range_from_0_to_1.0._see = "有效范围从0.0到1.0。参考"
video_post = "视频文章"
video_post_comment = "创建有特色视频的文章"
view = "查看"
view_post = "查看"
views = "查看"
widget = "组件"
widget_settings = "组件设置"
would_you_like_to_try_our = "你想不想试试我们的"
yes_im_in = "是"
yes_not_recommended = "是（不推荐）"
you_dont_have_permission_to_access_this_page = "你没有访问此页面的权限"
your_new_config_key = "新配置项"
your_new_value = "值"
your_backups = "你的备份"
your_latest_blog_posts = "最新的文章"
your_recent_posts = "你的最近文章"
by = "来自"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>提示：</u>用<code>Ctrl</code>/<code>CMD</code> + <code>F</code>搜索配置项或者值"
homepage = "主页"
instead = "instead"
item_class = "CSS类"
item_slug = "链接URL"
now = "现在"
of = "于"
optional = "选填"
post_your_post_slug = "/post/文章外链"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>专业提示：</u>你可以在模板的任何地方创建自定义的配置项并打印出对应的值"
read_more = "阅读全文"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/年/月/文章外链"
your_key = "your.key"
summary_behavior = "摘要行为"
default = "默认"
check_shortcode = "检查短代码"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "摘要模式下, 在将内容修剪为 x 字符之前，是否先检查短代码"
manage_users = "用户管理"
add_user = "添加用户"
username = "用户名"
role = "角色"
change_password = "修改密码"
config_mfa = "配置 MFA"
mfacode = "MFA 代码"
verify_code = "验证 MFA 代码"
verify_password = "验证当前密码"
manualsetupkey = "你可以手动添加配置密钥"
mfa_error = "MFA 代码不正确"
disablemfa = "禁用 MFA"
enable_auto_save = "启用自动保存"
explain_autosave = "启用后, 新文章或者草稿会每隔 60 秒自动保存"
login_protect_system = "Login protection system"
cloudflare_info = "参见 Cloudflare's Turnstile 文档: "
mfa_config = "多因素身份验证 (MFA)"
set_mfa_globally = "设置 MFA 状态"
explain_mfa = "启用后, MFA对所有用户都是可选的。禁用后，任何用户都无法使用它，它会隐藏登录页面上的字段。"
set_version_publicly = "版本号可见"
explain_version = "默认显示 HTMLy 版本号, 也可以设置隐藏."
focus_mode = "Toggle Focus"
writing = "写作"
writing_settings = "写作设置"
security = "安全"
security_settings = "安全设置"
msg_error_field_req_username = "用户名字段必填"
msg_error_field_req_password = "密码字段必填"
msg_error_field_req_title = "标题字段必填"
msg_error_field_req_content = "内容字段必填"
msg_error_field_req_tag = "标签字段必填"
msg_error_field_req_image = "图片字段必填"
msg_error_field_req_video = "视频字段必填"
msg_error_field_req_link = "链接字段必填"
msg_error_field_req_quote = "引用字段必填"
msg_error_field_req_audio = "音频字段必填"
msg_error_field_req_feedurl = "你需要指定 feed url."
rss_feeds_description_select = "RSS 描述"
rss_description_body = "文章内容"
rss_description_meta = "文章描述"
admin_theme = "后台主题"
admin_theme_light = "Light(明亮)"
admin_theme_dark = "Dark(暗色)"
search_index = "搜索索引"
fulltext_search = "全文搜索"
add_search_index = "添加文章到索引"
clear_search_index = "清除搜索索引"
unindexed_posts = "未被索引的文章"
indexed_posts = "文章已被索引"
custom_fields = "Custom fields"
