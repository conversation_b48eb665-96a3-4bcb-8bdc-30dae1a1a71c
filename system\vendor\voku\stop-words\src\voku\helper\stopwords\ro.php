<?php

// romanian

static $data = [
    'vreo',
    'acelea',
    'cita',
    'degraba',
    'lor',
    'alta',
    'tot',
    'ai',
    'dat',
    'x',
    'despre',
    'peste',
    'bine',
    'dar',
    'foarte',
    'z',
    'avea',
    'multi',
    'cit',
    'alt',
    'mai',
    'sa',
    'fie',
    'tu',
    'multe',
    'e',
    'orice',
    'dintr',
    'se',
    'g',
    'intr',
    'niste',
    'multa',
    'insa',
    'il',
    'fost',
    'a',
    'abia',
    'nimic',
    'sub',
    'acel',
    'in',
    'altceva',
    'si',
    'avem',
    'altfel',
    'c',
    'ea',
    'acest',
    'li',
    'parca',
    'fi',
    'dintre',
    'unele',
    'm',
    'acestei',
    'mare',
    'cel',
    'este',
    'pe',
    'atitia',
    'uneori',
    'acela',
    'iti',
    'asta<PERSON>',
    'acestui',
    'o',
    'imi',
    'ele',
    'ceilalti',
    'pai',
    'fata',
    'noua',
    'sa-ti',
    'altul',
    'au',
    'i',
    'prin',
    'conform',
    'aceste',
    'anume',
    'azi',
    'k',
    'unul',
    'ala',
    'unei',
    'fara',
    'ei',
    'la',
    'aceeasi',
    'u',
    'inapoi',
    'acestea',
    'acesta',
    'catre',
    'sale',
    'asupra',
    'as',
    'aceea',
    'ba',
    'ale',
    'da',
    'le',
    'apoi',
    'aia',
    'suntem',
    'cum',
    'isi',
    'inainte',
    's',
    'de',
    'cind',
    'cumva',
    'chiar',
    'acestia',
    'daca',
    'sunt',
    'care',
    'al',
    'numai',
    'cui',
    'sus',
    'tocmai',
    'prea',
    'cu',
    'mi',
    'eu',
    'doar',
    'niciodata',
    'exact',
    'putini',
    'aiurea',
    'tuturor',
    'celor',
    'astfel',
    'atunci',
    'citeva',
    'cat',
    'sau',
    'fel',
    'intre',
    'acolo',
    'nostri',
    'ma',
    'mult',
    'una',
    'ceea',
    'iar',
    'sintem',
    'ati',
    'din',
    'geaba',
    'sai',
    'caruia',
    'adica',
    'inca',
    'are',
    'aici',
    'ca',
    'ia',
    'nici',
    'd',
    'oricum',
    'asta',
    'carora',
    'face',
    'citiva',
    'voi',
    'unor',
    'f',
    'atat',
    'toata',
    'alaturi',
    'cea',
    'nu',
    'totusi',
    'ce',
    'altii',
    'acum',
    'sint',
    'capat',
    'mod',
    'deasupra',
    'cam',
    'vom',
    'b',
    'toate',
    'careia',
    'aceasta',
    'atit',
    'nimeni',
    'ii',
    'ci',
    'unde',
    'ul',
    'plus',
    'era',
    'sa-mi',
    'l',
    'spre',
    'dupa',
    'nou',
    'cele',
    'acea',
    'un',
    'incit',
    'n',
    'cei',
    'or',
    'va',
    'deci',
    'acelasi',
    'atatea',
    'h',
    'vor',
    'decit',
    'noi',
    'cineva',
    'desi',
    'ceva',
    'j',
    'ului',
    'atitea',
    'avut',
    'ar',
    'pina',
    't',
    'atata',
    'unui',
    'el',
    'citi',
    'asa',
    'totul',
    'pentru',
    'atita',
    'v',
    'alti',
    'asemenea',
    'atatia',
    'te',
    'ne',
    'deja',
    'unii',
    'p',
    'atare',
    'cite',
    'cine',
    'cand',
    'toti',
    'vreun',
    'ori',
    'r',
    'alte',
    'lui',
    'ti',
    'ni',
    'aceia',
    'am',
];

$result =& $data;
unset($data);
return $result;
