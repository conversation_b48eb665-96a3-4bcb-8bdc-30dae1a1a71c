{"name": "jbroadway/urlify", "type": "library", "description": "A fast PHP slug generator and transliteration library that converts non-ascii characters for use in URLs.", "keywords": ["urlify", "transliterate", "translit", "transliteration", "url", "encode", "downcode", "slug", "slugify", "slugs", "link", "iconv", "blogging", "blogs", "unicode", "ascii", "seo"], "homepage": "https://github.com/jbroadway/urlify", "license": "BSD-3-<PERSON><PERSON>-<PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.johnnybroadway.com/"}], "require": {"php": ">=7.2", "voku/portable-ascii": "^2.0", "voku/stop-words": "^2.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "autoload": {"psr-0": {"URLify": ""}}, "extra": {"branch-alias": {"dev-master": "1.0-dev"}}}