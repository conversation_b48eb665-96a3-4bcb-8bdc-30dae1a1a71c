about = "درباره ما"
add_category = "افزودن دسته بندی"
add_content = "افزودن محتوا"
add_link = "افزودن لینک"
add_menu = "افزودن منو"
add_new_page = "افزودن صفحه جدید"
add_new_post = "افزودن پست جدید"
add_source_link_optional = "Add source link (optional)"
add_sub = "افزودن صفحه فرعی"
address_url = "Address (URL)"
admin = "ادمین"
admin_panel_style_based_on = "Admin panel style based on"
all_blog_posts = "تمام ارسالی ها"
all_cache_has_been_deleted = "حافظه کش پاک شد!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "آرشیو"
are_you_sure_you_want_to_delete_ = "Are you sure you want to delete <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "At the moment you are using auto generated menu."
audio_post = "پست های صوتی"
audio_post_comment = "Creating blog post with featured audio"
author = "نویسنده"
author_description = "Just another HTMLy user"
back_to = "برگشت به"
backup = "پشتیبان"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "In one paragraph, tell us more about your blog."
blog_theme = "تم وبلاگ"
blog_title = "عنوان وبلاگ"
blog_title_placeholder = "My HTMLy Blog"
blog_posts_displayed_as = "Blog posts displayed as"
breadcrumb_home_text = "Breadcrumb home text"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "By using this importer you are agree if the feed is yours or at least you have the authority to publish it."
css_class_optional = "CSS Class (optional)"
cache_expiration = "Cache expiration (in hours)"
cache_off = "Cache off"
cache_timestamp = "Cache timestamp"
cancel = "انصراف"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha not correct"
categories = "دسته بندی ها"
category = "دسته بندی"
check_update = "بررسی به روزرسانی"
clear_cache = "پاک کردن حافظه کش"
comma_separated_values = "Comma separated values"
comment_system = "Comment system"
comments = "نظرات"
config = "تنظیمات"
congrats_you_have_the_latest_version_of_htmly = "Congrats! You have the latest version of HTMLy."
content = "محتوا"
contents = "فهرست"
copyright_line = "خط کپی رایت"
copyright_line_placeholder = "(c) Your name."
create_backup = "ساجاد نسخه پشتیبان"
created = "ایجاد شد"
custom = "سفارشی"
custom_settings = "تنظیمات سفارشی"
dashboard = "داشبورد"
date = "تاریخ"
date_format = "فرمت تاریخ"
delete = "حذف"
description = "توضیحات"
disable = "غیر فعال"
disabled = "غیرفعال شد"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "پیش نویس"
edit = "ویرایش"
edit_category = "ویرایش دسته بندی"
edit_post = "ویرایش"
edit_profile = "ویرایش پروفایل"
enable = "فعال"
enable_blog_url = "Enable blog URL"
enter_image_url = "ورود آدرس تصویر"
facebook_app_id = "Facebook App ID"
facebook_page = "Facebook page"
featured_audio = "Featured Audio"
featured_image = "Featured Image"
featured_link = "Featured Link"
featured_quote = "Featured Quote"
featured_video = "Featured Video"
feed_url = "Feed URL"
filename = "نام فایل"
follow = "دنبال کردن"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "نمایش صفحه اول"
full_post = "پست کامل"
general = "عمومی"
general_settings = "تنظیمات عمومی"
get_one_here = "Get one here"
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "صفحه اصلی"
if_left_empty_we_will_excerpt_it_from_the_content_below = "If leave empty we will excerpt it from the content below"
if_the_url_is_left_empty_we_will_use_the_page_title = "If the url leave empty we will use the page title"
if_the_url_is_left_empty_we_will_use_the_post_title = "If the url leave empty we will use the post title"
image_post = "ارسال تصویر"
image_post_comment = "ایجاد پست وبلاگ به همراه تصویر"
import = "وارد كردن"
import_feed = "Start Import Feed"
import_rss = "Import RSS"
import_rss_feed_2.0 = "Import RSS Feed 2.0"
insert_image = "درج تصویر"
invalid_error = "ERROR: Invalid username or password"
language = "زیان سیستم"
link_name = "نام لینک"
link_post = "لینک پست"
link_post_comment = "ایجاد پست وبلگ به همراه لینک"
login = "ورود"
login_page = "صفحه ورود"
logout = "خروج"
menu = "منو"
menus = "Menu Editor"
meta_description = "توضیحات متا"
meta_description_character = "Meta description character"
metatags = "متاتگ"
metatags_settings = "Metatags Settings"
mine = "Mine"
more = "بیشتر"
my_draft = "پیش نویس های من"
my_posts = "پست های من"
name = "نام"
newer = "جدیدتر"
next = "بعد"
next_post = "پست بعدی"
no_available_backup = "هیچ نسخه پشتیبانی وجود ندارد"
no_draft_found = "پیش نویس یافت نشد"
no_posts_found = "هیچ پستی ارسال نشده است"
no_related_post_found = "پست مرتبطی یافت نشد"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "No search results"
nope = "Nope"
not = "No"
older = "قدیمی ترها"
only = "فقط"
operations = "عملیات"
page = "صفحه"
page_generation_time = "Page generation time"
pages = "صفحه ها"
pass_error = "Password field is required"
password = "کلمه عبور"
performance = "پرفورمنس"
performance_settings = "Performance Settings"
permalink = "پیوند ثابت"
popular = "محبوب"
popular_posts = "پست های محبوب"
popular_posts_widget = "Popular posts widget"
popular_posts_widget_at_most = "Popular posts widget at most"
popular_tags = "تگ های محبوب"
post_by_author = "Posts by this author"
posted_in = "ارسال شده"
posted_on = "Posted on"
posts = "پست ها"
posts_by = "ارسال توسط"
posts_draft = "Posts draft"
posts_in_archive_page_at_most = "Posts in archive page at most"
posts_in_category_page_at_most = "Posts in category page at most"
posts_in_front_page_show_at_most = "Posts in front page show at most"
posts_in_profile_page_at_most = "Posts in profile page at most"
posts_in_search_result_at_most = "Posts in search result at most"
posts_in_tag_page_at_most = "Posts in tag page at most"
posts_in_type_page_at_most = "Posts in type page at most"
posts_index_settings = "Posts index settings"
posts_list = "لیست پست ها"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pre-release"
prev = "قدیمی"
prev_post = "پست قبلی"
preview = "مشاهده"
profile_for = "Profile for"
proudly_powered_by = "Proudly powered by"
publish = "انتشار"
publish_draft = "انتشار پیش نویس"
published = "منتشر شد"
quote_post = "Quote post"
quote_post_comment = "Creating blog post with featured quote"
rss_character = "RSS character"
rss_feeds_show_the_most_recent = "RSS feeds show the most recent"
rss_settings = "RSS settings"
read_more_text = "Read more text"
read_more_text_placeholder = "ادامه مطلب"
reading = "خواندن"
reading_settings = "Reading Settings"
recaptcha = "reCAPTCHA"
recent_posts = "پست های اخیر"
recent_posts_widget_at_most = "Recent posts widget at most"
regular_post = "پست عادی"
regular_post_comment = "ایجاد پست عادی وبلاگ"
related_posts = "پست های مرتبط"
related_widget_posts_at_most = "Related widget posts at most"
revert_to_draft = "Revert to draft"
save = "ذخیره"
save_config = "ذخیره تنظیمات"
save_edit = "Save Edit"
save_menu = "Save menu"
save_as_draft = "Save as draft"
save_category = "دخیره دسته بندی"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "جستجو"
search_for = "جستجو برای"
search_results_for = "Search results for"
search_results_not_found = "Search results not found!"
secret_key = "Secret Key"
settings = "تنظیمات"
sign_in_to_start_your_session = "Sign in to start your session"
site_key = "Site Key"
sitemap = "نقشه سایت"
slug = "Slug"
social_media = "رسانه های اجتماعی"
static_page = "صفحه ایستا"
static_page_comment = "ایجاد صفحه ایستا"
static_pages = "صفحه های ایستا"
summary = "خلاصه"
summary_character = "Summary character"
tag = "تگ"
tagcloud_widget_at_most = "TagCloud at most"
tagline = "Tagline"
tagline_placeholder = "Databaseless PHP Blogging Platform"
tagline_description = "In a few words, explain what this blog is about."
tags = "تگ ها"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "This is legacy code. Usually new created analytics using gtag.js"
this_page_doesnt_exist = "این صفحه وجود ندارد"
time = "ساعت"
timezone = "Timezone"
title = "عنوان"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "To use Disqus or Facebook comments you need to provide your Disqus shortname or your Facebook App ID."
token_error = "CSRF Token not correct"
tools = "ابزار"
twitter_account = "Twitter account"
type_to_search = "برای جستجو تایپ کنید"
uncategorized = "دسته بندی نشده"
uncategorized_comment = "Topics that don't need a category, or don't fit into any other existing category"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "به روز رسانی"
update_available = "Update Available"
update_draft = "به روز رسانی پیش نویس"
update_post = "به روز رسانی پست"
update_to = "به روزرسانی به"
upload = "ارسال"
user = "نام کاربری"
user_error = "User field is required"
valid_values_range_from_0_to_1.0._see = "Valid values range from 0.0 to 1.0. See"
video_post = "پست ویدیویی"
video_post_comment = "Creating blog post with featured video"
view = "View"
view_post = "View"
views = "Views"
widget = "ویجت"
widget_settings = "Widget Settings"
would_you_like_to_try_our = "Would you like to try our "
yes_im_in = "Yes I'm in"
yes_not_recommended = "Yes (not recommended)"
you_dont_have_permission_to_access_this_page = "You don't have permission to access this page"
your_new_config_key = "Your New Config Key"
your_new_value = "Your New Value"
your_backups = "Your backups"
your_latest_blog_posts = "آخرین پست های وبلاگ"
your_recent_posts = "آخرین پست ها"
by = "توسط"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>hint:</u> Use <code>Ctrl</code>/<code>CMD</code> + <code>F</code> to search for your config key or value."
homepage = "صفحه اصلی"
instead = "به جای"
item_class = "Insert CSS class"
item_slug = "افزودن لینک"
now = "now"
of = "of"
optional = "اختیاری"
post_your_post_slug = "/post/your-post-slug"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>pro tips:</u> You can create custom config keys and print out your config key value anywhere in your template."
read_more = "read more"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/year/month/your-post-slug"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
