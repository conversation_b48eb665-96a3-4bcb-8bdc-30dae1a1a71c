Just a [URL](/url/).

[URL and title](/url/ "title").

[URL and title](/url/  "title preceded by two spaces").

[URL and title](/url/	"title preceded by a tab").

[URL and title](/url/ "title has spaces afterward"  ).

[URL wrapped in angle brackets](</url/>).

[URL w/ angle brackets + title](</url/> "Here's the title").

[Empty]().

[With parens in the URL](http://en.wikipedia.org/wiki/WIMP_(computing))

(With outer parens and [parens in url](/foo(bar)))


[With parens in the URL](/foo(bar) "and a title")

(With outer parens and [parens in url](/foo(bar) "and a title"))
