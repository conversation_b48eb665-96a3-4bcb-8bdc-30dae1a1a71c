<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '1.0.0+no-version-set',
        'version' => '1.0.0.0',
        'reference' => null,
        'type' => 'library',
        'install_path' => __DIR__ . '/../../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '1.0.0+no-version-set',
            'version' => '1.0.0.0',
            'reference' => null,
            'type' => 'library',
            'install_path' => __DIR__ . '/../../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => 'v3.0.0',
            'version' => '3.0.0.0',
            'reference' => '510de6eca6248d77d31b339d62437cc995e2fb41',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'composer/ca-bundle' => array(
            'pretty_version' => '1.5.0',
            'version' => '1.5.0.0',
            'reference' => '0c5ccfcfea312b5c5a190a21ac5cef93f74baf99',
            'type' => 'library',
            'install_path' => __DIR__ . '/./ca-bundle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.5',
            'version' => '1.0.5.0',
            'reference' => '6faf451159fb8ba4126b925ed2d78acfce0dc016',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'ircmaxell/password-compat' => array(
            'pretty_version' => 'v1.0.4',
            'version' => '1.0.4.0',
            'reference' => '5c5cde8822a69545767f7c7f3058cb15ff84614c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ircmaxell/password-compat',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'jbroadway/urlify' => array(
            'pretty_version' => '1.2.4-stable',
            'version' => '1.2.4.0',
            'reference' => 'd0fafbaa1dc14e8039cdf5c72a932a8d1de1750e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../jbroadway/urlify',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'kanti/hub-updater' => array(
            'pretty_version' => 'v0.5.1',
            'version' => '0.5.1.0',
            'reference' => '014b33c1e3880bd8e037a960a89e7116eb08a26e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../kanti/hub-updater',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'michelf/php-markdown' => array(
            'pretty_version' => '1.9.1',
            'version' => '1.9.1.0',
            'reference' => '5024d623c1a057dcd2d076d25b7d270a1d0d55f3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../michelf/php-markdown',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'paragonie/constant_time_encoding' => array(
            'pretty_version' => 'v2.7.0',
            'version' => '2.7.0.0',
            'reference' => '52a0d99e69f56b9ec27ace92ba56897fe6993105',
            'type' => 'library',
            'install_path' => __DIR__ . '/../paragonie/constant_time_encoding',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'pragmarx/google2fa' => array(
            'pretty_version' => 'v8.0.1',
            'version' => '8.0.1.0',
            'reference' => '80c3d801b31fe165f8fe99ea085e0a37834e1be3',
            'type' => 'library',
            'install_path' => __DIR__ . '/../pragmarx/google2fa',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'suin/php-rss-writer' => array(
            'pretty_version' => '1.6.0',
            'version' => '1.6.0.0',
            'reference' => '78f45e44a2a7cb0d82e4b9efb6f7b7a075b9051c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../suin/php-rss-writer',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/portable-ascii' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => 'b56450eed252f6801410d810c8e1727224ae0743',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/portable-ascii',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'voku/stop-words' => array(
            'pretty_version' => '2.0.1',
            'version' => '2.0.1.0',
            'reference' => '8e63c0af20f800b1600783764e0ce19e53969f71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../voku/stop-words',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
