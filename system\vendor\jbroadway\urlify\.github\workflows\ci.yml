name: "Continuous Integration"

on:
  - push
  - pull_request

env:
  COMPOSER_FLAGS: "--no-interaction --prefer-dist"

jobs:
  tests:
    name: "CI"
    
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        php-version:
          - "7.2"
          - "7.3"
          - "7.4"
          - "8.0"
        
        dependencies: [highest]
    
    steps:
      - name: "Checkout"
        uses: "actions/checkout@v2"
    
      - name: "Setup PHP"
        uses: "shivammathur/setup-php@v2"
        with:
          coverage: "none"
          php-version: "${{ matrix.php-version }}"
    
      - name: "Install dependencies"
        run: |
          composer update ${{ env.COMPOSER_FLAGS }}
    
      - name: "Run tests"
        run: "composer exec phpunit -- --verbose"
