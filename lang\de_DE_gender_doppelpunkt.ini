about = "Über"
add_category = "Kategorie hinzufügen"
add_content = "Inhalt hinzufügen"
add_link = "Link hinzufügen"
add_menu = "Menü hinzufügen"
add_new_page = "Neue Seite hinzufügen"
add_new_post = "Neuen Beitrag hinzufügen"
add_source_link_optional = "Quell-Link hinzufügen (optional)"
add_sub = "Unterseite hinzufügen"
address_url = "Adresse (URL)"
admin = "Administrator:in"
admin_panel_style_based_on = "Admin-Panel-Stil basierend auf"
all_blog_posts = "Alle Beiträge"
all_cache_has_been_deleted = "Der gesamte Cache wurde gelöscht!"
all_posts_tagged = "Alle Beiträge getaggten"
archive_for = "Archiv für"
archive_page_for = "Archivseite für"
archives = "Archiv"
are_you_sure_you_want_to_delete_ = "Sind Sie sicher, dass Sie <strong>%s</strong> löschen wollen?"
at_the_moment_you_are_using_auto_generated_menu = "Im Moment verwenden Sie ein automatisch generiertes Menü."
audio_post = "Audio-Beitrag"
audio_post_comment = "Einen Beitrag zur Präsentation eines Audios erstellen"
author = "Autor:in"
author_description = "Nur ein:e weitere:r HTMLy-Benutzer:in"
back_to = "Zurück zu"
backup = "Backup"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "Beschreiben Sie in einem Absatz die Ausrichtung Ihres Blogs."
blog_posts_displayed_as = "Blog-Beiträge anzeigen als"
blog_theme = "Blog-Theme (Design)"
blog_title = "Blog-Titel"
blog_title_placeholder = "Ein HTMLy-Blog"
breadcrumb_home_text = "Bezeichnung für die Startseite im Breadcrumb-Menü"
by = "von"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Durch die Verwendung dieses Importeurs bestätigen Sie, dass der importierte Feed Ihnen gehört oder Sie berechtigt sind, seine Inhalte zu veröffentlichen."
cache_expiration = "Cache-Gültigkeitsdauer (in Stunden)"
cache_off = "Caching ausschalten"
cache_timestamp = "Cache-Zeitstempel"
cancel = "Abbrechen"
cannot_read_feed_content = "Feedinhalt kann nicht gelesen werden"
captcha_error = "reCAPTCHA nicht korrekt"
categories = "Kategorien"
category = "Kategorie"
check_shortcode = "Berücksichtige shortcode"
check_update = "Aktualisierung suchen"
clear_cache = "Cache löschen"
comma_separated_values = "Komma-getrennte Werte"
comment_system = "Kommentarsystem"
comments = "Kommentare"
config = "Konfiguration"
congrats_you_have_the_latest_version_of_htmly = "Herzlichen Glückwunsch! Sie haben die neueste Version von HTMLy."
content = "Inhalt"
contents = "Inhalt"
copyright_line = "Copyright-Zeile"
copyright_line_placeholder = "(c) Ihr Name."
create_backup = "Ein Backup erstellen"
created = "Erstellungsdatum"
css_class_optional = "CSS-Klasse (optional) "
custom = "Benutzerdefiniert"
custom_settings = "Benutzerdefinierte Einstellungen"
dashboard = "Übersicht"
date = "Datum"
date_format = "Datumsformat"
default = "Standard"
delete = "Löschen"
description = "Beschreibung"
disable = "Deaktivieren"
disabled = "Ausgeschaltet"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "Entwurf"
edit = "Bearbeiten"
edit_category = "Kategorie bearbeiten"
edit_post = "Bearbeiten"
edit_profile = "Profil bearbeiten"
enable = "Aktivieren"
enable_blog_url = "URL blog aktivieren"
enter_image_url = "Bild-URL eingeben"
facebook_app_id = "Facebook App-ID"
facebook_page = "Facebook-Seite"
featured_audio = "Ausgewähltes Audio"
featured_image = "Ausgewähltes Bild"
featured_link = "Ausgewählter Link"
featured_quote = "Ausgewähltes Zitat"
featured_video = "Ausgewähltes Video"
feed_url = "Feed-URL"
filename = "Dateiname"
follow = "Folgen"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Startseite zeigt"
full_post = "Vollständiger Beitrag"
general = "Allgemeines"
general_settings = "Allgemeine Einstellungen"
get_one_here = "Get one here"
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>Hinweis:</u> Nutzen Sie <code>STRG</code>/<code>CMD</code> + <code>F</code>, um nach einem Konfigurationsschlüssel oder Wert zu suchen."
home = "Startseite"
homepage = "Startseite"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Wenn das Feld leer bleibt, wird es aus dem folgenden Inhalt extrahiert"
if_the_url_is_left_empty_we_will_use_the_page_title = "Wenn die URL leer bleibt, wird der Seitentitel verwendet"
if_the_url_is_left_empty_we_will_use_the_post_title = "Wenn die URL leer bleibt, wird der Beitragstitel verwendet"
image_post = "Bild-Beitrag"
image_post_comment = "Einen Beitrag zur Präsentation eines Bildes erstellen"
import = "Importieren"
import_feed = "Feed importieren"
import_rss = "RSS importieren"
import_rss_feed_2.0 = "RSS-Feed 2.0 importieren"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "Bei Anzeige als Zusammenfassung wird entweder ein vorhandener shortcode ignoriert und die Zusammenfassung auf die angegebene Anzahl von Zeichen gekürzt (Standard) oder bis zum shortcode angezeigt (Berücksichtige shortcode)."
insert_image = "Bild einfügen"
instead = "stattdessen"
invalid_error = "FEHLER: Ungültiger Benutzer:innen-Name oder Passwort"
item_class = "CSS-Klasse"
item_slug = "Link-URL"
language = "Systemsprache"
link_name = "Name des Links"
link_post = "Link-Beitrag"
link_post_comment = "Einen Beitrag zur Präsentation eines Links erstellen"
login = "Anmeldung"
login_page = "Anmeldeseite"
logout = "Abmelden"
menu = "Menüs"
menus = "Menü-Editor"
meta_description = "Meta-Beschreibung"
meta_description_character = "Anzahl von Zeichen, die vom Inhalt übernommen werden, wenn das Feld Meta-Beschreibung leer gelassen wird"
metatags = "Metatags"
metatags_settings = "Metatags-Einstellungen"
mine = "Eigene Beiträge"
more = "Mehr"
my_draft = "Eigene Entwürfe"
my_posts = "Eigene Beiträge"
name = "Name"
newer = "Neuere"
next = "Nächster"
next_post = "Nächster Beitrag"
no_available_backup = "Derzeit ist kein Backup verfügbar."
no_draft_found = "Keine Entwürfe gefunden!"
no_posts_found = "Keine Beiträge gefunden!"
no_related_post_found = "Kein ähnlicher Beitrag gefunden!"
no_scheduled_posts_found = "Keine geplanten Beiträge gefunden!"
no_search_results = "Keine Suchergebnisse!"
nope = "Nein, danke."
not = "Nein"
now = "jetzt"
of = "von"
older = "Ältere"
only = "Nur"
operations = "Operationen"
optional = "optional"
page = "Seite"
page_generation_time = "Dauer der Seitenerzeugung"
pages = "Seiten"
pass_error = "Passwortfeld ist erforderlich!"
password = "Passwort"
performance = "Leistung"
performance_settings = "Leistungseinstellungen"
permalink = "Permalink"
popular = "Zugriffe"
popular_posts = "Beliebte Beiträge"
popular_posts_widget = "Zugriffszähler aktivieren und Widget Beliebte Beiträge einblenden"
popular_posts_widget_at_most = "Maximale Anzahl von Beiträgen im Widget Beliebte Beiträge"
popular_tags = "Beliebte Tags"
post_by_author = "Beiträge dieser Autorin oder dieses Autors"
post_your_post_slug = "/post/your-post-slug"
posted_in = "Veröffentlicht unter"
posted_on = "Veröffentlicht am"
posts = "Inhalte"
posts_by = "Beiträge von"
posts_draft = "Entwürfe"
posts_in_archive_page_at_most = "Maximale Anzahl von Beiträgen auf Archiv-Seiten"
posts_in_category_page_at_most = "Maximale Anzahl von Beiträgen auf Kategorien-Seiten"
posts_in_front_page_show_at_most = "Maximale Anzahl von Beiträgen auf der Startseite"
posts_in_profile_page_at_most = "Maximale Anzahl von Beiträgen auf Profil-Seiten"
posts_in_search_result_at_most = "Maximale Anzahl von Beiträgen in Suchergebnissen"
posts_in_tag_page_at_most = "Maximale Anzahl von Beiträgen auf tag-Seiten"
posts_in_type_page_at_most = "Posts in type page at most"
posts_index_settings = "Einstellungen zur Anzahl von Beiträgen"
posts_list = "Beiträge"
posts_tagged = "Beiträge mit Stichwort"
posts_with_type = "Beiträge mit Typ"
pre_release = "Pre-release"
prev = "Voriger"
prev_post = "Vorheriger Beitrag"
preview = "Vorschau"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>Profi-Tipp:</u> Sie können einen eigenen Konfigurationsschlüssel erzeugen und dessen Wert überall im Template anzeigen lassen."
profile_for = "Profil von"
proudly_powered_by = "Powered by"
publish = "Veröffentlichen"
publish_draft = "Veröffentlichen"
published = "Veröffentlicht"
quote_post = "Zitat-Beitrag"
quote_post_comment = "Einen Beitrag zur Präsentation eines Zitats erstellen"
read_more = "weiterlesen"
read_more_text = "Weiterlesen-Text"
read_more_text_placeholder = "Mehr ..."
reading = "Lesen"
reading_settings = "Leseeinstellungen"
recaptcha = "reCAPTCHA"
recent_posts = "Letzte Beiträge"
recent_posts_widget_at_most = "Maximale Anzahl von Beiträgen im Widget Letzte Beiträge"
regular_post = "Normaler Beitrag"
regular_post_comment = "Einen normalen Beitrag erstellen"
related_posts = "Ähnliche Beiträge"
related_widget_posts_at_most = "Maximale Anzahl von Beiträgen im Widget Ähnliche Beiträge"
revert_to_draft = "Als Entwurf speichern"
rss_character = "Länge eines RSS-Beitrags (in Zeichen)"
rss_feeds_show_the_most_recent = "RSS-Feeds zeigen die folgende Anzahl der aktuellsten Beiträge"
rss_settings = "RSS-Einstellungen"
save = "Speichern"
save_as_draft = "Speichern als Entwurf"
save_category = "Kategorie speichern"
save_config = "Konfiguration speichern"
save_edit = "Änderungen speichern"
save_menu = "Menü speichern"
scheduled = "Geplant"
scheduled_posts = "Geplante Beiträge"
scheduled_tips = "Veröffentlichen eines Beitrags mit Zeitpunkt in der Zukunft, fügt ihn bei Geplante Beiträge hinzu"
search = "Suche"
search_for = "Suche nach"
search_results_for = "Suchergebnisse für"
search_results_not_found = "Suchergebnisse nicht gefunden!"
secret_key = "Secret Key"
settings = "Einstellungen"
sign_in_to_start_your_session = "Melden Sie sich an, um Ihre Sitzung zu starten"
site_key = "Site Key"
sitemap = "Sitemap"
slug = "Slug"
social_media = "Social Media"
static_page = "Statische Seite"
static_page_comment = "Eine statische Seite erstellen"
static_pages = "Statische Seiten"
summary = "Zusammenfassung"
summary_behavior = "Generieren der Zusammenfassung"
summary_character = "Länge der Zusammenfassung (in Zeichen)"
tag = "Schlagwort"
tagcloud_widget_at_most = "Höchstzahl Schlagwörter in Tag-Cloud"
tagline = "Slogan"
tagline_description = "Erklären Sie in wenigen Worten, worum es in diesem Blog geht."
tagline_placeholder = "Datenbanklose PHP-Blogging-Plattform"
tags = "Schlagworte"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Dies ist veralteter Code. Normalerweise wird gtag.js verwendet."
this_page_doesnt_exist = "Diese Seite existiert nicht!"
time = "Zeit"
timezone = "Zeitzone"
title = "Titel"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Um Disqus- oder Facebook-Kommentare einzubinden, geben Sie den Disqus-Kurznamen oder die Facebook App-ID an."
token_error = "CSRF-Token nicht korrekt"
tools = "Werkzeuge"
twitter_account = "Twitter-Konto"
type_to_search = "Tippen Sie, um zu suchen"
uncategorized = "Unkategorisiert"
uncategorized_comment = "Beiträge, die in keine Kategorie passen (sollen)."
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unbekanntes Feedformat"
update = "Aktualisieren"
update_available = "Update verfügbar"
update_draft = "Entwurf aktualisieren"
update_post = "Beitrag aktualisieren"
update_to = "Aktualisieren auf"
upload = "Upload"
user = "Benutzer:in"
user_error = "Benutzer:innen-Feld ist erforderlich"
valid_values_range_from_0_to_1.0._see = "Gültige Werte nur zwischen 0.0 und 1.0. Siehe"
video_post = "Video-Beitrag"
video_post_comment = "Einen Beitrag zur Präsentation eines Videos erstellen"
view = "Ansicht"
view_post = "Ansicht"
views = "Ansichten"
widget = "Widget"
widget_key_placeholder = "12345abcde"
widget_settings = "Widget-Einstellungen"
would_you_like_to_try_our = "Besuchen Sie doch unsere "
year_month_your_post_slug = "/year/month/your-post-slug"
yes_im_in = "Ja, ich bin dabei!"
yes_not_recommended = "Ja (nicht empfohlen)"
you_dont_have_permission_to_access_this_page = "Sie haben keine Berechtigung, auf diese Seite zuzugreifen."
your_backups = "Ihre Backups"
your_key = "your.key"
your_latest_blog_posts = "Neueste Blog-Beiträge"
your_new_config_key = "Neuer Konfigurations-Schlüssel"
your_new_value = "Neuer Wert"
your_recent_posts = "Ihre letzten Beiträge"
manage_users = "Benutzer:innen-Verwaltung"
add_user = "Benutzer:in hinzufügen"
username = "Benutzer:innen-Name"
role = "Rolle"
change_password = "Passwort ändern"
config_mfa = "MFA konfigurieren"
mfacode = "MFA Code"
verify_code = "MFA code verifizieren"
verify_password = "Aktuelles Passwort verifizieren"
manualsetupkey = "Der setup key kann auch manuell hinzugefügt werden"
mfa_error = "MFA code ist nicht korrekt"
disablemfa = "MFA deaktivieren"
enable_auto_save = "Automatisches Speichern aktivieren"
explain_autosave = "Neue Inhalte oder Entwürfe werden automatisch alle 60 Sekunden gespeichert, wenn aktiviert."
login_protect_system = "Login-Schutz"
cloudflare_info = "Sehen Sie sich die Turnstile-Dokumentation von Cloudflare an: "
mfa_config = "Multi-Faktor-Authentifizierung (MFA)"
set_mfa_globally = "MFA ermöglichen"
explain_mfa = "Wenn aktiviert, ist MFA für alle Benutzer:innen optional. Wenn deaktiviert, kann MFA nicht verwendet werden und das Feld wird auf der Anmeldeseite ausgeblendet."
set_version_publicly = "Version öffentlich sichtbar"
explain_version = "Standardmäßig ist die Version von HTMLy öffentlich im Quellcode sichtbar. Einige Administrator:innen ziehen es möglicherweise vor, dies auszublenden."
focus_mode = "Fokus-Modus umschalten"
writing = "Schreiben"
writing_settings = "Schreibeinstellungen"
security = "Sicherheit"
security_settings = "Sicherheitseinstellungen"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
