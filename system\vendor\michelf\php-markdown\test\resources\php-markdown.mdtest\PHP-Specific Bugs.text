This tests for a bug where quotes escaped by PHP when using 
`preg_replace` with the `/e` modifier must be correctly unescaped
(hence the `_UnslashQuotes` function found only in PHP Markdown).



Headers below should appear exactly as they are typed (no backslash
added or removed).

Header "quoted\" again \\""
===========================

Header "quoted\" again \\""
---------------------------

### Header "quoted\" again \\"" ###



Test with tabs for `_Detab`:

	Code	'block'	with	some	"tabs"	and	"quotes"
