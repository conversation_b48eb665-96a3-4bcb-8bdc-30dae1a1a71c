version: "2"
services:
  php70:
    image: php:7.0-alpine
    command: vendor/bin/phpunit
    volumes: [".:/app"]
    working_dir: /app

  php56:
    image: php:5.6-alpine
    command: vendor/bin/phpunit
    volumes: [".:/app"]
    working_dir: /app

  php55:
    image: php:5.5-alpine
    command: vendor/bin/phpunit
    volumes: [".:/app"]
    working_dir: /app

  php54:
    image: php:5.4-cli
    command: vendor/bin/phpunit
    volumes: [".:/app"]
    working_dir: /app
