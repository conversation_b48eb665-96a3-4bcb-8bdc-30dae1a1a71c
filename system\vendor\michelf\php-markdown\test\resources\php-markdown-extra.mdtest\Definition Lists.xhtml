<p>A simple definition list:</p>

<dl>
<dt>Term 1</dt>
<dd>Definition 1</dd>

<dt>Term 2</dt>
<dd>Definition 2</dd>
</dl>

<p>With multiple terms:</p>

<dl>
<dt>Term 1</dt>
<dt>Term 2</dt>
<dd>Definition 1</dd>

<dt>Term 3</dt>
<dt>Term 4</dt>
<dd>Definition 2</dd>
</dl>

<p>With multiple definitions:</p>

<dl>
<dt>Term 1</dt>
<dd>Definition 1</dd>

<dd>Definition 2</dd>

<dt>Term 2</dt>
<dd>Definition 3</dd>

<dd>Definition 4</dd>
</dl>

<p>With multiple lines per definition:</p>

<dl>
<dt>Term 1</dt>
<dd>Definition 1 line 1 ...
Definition 1 line 2</dd>

<dd>Definition 2 line 1 ...
Definition 2 line 2</dd>

<dt>Term 2</dt>
<dd>Definition 3 line 2 ...
Definition 3 line 2</dd>

<dd>Definition 4 line 2 ...
Definition 4 line 2</dd>
</dl>

<p>With paragraphs:</p>

<dl>
<dt>Term 1</dt>
<dd>
<p>Definition 1 (paragraph)</p>
</dd>

<dt>Term 2</dt>
<dd>
<p>Definition 2 (paragraph)</p>
</dd>
</dl>

<p>With multiple paragraphs:</p>

<dl>
<dt>Term 1</dt>
<dd>
<p>Definition 1 paragraph 1 line 1 ...
Definition 1 paragraph 1 line 2</p>

<p>Definition 1 paragraph 2 line 1 ...
Definition 1 paragraph 2 line 2</p>
</dd>

<dt>Term 2</dt>
<dd>
<p>Definition 1 paragraph 1 line 1 ...
Definition 1 paragraph 1 line 2 (lazy)</p>

<p>Definition 1 paragraph 2 line 1 ...
Definition 1 paragraph 2 line 2 (lazy)</p>
</dd>
</dl>

<hr />

<p>A mix:</p>

<dl>
<dt>Term 1</dt>
<dt>Term 2</dt>
<dd>
<p>Definition 1 paragraph 1 line 1 ...
Definition 1 paragraph 1 line 2 (lazy)</p>

<p>Definition 1 paragraph 2 line 1 ...
Definition 1 paragraph 2 line 2</p>
</dd>

<dd>
<p>Definition 2 paragraph 1 line 1 ...
Definition 2 paragraph 1 line 2 (lazy)</p>
</dd>

<dt>Term 3</dt>
<dd>Definition 3 (no paragraph)</dd>

<dd>Definition 4 (no paragraph)</dd>

<dd>Definition 5 line 1 ...
Definition 5 line 2 (no paragraph)</dd>

<dd>
<p>Definition 6 paragraph 1 line 1 ...
Definition 6 paragraph 1 line 2</p>
</dd>

<dd>Definition 7 (no paragraph)</dd>

<dd>
<p>Definition 8 paragraph 1 line 1 (forced paragraph) ...
Definition 8 paragraph 1 line 2</p>

<p>Definition 8 paragraph 2 line 1</p>
</dd>

<dt>Term 4</dt>
<dd>
<p>Definition 9 paragraph 1 line 1 (forced paragraph) ...
Definition 9 paragraph 1 line 2</p>

<p>Definition 9 paragraph 2 line 1</p>
</dd>

<dd>Definition 10 (no paragraph)</dd>
</dl>

<hr />

<p>Special cases:</p>

<dl>
<dt>Term</dt>
<dd>
<pre><code>code block
as first element of a definition
</code></pre>
</dd>
</dl>
