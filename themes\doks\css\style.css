/*!
 * Bootstrap v5.0.0-beta3 (https://getbootstrap.com/)
 * Copyright 2011-2021 The Bootstrap Authors
 * Copyright 2011-2021 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */:root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #5d2f86;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffe000;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-primary: #2b388f;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffe000;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-font-sans-serif: "Jost", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, "Helvetica Neue", arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: sfmono-regular, menlo, monaco, consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255,255,255,0.15), rgba(255,255,255,0))
}
*,
*::before,
*::after {
  box-sizing:border-box
}
@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior:smooth
  }
}
body {
  margin:0;
  font-family:"Jost",-apple-system,blinkmacsystemfont,"Segoe UI",roboto,"Helvetica Neue",arial,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";
  font-size:1rem;
  font-weight:400;
  line-height:1.5;
  color:#1d2d35;
  background-color:#fff;
  -webkit-text-size-adjust:100%;
  -webkit-tap-highlight-color:rgba(29,45,53,0)
}
h4,
.h4,
h3,
.h3,
h2,
.h2,
h1,
.h1 {
  margin-top:0;
  margin-bottom:.5rem;
  font-weight:700;
  line-height:1.2
}
h1,
.h1 {
  font-size:calc(1.375rem + 1.5vw)
}
@media (min-width: 1200px) {
  h1,
  .h1 {
    font-size:2.5rem
  }
}
h2,
.h2 {
  font-size:calc(1.325rem + .9vw)
}
@media (min-width: 1200px) {
  h2,
  .h2 {
    font-size:2rem
  }
}
h3,
.h3 {
  font-size:calc(1.3rem + .6vw)
}
@media (min-width: 1200px) {
  h3,
  .h3 {
    font-size:1.75rem
  }
}
h4,
.h4 {
  font-size:calc(1.275rem + .3vw)
}
@media (min-width: 1200px) {
  h4,
  .h4 {
    font-size:1.5rem
  }
}
p {
  margin-top:0;
  margin-bottom:1rem
}
ol,
ul {
  padding-left:2rem
}
ol,
ul {
  margin-top:0;
  margin-bottom:1rem
}
ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom:0
}
blockquote {
  margin:0 0 1rem
}
strong {
  font-weight:bolder
}
small,
.small {
  font-size:.875em
}
a {
  color:#5d2f86;
  text-decoration:none
}
a:hover {
  color:#4a266b
}
a:not([href]):not([class]),
a:not([href]):not([class]):hover {
  color:inherit;
  text-decoration:none
}
pre,
code,
kbd,
samp {
  font-family:var(--bs-font-monospace);
  font-size:1em;
  direction:ltr /* rtl:ignore */;
  unicode-bidi:bidi-override
}
pre {
  display:block;
  margin-top:0;
  margin-bottom:1rem;
  overflow:auto;
  font-size:.875em
}
pre code {
  font-size:inherit;
  color:inherit;
  word-break:normal
}
code {
  font-size:.875em;
  color:#d63384;
  word-wrap:break-word
}
a>code {
  color:inherit
}
kbd {
  padding:.2rem .4rem;
  font-size:.875em;
  color:#fff;
  background-color:#212529;
  border-radius:.2rem
}
kbd kbd {
  padding:0;
  font-size:1em;
  font-weight:700
}
figure {
  margin:0 0 1rem
}
img,
svg {
  vertical-align:middle
}
table {
  caption-side:bottom;
  border-collapse:collapse
}
caption {
  padding-top:.5rem;
  padding-bottom:.5rem;
  color:#6c757d;
  text-align:left
}
th {
  text-align:inherit;
  text-align:-webkit-match-parent
}
thead,
tbody,
tr,
td,
th {
  border-color:inherit;
  border-style:solid;
  border-width:0
}
label {
  display:inline-block
}
button {
  border-radius:0
}
button:focus:not(:focus-visible) {
  outline:0
}
input,
button {
  margin:0;
  font-family:inherit;
  font-size:inherit;
  line-height:inherit
}
button {
  text-transform:none
}
[role="button"] {
  cursor:pointer
}
[list]::-webkit-calendar-picker-indicator {
  display:none
}
button,
[type="button"] {
  -webkit-appearance:button
}
button:not(:disabled),
[type="button"]:not(:disabled) {
  cursor:pointer
}
[type="search"] {
  outline-offset:-2px;
  -webkit-appearance:textfield
}
summary {
  display:list-item;
  cursor:pointer
}
.lead {
  font-size:1.25rem;
  font-weight:400
}
.list-unstyled {
  padding-left:0;
  list-style:none
}
.list-inline {
  padding-left:0;
  list-style:none
}
.list-inline-item {
  display:inline-block
}
.list-inline-item:not(:last-child) {
  margin-right:.5rem
}
.blockquote {
  margin-bottom:1rem;
  font-size:1.25rem
}
.blockquote>:last-child {
  margin-bottom:0
}
.img-fluid {
  max-width:100%;
  height:auto
}
.figure {
  display:inline-block
}
.figure-caption {
  font-size:.875em;
  color:#6c757d
}
.container,
.container-fluid {
  width:100%;
  padding-right:var(--bs-gutter-x, 24px);
  padding-left:var(--bs-gutter-x, 24px);
  margin-right:auto;
  margin-left:auto
}
@media (min-width: 576px) {
  .container {
    max-width:540px
  }
}
@media (min-width: 768px) {
  .container {
    max-width:720px
  }
}
@media (min-width: 992px) {
  .container {
    max-width:960px
  }
}
@media (min-width: 1200px) {
  .container {
    max-width:1240px
  }
}
@media (min-width: 1400px) {
  .container {
    max-width:1320px
  }
}
.row {
  --bs-gutter-x: 48px;
  --bs-gutter-y: 0;
  display:flex;
  flex-wrap:wrap;
  margin-top:calc(var(--bs-gutter-y) * -1);
  margin-right:calc(var(--bs-gutter-x) / -2);
  margin-left:calc(var(--bs-gutter-x) / -2)
}
.row>* {
  flex-shrink:0;
  width:100%;
  max-width:100%;
  padding-right:calc(var(--bs-gutter-x) / 2);
  padding-left:calc(var(--bs-gutter-x) / 2);
  margin-top:var(--bs-gutter-y)
}
@media (min-width: 768px) {
  .col-md-12 {
    flex:0 0 auto;
    width:75%
  }
}
@media (min-width: 992px) {
  .col-lg-5 {
    flex:0 0 auto;
    width:31.25%
  }
  .col-lg-8 {
    flex:0 0 auto;
    width:50%
  }
  .col-lg-9 {
    flex:0 0 auto;
    width:56.25%
  }
  .col-lg-10 {
    flex:0 0 auto;
    width:62.5%
  }
  .col-lg-11 {
    flex:0 0 auto;
    width:68.75%
  }
  .col-lg-12 {
    flex:0 0 auto;
    width:75%
  }
}
@media (min-width: 1200px) {
  .col-xl-3 {
    flex:0 0 auto;
    width:18.75%
  }
  .col-xl-4 {
    flex:0 0 auto;
    width:25%
  }
  .col-xl-8 {
    flex:0 0 auto;
    width:50%
  }
  .col-xl-9 {
    flex:0 0 auto;
    width:56.25%
  }
}
.table,
table {
  --bs-table-bg: rgba(0,0,0,0);
  --bs-table-striped-color: #1d2d35;
  --bs-table-striped-bg: rgba(29,45,53,0.05);
  --bs-table-active-color: #1d2d35;
  --bs-table-active-bg: rgba(29,45,53,0.1);
  --bs-table-hover-color: #1d2d35;
  --bs-table-hover-bg: rgba(29,45,53,0.075);
  width:100%;
  margin-bottom:1rem;
  color:#1d2d35;
  vertical-align:top;
  border-color:#e9ecef
}
.table>:not(caption)>*>*,
table>:not(caption)>*>* {
  padding:.5rem .5rem;
  background-color:var(--bs-table-bg);
  border-bottom-width:1px;
  box-shadow:inset 0 0 0 9999px var(--bs-table-accent-bg)
}
.table>tbody,
table>tbody {
  vertical-align:inherit
}
.table>thead,
table>thead {
  vertical-align:bottom
}
.table>:not(:last-child)>:last-child>*,
table>:not(:last-child)>:last-child>* {
  border-bottom-color:currentColor
}
body.dark table {
  --bs-table-bg: #212529;
  --bs-table-striped-bg: #2c3034;
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: #373b3e;
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: #323539;
  --bs-table-hover-color: #fff;
  color:#fff;
  border-color:#373b3e
}
.form-control {
  display:block;
  width:100%;
  padding:.375rem .75rem;
  font-size:1rem;
  font-weight:400;
  line-height:1.5;
  color:#1d2d35;
  background-color:#fff;
  background-clip:padding-box;
  border:1px solid #ced4da;
  -webkit-appearance:none;
  -moz-appearance:none;
  appearance:none;
  border-radius:.25rem;
  transition:border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition:none
  }
}
.form-control:focus {
  color:#1d2d35;
  background-color:#fff;
  border-color:#959cc7;
  outline:0;
  box-shadow:0 0 0 .25rem rgba(43,56,143,0.25)
}
.form-control::-webkit-date-and-time-value {
  height:1.5em
}
.form-control::-moz-placeholder {
  color:#6c757d;
  opacity:1
}
.form-control:-ms-input-placeholder {
  color:#6c757d;
  opacity:1
}
.form-control::placeholder {
  color:#6c757d;
  opacity:1
}
.form-control:disabled {
  background-color:#e9ecef;
  opacity:1
}
.form-control::file-selector-button {
  padding:.375rem .75rem;
  margin:-.375rem -.75rem;
  -webkit-margin-end:.75rem;
  margin-inline-end:.75rem;
  color:#1d2d35;
  background-color:#e9ecef;
  pointer-events:none;
  border-color:inherit;
  border-style:solid;
  border-width:0;
  border-inline-end-width:1px;
  border-radius:0;
  transition:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out
}
@media (prefers-reduced-motion: reduce) {
  .form-control::file-selector-button {
    transition:none
  }
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color:#dde0e3
}
.form-control::-webkit-file-upload-button {
  padding:.375rem .75rem;
  margin:-.375rem -.75rem;
  -webkit-margin-end:.75rem;
  margin-inline-end:.75rem;
  color:#1d2d35;
  background-color:#e9ecef;
  pointer-events:none;
  border-color:inherit;
  border-style:solid;
  border-width:0;
  border-inline-end-width:1px;
  border-radius:0;
  -webkit-transition:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out;
  transition:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition:none;
    transition:none
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color:#dde0e3
}
.btn {
  display:inline-block;
  font-weight:400;
  line-height:1.5;
  color:#1d2d35;
  text-align:center;
  vertical-align:middle;
  cursor:pointer;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none;
  background-color:transparent;
  border:1px solid transparent;
  padding:.375rem .75rem;
  font-size:1rem;
  border-radius:.25rem;
  transition:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out,box-shadow 0.15s ease-in-out
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition:none
  }
}
.btn:hover {
  color:#1d2d35
}
.btn:focus {
  outline:0;
  box-shadow:0 0 0 .25rem rgba(43,56,143,0.25)
}
.btn:disabled,
.btn.disabled {
  pointer-events:none;
  opacity:.65
}
.btn-primary {
  color:#fff;
  background-color:#5d2f86;
  border-color:#5d2f86
}
.btn-primary:hover {
  color:#fff;
  background-color:#49256a;
  border-color:#432260
}
.btn-primary:focus,
.btn-primary.focus {
  color:#fff;
  background-color:#49256a;
  border-color:#432260;
  box-shadow:0 0 0 .2rem rgba(117,78,152,0.5)
}
.btn-primary.disabled,
.btn-primary:disabled {
  color:#fff;
  background-color:#5d2f86;
  border-color:#5d2f86
}
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary.active:not(:disabled):not(.disabled) {
  color:#fff;
  background-color:#432260;
  border-color:#3c1e57
}
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary.active:not(:disabled):not(.disabled):focus {
  box-shadow:0 0 0 .2rem rgba(117,78,152,0.5)
}
.btn-outline-primary {
  color:#2b388f;
  border-color:#2b388f
}
.btn-outline-primary:hover {
  color:#fff;
  background-color:#2b388f;
  border-color:#2b388f
}
.btn-outline-primary:focus {
  box-shadow:0 0 0 .25rem rgba(43,56,143,0.5)
}
.btn-outline-primary:active,
.btn-outline-primary.active {
  color:#fff;
  background-color:#2b388f;
  border-color:#2b388f
}
.btn-outline-primary:active:focus,
.btn-outline-primary.active:focus {
  box-shadow:0 0 0 .25rem rgba(43,56,143,0.5)
}
.btn-outline-primary:disabled,
.btn-outline-primary.disabled {
  color:#2b388f;
  background-color:transparent
}
.btn-link {
  font-weight:400;
  color:#5d2f86;
  text-decoration:none
}
.btn-link:hover {
  color:#4a266b
}
.btn-link:disabled,
.btn-link.disabled {
  color:#6c757d
}
.btn-lg {
  padding:.5rem 1rem;
  font-size:1.25rem;
  border-radius:.3rem
}
.collapse:not(.show) {
  display:none
}
.nav {
  display:flex;
  flex-wrap:wrap;
  padding-left:0;
  margin-bottom:0;
  list-style:none
}
.nav-link {
  display:block;
  padding:.5rem 1rem;
  transition:color 0.15s ease-in-out,background-color 0.15s ease-in-out,border-color 0.15s ease-in-out
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition:none
  }
}
.nav-link.disabled {
  color:#6c757d;
  pointer-events:none;
  cursor:default
}
.navbar {
  position:relative;
  display:flex;
  flex-wrap:wrap;
  align-items:center;
  justify-content:space-between;
  padding-top:.5rem;
  padding-bottom:.5rem
}
.navbar>.container,
.navbar>.container-fluid {
  display:flex;
  flex-wrap:inherit;
  align-items:center;
  justify-content:space-between
}
.navbar-brand {
  padding-top:.3125rem;
  padding-bottom:.3125rem;
  margin-right:1rem;
  font-size:1.25rem;
  white-space:nowrap
}
.navbar-nav {
  display:flex;
  flex-direction:column;
  padding-left:0;
  margin-bottom:0;
  list-style:none
}
.navbar-nav .nav-link {
  padding-right:0;
  padding-left:0
}
.navbar-text {
  padding-top:.5rem;
  padding-bottom:.5rem
}
.navbar-collapse {
  flex-basis:100%;
  flex-grow:1;
  align-items:center
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap:nowrap;
    justify-content:flex-start
  }
  .navbar-expand-md .navbar-nav {
    flex-direction:row
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right:.5rem;
    padding-left:.5rem
  }
  .navbar-expand-md .navbar-collapse {
    display:flex !important;
    flex-basis:auto
  }
}
.navbar-light .navbar-brand {
  color:#5d2f86
}
.navbar-light .navbar-brand:hover,
.navbar-light .navbar-brand:focus {
  color:#5d2f86
}
.navbar-light .navbar-nav .nav-link {
  color:#1d2d35
}
.navbar-light .navbar-nav .nav-link:hover,
.navbar-light .navbar-nav .nav-link:focus {
  color:#5d2f86
}
.navbar-light .navbar-nav .nav-link.disabled {
  color:rgba(29,45,53,0.3)
}
.navbar-light .navbar-nav .show>.nav-link,
.navbar-light .navbar-nav .nav-link.active {
  color:#5d2f86
}
.navbar-light .navbar-text {
  color:#1d2d35
}
.navbar-light .navbar-text a,
.navbar-light .navbar-text a:hover,
.navbar-light .navbar-text a:focus {
  color:#5d2f86
}
.card {
  position:relative;
  display:flex;
  flex-direction:column;
  min-width:0;
  word-wrap:break-word;
  background-color:#fff;
  background-clip:border-box;
  border:1px solid #e9ecef;
  border-radius:.25rem
}
.card-body {
  flex:1 1 auto;
  padding:1rem 1rem
}
.breadcrumb {
  display:flex;
  flex-wrap:wrap;
  padding:0 0;
  margin-bottom:1rem;
  list-style:none
}
.breadcrumb-item+.breadcrumb-item {
  padding-left:.5rem
}
.breadcrumb-item+.breadcrumb-item::before {
  float:left;
  padding-right:.5rem;
  color:#6c757d;
  content:var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */
}
.breadcrumb-item.active {
  color:#6c757d
}
.pagination {
  display:flex;
  padding-left:0;
  list-style:none
}
.alert {
  position:relative;
  padding:1rem 1.5rem;
  margin-bottom:0;
  border:0 solid transparent;
  border-radius:0
}
.alert-primary {
  color:#fff;
  background-color:#2b388f;
  border-color:#2b388f
}
.alert-warning {
  color:#1d2d35;
  background-color:#ffe000;
  border-color:#ffe000
}
@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x:1rem
  }
}
@keyframes progress-bar-stripes {
  0% {
    background-position-x:1rem
  }
}
@-webkit-keyframes spinner-border {
  to {
    transform:rotate(360deg) /* rtl:ignore */
  }
}
@keyframes spinner-border {
  to {
    transform:rotate(360deg) /* rtl:ignore */
  }
}
@-webkit-keyframes spinner-grow {
  0% {
    transform:scale(0)
  }
  50% {
    opacity:1;
    transform:none
  }
}
@keyframes spinner-grow {
  0% {
    transform:scale(0)
  }
  50% {
    opacity:1;
    transform:none
  }
}
.fixed-top {
  position:fixed;
  top:0;
  right:0;
  left:0;
  z-index:1030
}
.fixed-bottom {
  position:fixed;
  right:0;
  bottom:0;
  left:0;
  z-index:1030
}
.visually-hidden {
  position:absolute !important;
  width:1px !important;
  height:1px !important;
  padding:0 !important;
  margin:-1px !important;
  overflow:hidden !important;
  clip:rect(0, 0, 0, 0) !important;
  white-space:nowrap !important;
  border:0 !important
}
.stretched-link::after {
  position:absolute;
  top:0;
  right:0;
  bottom:0;
  left:0;
  z-index:1;
  content:""
}
.d-flex {
  display:flex !important
}
.d-none {
  display:none !important
}
.shadow {
  box-shadow:0 0.5rem 1rem rgba(29,45,53,0.15) !important
}
.position-relative {
  position:relative !important
}
.border-top {
  border-top:1px solid #e9ecef !important
}
.w-100 {
  width:100% !important
}
.flex-grow-1 {
  flex-grow:1 !important
}
.flex-shrink-1 {
  flex-shrink:1 !important
}
.justify-content-center {
  justify-content:center !important
}
.justify-content-between {
  justify-content:space-between !important
}
.order-first {
  order:-1 !important
}
.order-0 {
  order:0 !important
}
.order-1 {
  order:1 !important
}
.order-2 {
  order:2 !important
}
.order-3 {
  order:3 !important
}
.order-4 {
  order:4 !important
}
.order-5 {
  order:5 !important
}
.order-last {
  order:6 !important
}
.my-1 {
  margin-top:.25rem !important;
  margin-bottom:.25rem !important
}
.my-3 {
  margin-top:1rem !important;
  margin-bottom:1rem !important
}
.mt-0 {
  margin-top:0 !important
}
.me-auto {
  margin-right:auto !important
}
.mb-2 {
  margin-bottom:.5rem !important
}
.ms-2 {
  margin-left:.5rem !important
}
.ms-auto {
  margin-left:auto !important
}
.mt-n3 {
  margin-top:-1rem !important
}
.px-4 {
  padding-right:1.5rem !important;
  padding-left:1.5rem !important
}
.py-2 {
  padding-top:.5rem !important;
  padding-bottom:.5rem !important
}
.pb-3 {
  padding-bottom:1rem !important
}
.text-center {
  text-align:center !important
}
.text-body {
  color:#1d2d35 !important
}
.text-muted {
  color:#6c757d !important
}
.bg-light {
  background-color:#f8f9fa !important
}
.bg-white {
  background-color:#fff !important
}
.rounded {
  border-radius:.25rem !important
}
@media (min-width: 768px) {
  .d-md-none {
    display:none !important
  }
  .order-md-0 {
    order:0 !important
  }
  .order-md-1 {
    order:1 !important
  }
  .order-md-2 {
    order:2 !important
  }
  .order-md-3 {
    order:3 !important
  }
  .order-md-4 {
    order:4 !important
  }
  .order-md-5 {
    order:5 !important
  }
}
@media (min-width: 992px) {
  .order-lg-first {
    order:-1 !important
  }
  .order-lg-last {
    order:6 !important
  }
  .text-lg-end {
    text-align:right !important
  }
}
@media (min-width: 1200px) {
  .d-xl-block {
    display:block !important
  }
  .flex-xl-nowrap {
    flex-wrap:nowrap !important
  }
  .mx-xl-auto {
    margin-right:auto !important;
    margin-left:auto !important
  }
}
@font-face {
  font-family:'KaTeX_AMS';
  src:url(../fonts/KaTeX_AMS-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_AMS-Regular.woff) format("woff"),
  url(../fonts/KaTeX_AMS-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Caligraphic';
  src:url(../fonts/KaTeX_Caligraphic-Bold.woff2) format("woff2"),
  url(../fonts/KaTeX_Caligraphic-Bold.woff) format("woff"),
  url(../fonts/KaTeX_Caligraphic-Bold.ttf) format("truetype");
  font-weight:bold;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Caligraphic';
  src:url(../fonts/KaTeX_Caligraphic-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Caligraphic-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Caligraphic-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Fraktur';
  src:url(../fonts/KaTeX_Fraktur-Bold.woff2) format("woff2"),
  url(../fonts/KaTeX_Fraktur-Bold.woff) format("woff"),
  url(../fonts/KaTeX_Fraktur-Bold.ttf) format("truetype");
  font-weight:bold;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Fraktur';
  src:url(../fonts/KaTeX_Fraktur-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Fraktur-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Fraktur-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Main';
  src:url(../fonts/KaTeX_Main-Bold.woff2) format("woff2"),
  url(../fonts/KaTeX_Main-Bold.woff) format("woff"),
  url(../fonts/KaTeX_Main-Bold.ttf) format("truetype");
  font-weight:bold;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Main';
  src:url(../fonts/KaTeX_Main-BoldItalic.woff2) format("woff2"),
  url(../fonts/KaTeX_Main-BoldItalic.woff) format("woff"),
  url(../fonts/KaTeX_Main-BoldItalic.ttf) format("truetype");
  font-weight:bold;
  font-style:italic
}
@font-face {
  font-family:'KaTeX_Main';
  src:url(../fonts/KaTeX_Main-Italic.woff2) format("woff2"),
  url(../fonts/KaTeX_Main-Italic.woff) format("woff"),
  url(../fonts/KaTeX_Main-Italic.ttf) format("truetype");
  font-weight:normal;
  font-style:italic
}
@font-face {
  font-family:'KaTeX_Main';
  src:url(../fonts/KaTeX_Main-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Main-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Main-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Math';
  src:url(../fonts/KaTeX_Math-BoldItalic.woff2) format("woff2"),
  url(../fonts/KaTeX_Math-BoldItalic.woff) format("woff"),
  url(../fonts/KaTeX_Math-BoldItalic.ttf) format("truetype");
  font-weight:bold;
  font-style:italic
}
@font-face {
  font-family:'KaTeX_Math';
  src:url(../fonts/KaTeX_Math-Italic.woff2) format("woff2"),
  url(../fonts/KaTeX_Math-Italic.woff) format("woff"),
  url(../fonts/KaTeX_Math-Italic.ttf) format("truetype");
  font-weight:normal;
  font-style:italic
}
@font-face {
  font-family:'KaTeX_SansSerif';
  src:url(../fonts/KaTeX_SansSerif-Bold.woff2) format("woff2"),
  url(../fonts/KaTeX_SansSerif-Bold.woff) format("woff"),
  url(../fonts/KaTeX_SansSerif-Bold.ttf) format("truetype");
  font-weight:bold;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_SansSerif';
  src:url(../fonts/KaTeX_SansSerif-Italic.woff2) format("woff2"),
  url(../fonts/KaTeX_SansSerif-Italic.woff) format("woff"),
  url(../fonts/KaTeX_SansSerif-Italic.ttf) format("truetype");
  font-weight:normal;
  font-style:italic
}
@font-face {
  font-family:'KaTeX_SansSerif';
  src:url(../fonts/KaTeX_SansSerif-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_SansSerif-Regular.woff) format("woff"),
  url(../fonts/KaTeX_SansSerif-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Script';
  src:url(../fonts/KaTeX_Script-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Script-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Script-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Size1';
  src:url(../fonts/KaTeX_Size1-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Size1-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Size1-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Size2';
  src:url(../fonts/KaTeX_Size2-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Size2-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Size2-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Size3';
  src:url(../fonts/KaTeX_Size3-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Size3-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Size3-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Size4';
  src:url(../fonts/KaTeX_Size4-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Size4-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Size4-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
@font-face {
  font-family:'KaTeX_Typewriter';
  src:url(../fonts/KaTeX_Typewriter-Regular.woff2) format("woff2"),
  url(../fonts/KaTeX_Typewriter-Regular.woff) format("woff"),
  url(../fonts/KaTeX_Typewriter-Regular.ttf) format("truetype");
  font-weight:normal;
  font-style:normal
}
.katex {
  font:normal 1.21em KaTeX_Main, Times New Roman, serif;
  line-height:1.2;
  text-indent:0;
  text-rendering:auto
}
.katex * {
  -ms-high-contrast-adjust:none !important;
  border-color:currentColor
}
.katex .katex-version::after {
  content:"0.13.2"
}
.katex .katex-mathml {
  position:absolute;
  clip:rect(1px, 1px, 1px, 1px);
  padding:0;
  border:0;
  height:1px;
  width:1px;
  overflow:hidden
}
.katex .katex-html>.newline {
  display:block
}
.katex .base {
  position:relative;
  display:inline-block;
  white-space:nowrap;
  width:-webkit-min-content;
  width:-moz-min-content;
  width:min-content
}
.katex .strut {
  display:inline-block
}
.katex .textbf {
  font-weight:bold
}
.katex .textit {
  font-style:italic
}
.katex .textrm {
  font-family:KaTeX_Main
}
.katex .textsf {
  font-family:KaTeX_SansSerif
}
.katex .texttt {
  font-family:KaTeX_Typewriter
}
.katex .mathnormal {
  font-family:KaTeX_Math;
  font-style:italic
}
.katex .mathit {
  font-family:KaTeX_Main;
  font-style:italic
}
.katex .mathrm {
  font-style:normal
}
.katex .mathbf {
  font-family:KaTeX_Main;
  font-weight:bold
}
.katex .boldsymbol {
  font-family:KaTeX_Math;
  font-weight:bold;
  font-style:italic
}
.katex .amsrm {
  font-family:KaTeX_AMS
}
.katex .mathbb,
.katex .textbb {
  font-family:KaTeX_AMS
}
.katex .mathcal {
  font-family:KaTeX_Caligraphic
}
.katex .mathfrak,
.katex .textfrak {
  font-family:KaTeX_Fraktur
}
.katex .mathtt {
  font-family:KaTeX_Typewriter
}
.katex .mathscr,
.katex .textscr {
  font-family:KaTeX_Script
}
.katex .mathsf,
.katex .textsf {
  font-family:KaTeX_SansSerif
}
.katex .mathboldsf,
.katex .textboldsf {
  font-family:KaTeX_SansSerif;
  font-weight:bold
}
.katex .mathitsf,
.katex .textitsf {
  font-family:KaTeX_SansSerif;
  font-style:italic
}
.katex .mainrm {
  font-family:KaTeX_Main;
  font-style:normal
}
.katex .vlist-t {
  display:inline-table;
  table-layout:fixed;
  border-collapse:collapse
}
.katex .vlist-r {
  display:table-row
}
.katex .vlist {
  display:table-cell;
  vertical-align:bottom;
  position:relative
}
.katex .vlist>span {
  display:block;
  height:0;
  position:relative
}
.katex .vlist>span>span {
  display:inline-block
}
.katex .vlist>span>.pstrut {
  overflow:hidden;
  width:0
}
.katex .vlist-t2 {
  margin-right:-2px
}
.katex .vlist-s {
  display:table-cell;
  vertical-align:bottom;
  font-size:1px;
  width:2px;
  min-width:2px
}
.katex .vbox {
  display:inline-flex;
  flex-direction:column;
  align-items:baseline
}
.katex .hbox {
  display:inline-flex;
  flex-direction:row;
  width:100%
}
.katex .thinbox {
  display:inline-flex;
  flex-direction:row;
  width:0;
  max-width:0
}
.katex .msupsub {
  text-align:left
}
.katex .mfrac>span>span {
  text-align:center
}
.katex .mfrac .frac-line {
  display:inline-block;
  width:100%;
  border-bottom-style:solid
}
.katex .mfrac .frac-line,
.katex .overline .overline-line,
.katex .underline .underline-line,
.katex .hline,
.katex .hdashline,
.katex .rule {
  min-height:1px
}
.katex .mspace {
  display:inline-block
}
.katex .llap,
.katex .rlap,
.katex .clap {
  width:0;
  position:relative
}
.katex .llap>.inner,
.katex .rlap>.inner,
.katex .clap>.inner {
  position:absolute
}
.katex .llap>.fix,
.katex .rlap>.fix,
.katex .clap>.fix {
  display:inline-block
}
.katex .llap>.inner {
  right:0
}
.katex .rlap>.inner,
.katex .clap>.inner {
  left:0
}
.katex .clap>.inner>span {
  margin-left:-50%;
  margin-right:50%
}
.katex .rule {
  display:inline-block;
  border:solid 0;
  position:relative
}
.katex .overline .overline-line,
.katex .underline .underline-line,
.katex .hline {
  display:inline-block;
  width:100%;
  border-bottom-style:solid
}
.katex .hdashline {
  display:inline-block;
  width:100%;
  border-bottom-style:dashed
}
.katex .sqrt>.root {
  margin-left:0.27777778em;
  margin-right:-0.55555556em
}
.katex .sizing.reset-size1.size1,
.katex .fontsize-ensurer.reset-size1.size1 {
  font-size:1em
}
.katex .sizing.reset-size1.size2,
.katex .fontsize-ensurer.reset-size1.size2 {
  font-size:1.2em
}
.katex .sizing.reset-size1.size3,
.katex .fontsize-ensurer.reset-size1.size3 {
  font-size:1.4em
}
.katex .sizing.reset-size1.size4,
.katex .fontsize-ensurer.reset-size1.size4 {
  font-size:1.6em
}
.katex .sizing.reset-size1.size5,
.katex .fontsize-ensurer.reset-size1.size5 {
  font-size:1.8em
}
.katex .sizing.reset-size1.size6,
.katex .fontsize-ensurer.reset-size1.size6 {
  font-size:2em
}
.katex .sizing.reset-size1.size7,
.katex .fontsize-ensurer.reset-size1.size7 {
  font-size:2.4em
}
.katex .sizing.reset-size1.size8,
.katex .fontsize-ensurer.reset-size1.size8 {
  font-size:2.88em
}
.katex .sizing.reset-size1.size9,
.katex .fontsize-ensurer.reset-size1.size9 {
  font-size:3.456em
}
.katex .sizing.reset-size1.size10,
.katex .fontsize-ensurer.reset-size1.size10 {
  font-size:4.148em
}
.katex .sizing.reset-size1.size11,
.katex .fontsize-ensurer.reset-size1.size11 {
  font-size:4.976em
}
.katex .sizing.reset-size2.size1,
.katex .fontsize-ensurer.reset-size2.size1 {
  font-size:0.83333333em
}
.katex .sizing.reset-size2.size2,
.katex .fontsize-ensurer.reset-size2.size2 {
  font-size:1em
}
.katex .sizing.reset-size2.size3,
.katex .fontsize-ensurer.reset-size2.size3 {
  font-size:1.16666667em
}
.katex .sizing.reset-size2.size4,
.katex .fontsize-ensurer.reset-size2.size4 {
  font-size:1.33333333em
}
.katex .sizing.reset-size2.size5,
.katex .fontsize-ensurer.reset-size2.size5 {
  font-size:1.5em
}
.katex .sizing.reset-size2.size6,
.katex .fontsize-ensurer.reset-size2.size6 {
  font-size:1.66666667em
}
.katex .sizing.reset-size2.size7,
.katex .fontsize-ensurer.reset-size2.size7 {
  font-size:2em
}
.katex .sizing.reset-size2.size8,
.katex .fontsize-ensurer.reset-size2.size8 {
  font-size:2.4em
}
.katex .sizing.reset-size2.size9,
.katex .fontsize-ensurer.reset-size2.size9 {
  font-size:2.88em
}
.katex .sizing.reset-size2.size10,
.katex .fontsize-ensurer.reset-size2.size10 {
  font-size:3.45666667em
}
.katex .sizing.reset-size2.size11,
.katex .fontsize-ensurer.reset-size2.size11 {
  font-size:4.14666667em
}
.katex .sizing.reset-size3.size1,
.katex .fontsize-ensurer.reset-size3.size1 {
  font-size:0.71428571em
}
.katex .sizing.reset-size3.size2,
.katex .fontsize-ensurer.reset-size3.size2 {
  font-size:0.85714286em
}
.katex .sizing.reset-size3.size3,
.katex .fontsize-ensurer.reset-size3.size3 {
  font-size:1em
}
.katex .sizing.reset-size3.size4,
.katex .fontsize-ensurer.reset-size3.size4 {
  font-size:1.14285714em
}
.katex .sizing.reset-size3.size5,
.katex .fontsize-ensurer.reset-size3.size5 {
  font-size:1.28571429em
}
.katex .sizing.reset-size3.size6,
.katex .fontsize-ensurer.reset-size3.size6 {
  font-size:1.42857143em
}
.katex .sizing.reset-size3.size7,
.katex .fontsize-ensurer.reset-size3.size7 {
  font-size:1.71428571em
}
.katex .sizing.reset-size3.size8,
.katex .fontsize-ensurer.reset-size3.size8 {
  font-size:2.05714286em
}
.katex .sizing.reset-size3.size9,
.katex .fontsize-ensurer.reset-size3.size9 {
  font-size:2.46857143em
}
.katex .sizing.reset-size3.size10,
.katex .fontsize-ensurer.reset-size3.size10 {
  font-size:2.96285714em
}
.katex .sizing.reset-size3.size11,
.katex .fontsize-ensurer.reset-size3.size11 {
  font-size:3.55428571em
}
.katex .sizing.reset-size4.size1,
.katex .fontsize-ensurer.reset-size4.size1 {
  font-size:0.625em
}
.katex .sizing.reset-size4.size2,
.katex .fontsize-ensurer.reset-size4.size2 {
  font-size:0.75em
}
.katex .sizing.reset-size4.size3,
.katex .fontsize-ensurer.reset-size4.size3 {
  font-size:0.875em
}
.katex .sizing.reset-size4.size4,
.katex .fontsize-ensurer.reset-size4.size4 {
  font-size:1em
}
.katex .sizing.reset-size4.size5,
.katex .fontsize-ensurer.reset-size4.size5 {
  font-size:1.125em
}
.katex .sizing.reset-size4.size6,
.katex .fontsize-ensurer.reset-size4.size6 {
  font-size:1.25em
}
.katex .sizing.reset-size4.size7,
.katex .fontsize-ensurer.reset-size4.size7 {
  font-size:1.5em
}
.katex .sizing.reset-size4.size8,
.katex .fontsize-ensurer.reset-size4.size8 {
  font-size:1.8em
}
.katex .sizing.reset-size4.size9,
.katex .fontsize-ensurer.reset-size4.size9 {
  font-size:2.16em
}
.katex .sizing.reset-size4.size10,
.katex .fontsize-ensurer.reset-size4.size10 {
  font-size:2.5925em
}
.katex .sizing.reset-size4.size11,
.katex .fontsize-ensurer.reset-size4.size11 {
  font-size:3.11em
}
.katex .sizing.reset-size5.size1,
.katex .fontsize-ensurer.reset-size5.size1 {
  font-size:0.55555556em
}
.katex .sizing.reset-size5.size2,
.katex .fontsize-ensurer.reset-size5.size2 {
  font-size:0.66666667em
}
.katex .sizing.reset-size5.size3,
.katex .fontsize-ensurer.reset-size5.size3 {
  font-size:0.77777778em
}
.katex .sizing.reset-size5.size4,
.katex .fontsize-ensurer.reset-size5.size4 {
  font-size:0.88888889em
}
.katex .sizing.reset-size5.size5,
.katex .fontsize-ensurer.reset-size5.size5 {
  font-size:1em
}
.katex .sizing.reset-size5.size6,
.katex .fontsize-ensurer.reset-size5.size6 {
  font-size:1.11111111em
}
.katex .sizing.reset-size5.size7,
.katex .fontsize-ensurer.reset-size5.size7 {
  font-size:1.33333333em
}
.katex .sizing.reset-size5.size8,
.katex .fontsize-ensurer.reset-size5.size8 {
  font-size:1.6em
}
.katex .sizing.reset-size5.size9,
.katex .fontsize-ensurer.reset-size5.size9 {
  font-size:1.92em
}
.katex .sizing.reset-size5.size10,
.katex .fontsize-ensurer.reset-size5.size10 {
  font-size:2.30444444em
}
.katex .sizing.reset-size5.size11,
.katex .fontsize-ensurer.reset-size5.size11 {
  font-size:2.76444444em
}
.katex .sizing.reset-size6.size1,
.katex .fontsize-ensurer.reset-size6.size1 {
  font-size:0.5em
}
.katex .sizing.reset-size6.size2,
.katex .fontsize-ensurer.reset-size6.size2 {
  font-size:0.6em
}
.katex .sizing.reset-size6.size3,
.katex .fontsize-ensurer.reset-size6.size3 {
  font-size:0.7em
}
.katex .sizing.reset-size6.size4,
.katex .fontsize-ensurer.reset-size6.size4 {
  font-size:0.8em
}
.katex .sizing.reset-size6.size5,
.katex .fontsize-ensurer.reset-size6.size5 {
  font-size:0.9em
}
.katex .sizing.reset-size6.size6,
.katex .fontsize-ensurer.reset-size6.size6 {
  font-size:1em
}
.katex .sizing.reset-size6.size7,
.katex .fontsize-ensurer.reset-size6.size7 {
  font-size:1.2em
}
.katex .sizing.reset-size6.size8,
.katex .fontsize-ensurer.reset-size6.size8 {
  font-size:1.44em
}
.katex .sizing.reset-size6.size9,
.katex .fontsize-ensurer.reset-size6.size9 {
  font-size:1.728em
}
.katex .sizing.reset-size6.size10,
.katex .fontsize-ensurer.reset-size6.size10 {
  font-size:2.074em
}
.katex .sizing.reset-size6.size11,
.katex .fontsize-ensurer.reset-size6.size11 {
  font-size:2.488em
}
.katex .sizing.reset-size7.size1,
.katex .fontsize-ensurer.reset-size7.size1 {
  font-size:0.41666667em
}
.katex .sizing.reset-size7.size2,
.katex .fontsize-ensurer.reset-size7.size2 {
  font-size:0.5em
}
.katex .sizing.reset-size7.size3,
.katex .fontsize-ensurer.reset-size7.size3 {
  font-size:0.58333333em
}
.katex .sizing.reset-size7.size4,
.katex .fontsize-ensurer.reset-size7.size4 {
  font-size:0.66666667em
}
.katex .sizing.reset-size7.size5,
.katex .fontsize-ensurer.reset-size7.size5 {
  font-size:0.75em
}
.katex .sizing.reset-size7.size6,
.katex .fontsize-ensurer.reset-size7.size6 {
  font-size:0.83333333em
}
.katex .sizing.reset-size7.size7,
.katex .fontsize-ensurer.reset-size7.size7 {
  font-size:1em
}
.katex .sizing.reset-size7.size8,
.katex .fontsize-ensurer.reset-size7.size8 {
  font-size:1.2em
}
.katex .sizing.reset-size7.size9,
.katex .fontsize-ensurer.reset-size7.size9 {
  font-size:1.44em
}
.katex .sizing.reset-size7.size10,
.katex .fontsize-ensurer.reset-size7.size10 {
  font-size:1.72833333em
}
.katex .sizing.reset-size7.size11,
.katex .fontsize-ensurer.reset-size7.size11 {
  font-size:2.07333333em
}
.katex .sizing.reset-size8.size1,
.katex .fontsize-ensurer.reset-size8.size1 {
  font-size:0.34722222em
}
.katex .sizing.reset-size8.size2,
.katex .fontsize-ensurer.reset-size8.size2 {
  font-size:0.41666667em
}
.katex .sizing.reset-size8.size3,
.katex .fontsize-ensurer.reset-size8.size3 {
  font-size:0.48611111em
}
.katex .sizing.reset-size8.size4,
.katex .fontsize-ensurer.reset-size8.size4 {
  font-size:0.55555556em
}
.katex .sizing.reset-size8.size5,
.katex .fontsize-ensurer.reset-size8.size5 {
  font-size:0.625em
}
.katex .sizing.reset-size8.size6,
.katex .fontsize-ensurer.reset-size8.size6 {
  font-size:0.69444444em
}
.katex .sizing.reset-size8.size7,
.katex .fontsize-ensurer.reset-size8.size7 {
  font-size:0.83333333em
}
.katex .sizing.reset-size8.size8,
.katex .fontsize-ensurer.reset-size8.size8 {
  font-size:1em
}
.katex .sizing.reset-size8.size9,
.katex .fontsize-ensurer.reset-size8.size9 {
  font-size:1.2em
}
.katex .sizing.reset-size8.size10,
.katex .fontsize-ensurer.reset-size8.size10 {
  font-size:1.44027778em
}
.katex .sizing.reset-size8.size11,
.katex .fontsize-ensurer.reset-size8.size11 {
  font-size:1.72777778em
}
.katex .sizing.reset-size9.size1,
.katex .fontsize-ensurer.reset-size9.size1 {
  font-size:0.28935185em
}
.katex .sizing.reset-size9.size2,
.katex .fontsize-ensurer.reset-size9.size2 {
  font-size:0.34722222em
}
.katex .sizing.reset-size9.size3,
.katex .fontsize-ensurer.reset-size9.size3 {
  font-size:0.40509259em
}
.katex .sizing.reset-size9.size4,
.katex .fontsize-ensurer.reset-size9.size4 {
  font-size:0.46296296em
}
.katex .sizing.reset-size9.size5,
.katex .fontsize-ensurer.reset-size9.size5 {
  font-size:0.52083333em
}
.katex .sizing.reset-size9.size6,
.katex .fontsize-ensurer.reset-size9.size6 {
  font-size:0.5787037em
}
.katex .sizing.reset-size9.size7,
.katex .fontsize-ensurer.reset-size9.size7 {
  font-size:0.69444444em
}
.katex .sizing.reset-size9.size8,
.katex .fontsize-ensurer.reset-size9.size8 {
  font-size:0.83333333em
}
.katex .sizing.reset-size9.size9,
.katex .fontsize-ensurer.reset-size9.size9 {
  font-size:1em
}
.katex .sizing.reset-size9.size10,
.katex .fontsize-ensurer.reset-size9.size10 {
  font-size:1.20023148em
}
.katex .sizing.reset-size9.size11,
.katex .fontsize-ensurer.reset-size9.size11 {
  font-size:1.43981481em
}
.katex .sizing.reset-size10.size1,
.katex .fontsize-ensurer.reset-size10.size1 {
  font-size:0.24108004em
}
.katex .sizing.reset-size10.size2,
.katex .fontsize-ensurer.reset-size10.size2 {
  font-size:0.28929605em
}
.katex .sizing.reset-size10.size3,
.katex .fontsize-ensurer.reset-size10.size3 {
  font-size:0.33751205em
}
.katex .sizing.reset-size10.size4,
.katex .fontsize-ensurer.reset-size10.size4 {
  font-size:0.38572806em
}
.katex .sizing.reset-size10.size5,
.katex .fontsize-ensurer.reset-size10.size5 {
  font-size:0.43394407em
}
.katex .sizing.reset-size10.size6,
.katex .fontsize-ensurer.reset-size10.size6 {
  font-size:0.48216008em
}
.katex .sizing.reset-size10.size7,
.katex .fontsize-ensurer.reset-size10.size7 {
  font-size:0.57859209em
}
.katex .sizing.reset-size10.size8,
.katex .fontsize-ensurer.reset-size10.size8 {
  font-size:0.69431051em
}
.katex .sizing.reset-size10.size9,
.katex .fontsize-ensurer.reset-size10.size9 {
  font-size:0.83317261em
}
.katex .sizing.reset-size10.size10,
.katex .fontsize-ensurer.reset-size10.size10 {
  font-size:1em
}
.katex .sizing.reset-size10.size11,
.katex .fontsize-ensurer.reset-size10.size11 {
  font-size:1.19961427em
}
.katex .sizing.reset-size11.size1,
.katex .fontsize-ensurer.reset-size11.size1 {
  font-size:0.20096463em
}
.katex .sizing.reset-size11.size2,
.katex .fontsize-ensurer.reset-size11.size2 {
  font-size:0.24115756em
}
.katex .sizing.reset-size11.size3,
.katex .fontsize-ensurer.reset-size11.size3 {
  font-size:0.28135048em
}
.katex .sizing.reset-size11.size4,
.katex .fontsize-ensurer.reset-size11.size4 {
  font-size:0.32154341em
}
.katex .sizing.reset-size11.size5,
.katex .fontsize-ensurer.reset-size11.size5 {
  font-size:0.36173633em
}
.katex .sizing.reset-size11.size6,
.katex .fontsize-ensurer.reset-size11.size6 {
  font-size:0.40192926em
}
.katex .sizing.reset-size11.size7,
.katex .fontsize-ensurer.reset-size11.size7 {
  font-size:0.48231511em
}
.katex .sizing.reset-size11.size8,
.katex .fontsize-ensurer.reset-size11.size8 {
  font-size:0.57877814em
}
.katex .sizing.reset-size11.size9,
.katex .fontsize-ensurer.reset-size11.size9 {
  font-size:0.69453376em
}
.katex .sizing.reset-size11.size10,
.katex .fontsize-ensurer.reset-size11.size10 {
  font-size:0.83360129em
}
.katex .sizing.reset-size11.size11,
.katex .fontsize-ensurer.reset-size11.size11 {
  font-size:1em
}
.katex .delimsizing.size1 {
  font-family:KaTeX_Size1
}
.katex .delimsizing.size2 {
  font-family:KaTeX_Size2
}
.katex .delimsizing.size3 {
  font-family:KaTeX_Size3
}
.katex .delimsizing.size4 {
  font-family:KaTeX_Size4
}
.katex .delimsizing.mult .delim-size1>span {
  font-family:KaTeX_Size1
}
.katex .delimsizing.mult .delim-size4>span {
  font-family:KaTeX_Size4
}
.katex .nulldelimiter {
  display:inline-block;
  width:0.12em
}
.katex .delimcenter {
  position:relative
}
.katex .op-symbol {
  position:relative
}
.katex .op-symbol.small-op {
  font-family:KaTeX_Size1
}
.katex .op-symbol.large-op {
  font-family:KaTeX_Size2
}
.katex .op-limits>.vlist-t {
  text-align:center
}
.katex .accent>.vlist-t {
  text-align:center
}
.katex .accent .accent-body {
  position:relative
}
.katex .accent .accent-body:not(.accent-full) {
  width:0
}
.katex .overlay {
  display:block
}
.katex .mtable .vertical-separator {
  display:inline-block;
  min-width:1px
}
.katex .mtable .arraycolsep {
  display:inline-block
}
.katex .mtable .col-align-c>.vlist-t {
  text-align:center
}
.katex .mtable .col-align-l>.vlist-t {
  text-align:left
}
.katex .mtable .col-align-r>.vlist-t {
  text-align:right
}
.katex .svg-align {
  text-align:left
}
.katex svg {
  display:block;
  position:absolute;
  width:100%;
  height:inherit;
  fill:currentColor;
  stroke:currentColor;
  fill-rule:nonzero;
  fill-opacity:1;
  stroke-width:1;
  stroke-linecap:butt;
  stroke-linejoin:miter;
  stroke-miterlimit:4;
  stroke-dasharray:none;
  stroke-dashoffset:0;
  stroke-opacity:1
}
.katex svg path {
  stroke:none
}
.katex img {
  border-style:none;
  min-width:0;
  min-height:0;
  max-width:none;
  max-height:none
}
.katex .stretchy {
  width:100%;
  display:block;
  position:relative;
  overflow:hidden
}
.katex .stretchy::before,
.katex .stretchy::after {
  content:""
}
.katex .hide-tail {
  width:100%;
  position:relative;
  overflow:hidden
}
.katex .halfarrow-left {
  position:absolute;
  left:0;
  width:50.2%;
  overflow:hidden
}
.katex .halfarrow-right {
  position:absolute;
  right:0;
  width:50.2%;
  overflow:hidden
}
.katex .brace-left {
  position:absolute;
  left:0;
  width:25.1%;
  overflow:hidden
}
.katex .brace-center {
  position:absolute;
  left:25%;
  width:50%;
  overflow:hidden
}
.katex .brace-right {
  position:absolute;
  right:0;
  width:25.1%;
  overflow:hidden
}
.katex .x-arrow-pad {
  padding:0 0.5em
}
.katex .cd-arrow-pad {
  padding:0 0.55556em 0 0.27778em
}
.katex .x-arrow,
.katex .mover,
.katex .munder {
  text-align:center
}
.katex .boxpad {
  padding:0 0.3em 0 0.3em
}
.katex .fbox,
.katex .fcolorbox {
  box-sizing:border-box;
  border:0.04em solid
}
.katex .cancel-pad {
  padding:0 0.2em 0 0.2em
}
.katex .cancel-lap {
  margin-left:-0.2em;
  margin-right:-0.2em
}
.katex .sout {
  border-bottom-style:solid;
  border-bottom-width:0.08em
}
.katex .angl {
  box-sizing:border-content;
  border-top:0.049em solid;
  border-right:0.049em solid;
  margin-right:0.03889em
}
.katex .anglpad {
  padding:0 0.03889em 0 0.03889em
}
.katex .eqn-num::before {
  counter-increment:katexEqnNo;
  content:"(" counter(katexEqnNo) ")"
}
.katex .mml-eqn-num::before {
  counter-increment:mmlEqnNo;
  content:"(" counter(mmlEqnNo) ")"
}
.katex .mtr-glue {
  width:50%
}
.katex .cd-vert-arrow {
  display:inline-block;
  position:relative
}
.katex .cd-label-left {
  display:inline-block;
  position:absolute;
  right:calc(50% + 0.3em);
  text-align:left
}
.katex .cd-label-right {
  display:inline-block;
  position:absolute;
  left:calc(50% + 0.3em);
  text-align:right
}
.katex-display {
  display:block;
  margin:1em 0;
  text-align:center
}
.katex-display>.katex {
  display:block;
  text-align:center;
  white-space:nowrap
}
.katex-display>.katex>.katex-html {
  display:block;
  position:relative
}
.katex-display>.katex>.katex-html>.tag {
  position:absolute;
  right:0
}
.katex-display.leqno>.katex>.katex-html>.tag {
  left:0;
  right:auto
}
.katex-display.fleqn>.katex {
  text-align:left;
  padding-left:2em
}
body {
  counter-reset:katexEqnNo mmlEqnNo
}
@font-face {
  font-family:"Jost";
  font-style:normal;
  font-weight:400;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-regular.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-regular.woff") format("woff")
}
@font-face {
  font-family:"Jost";
  font-style:normal;
  font-weight:500;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-500.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-500.woff") format("woff")
}
@font-face {
  font-family:"Jost";
  font-style:normal;
  font-weight:700;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-700.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-700.woff") format("woff")
}
@font-face {
  font-family:"Jost";
  font-style:italic;
  font-weight:400;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-italic.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-italic.woff") format("woff")
}
@font-face {
  font-family:"Jost";
  font-style:italic;
  font-weight:500;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-500italic.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-500italic.woff") format("woff")
}
@font-face {
  font-family:"Jost";
  font-style:italic;
  font-weight:700;
  font-display:swap;
  src:local("Jost"),
  url("../fonts/jost/jost-v4-latin-700italic.woff2") format("woff2"),
  url("../fonts/jost/jost-v4-latin-700italic.woff") format("woff")
}
.contributors .content,
.blog .content,
.page .content,
.error404 .content,
.docs.list .content {
  padding-top:1rem;
  padding-bottom:3rem
}
h1,
h2,
h3,
h4,
.h1,
.h2,
.h3,
.h4 {
  margin:2rem 0 1rem
}
@media (min-width: 768px) {
  body {
    font-size:1.125rem;
    padding-top:4rem !important
  }
  h1,
  h2,
  h3,
  h4,
  .h1,
  .h2,
  .h3,
  .h4 {
    margin-bottom:1.125rem
  }
}
.home h1,
.home .h1 {
  font-size:calc(1.875rem + 1.5vw)
}
a:hover,
a:focus {
  text-decoration:underline
}
a.btn:hover,
a.btn:focus {
  text-decoration:none
}
.section {
  padding-top:5rem;
  padding-bottom:5rem
}
.section-sm {
  padding-top:1rem;
  padding-bottom:1rem
}
body {
  padding-top:3.5625rem
}
.docs-sidebar {
  order:0;
  display:none;
}
.docs-bar .menu-icon {
  display:block;
}
@media (min-width: 992px) {
  .docs-sidebar {
    order:0;
	display:block;
  }
  .docs-bar {
	border-right:1px solid #e9ecef 
  }
  .docs-bar .menu-icon {
	display:none;
  }
  @supports (position: sticky) {
    .docs-sidebar {
      position:sticky;
      top:4rem;
      z-index:1000;
      height:calc(100vh - 4rem)
    }
  }
}
@media (min-width: 1200px) {
  .docs-sidebar {
    flex:0 1 320px
  }
}
.docs-links {
  padding-bottom:5rem
}
@media (min-width: 992px) {
  @supports (position: sticky) {
    .docs-links {
      max-height:calc(100vh - 4rem);
      overflow-y:scroll
    }
  }
}
@media (min-width: 992px) {
  .docs-links {
    display:block;
    width:auto;
    margin-right:-1.5rem;
    padding-bottom:4rem
  }
}
.docs-toc {
  order:2
}
@supports (position: sticky) {
  .docs-toc {
    position:sticky;
    top:4rem;
    height:calc(100vh - 4rem);
    overflow-y:auto
  }
}
.docs-content {
  padding-bottom:3rem;
  order:1
}
.docs-navigation {
  border-top:1px solid #e9ecef;
  margin-top:2rem;
  margin-bottom:0;
  padding-top:2rem
}
.docs-navigation a {
  font-size:.9rem
}
@media (min-width: 992px) {
  .docs-navigation {
    margin-bottom:-1rem
  }
  .docs-navigation a {
    font-size:1rem
  }
}
.navbar a:hover,
.navbar a:focus {
  text-decoration:none
}
#toc ul {
  padding-left:0;
  list-style:none
}
::-moz-selection {
  background:rgba(212,53,159,0.2)
}
::selection {
  background:rgba(212,53,159,0.2)
}
.bg-dots {
  background-image:radial-gradient(#dee2e6 15%, transparent 15%);
  background-position:0 0;
  background-size:1rem 1rem;
  -webkit-mask:linear-gradient(to top, #fff, transparent);
  mask:linear-gradient(to top, #fff, transparent);
  width:100%;
  height:9rem;
  margin-top:-10rem;
  z-index:-1
}
.katex {
  font-size:1.125rem
}
body.dark {
  background:#212529;
  color:#dee2e6
}
body.dark a {
  color:#8ed6fb
}
body.dark a.text-body {
  color:#dee2e6 !important
}
body.dark .btn-primary {
  color:#1d2d35;
  background-color:#8ed6fb;
  border-color:#8ed6fb;
  color:#212529 !important
}
body.dark .btn-primary:hover {
  color:#1d2d35;
  background-color:#9fdcfc;
  border-color:#99dafb
}
body.dark .btn-primary:focus {
  color:#1d2d35;
  background-color:#9fdcfc;
  border-color:#99dafb;
  box-shadow:0 0 0 .25rem rgba(125,189,221,0.5)
}
body.dark .btn-primary:active,
body.dark .btn-primary.active {
  color:#1d2d35;
  background-color:#a5defc;
  border-color:#99dafb
}
body.dark .btn-primary:active:focus,
body.dark .btn-primary.active:focus {
  box-shadow:0 0 0 .25rem rgba(125,189,221,0.5)
}
body.dark .btn-primary:disabled,
body.dark .btn-primary.disabled {
  color:#1d2d35;
  background-color:#8ed6fb;
  border-color:#8ed6fb
}
body.dark .btn-outline-primary {
  color:#8ed6fb;
  border-color:#8ed6fb;
  color:#8ed6fb
}
body.dark .btn-outline-primary:hover {
  color:#8ed6fb;
  background-color:#8ed6fb;
  border-color:#8ed6fb
}
body.dark .btn-outline-primary:focus {
  box-shadow:0 0 0 .25rem rgba(142,214,251,0.5)
}
body.dark .btn-outline-primary:active,
body.dark .btn-outline-primary.active {
  color:#1d2d35;
  background-color:#8ed6fb;
  border-color:#8ed6fb
}
body.dark .btn-outline-primary:active:focus,
body.dark .btn-outline-primary.active:focus {
  box-shadow:0 0 0 .25rem rgba(142,214,251,0.5)
}
body.dark .btn-outline-primary:disabled,
body.dark .btn-outline-primary.disabled {
  color:#8ed6fb;
  background-color:transparent
}
body.dark .btn-outline-primary:hover {
  color:#212529
}
body.dark .navbar {
  background:#212529;
  opacity:0.975;
  border-bottom:1px solid #1b1f22
}
body.dark.home .navbar {
  border-bottom:0
}
body.dark .navbar-light .navbar-brand {
  color:#dee2e6 !important
}
body.dark .navbar-light .navbar-nav .nav-link {
  color:#dee2e6
}
body.dark .navbar-light .navbar-nav .nav-link:hover,
body.dark .navbar-light .navbar-nav .nav-link:focus {
  color:#8ed6fb
}
body.dark .navbar-light .navbar-nav .nav-link.disabled {
  color:rgba(255,255,255,0.25)
}
body.dark .navbar-light .navbar-nav .show>.nav-link,
body.dark .navbar-light .navbar-nav .active>.nav-link,
body.dark .navbar-light .navbar-nav .nav-link.show,
body.dark .navbar-light .navbar-nav .nav-link.active {
  color:#8ed6fb
}
body.dark .navbar-light .navbar-text {
  color:#dee2e6
}
body.dark .alert-primary a {
  color:#212529
}
body.dark .alert-warning {
  background:#1b1f22;
  color:#dee2e6
}
body.dark .page-links a {
  color:#dee2e6
}
body.dark .showcase-meta a {
  color:#dee2e6
}
body.dark .showcase-meta a:hover,
body.dark .showcase-meta a:focus {
  color:#8ed6fb
}
body.dark .docs-link:hover,
body.dark .docs-link.active,
body.dark .page-links a:hover {
  text-decoration:none;
  color:#8ed6fb
}
body.dark .navbar-light .navbar-text a {
  color:#8ed6fb
}
body.dark .docs-links h3.sidebar-link a,
body.dark .docs-links .sidebar-link.h3 a,
body.dark .page-links h3.sidebar-link a,
body.dark .page-links .sidebar-link.h3 a {
  color:#dee2e6
}
body.dark .navbar-light .navbar-text a:hover,
body.dark .navbar-light .navbar-text a:focus {
  color:#8ed6fb
}
body.dark .navbar .btn-link {
  color:#dee2e6
}
body.dark .content .btn-link {
  color:#8ed6fb
}
body.dark .content .btn-link:hover {
  color:#8ed6fb
}
body.dark .navbar .btn-link:hover {
  color:#8ed6fb
}
body.dark .navbar .btn-link:active {
  color:#8ed6fb
}
body.dark .form-control.is-search {
  background:#1b1f22
}
body.dark .navbar-form::after {
  color:#6c757d;
  border:1px solid #343a40
}
body.dark .form-control {
  color:#adb5bd
}
body.dark .form-control:focus {
  box-shadow:0 0 0 0.2rem #9adafb
}
body.dark .border-top {
  border-top:1px solid #1b1f22 !important
}
@media (min-width: 992px) {
  body.dark .docs-sidebar {
    order:0;
	display:block;
  }
  body.dark .docs-bar {
	border-right:1px solid #1b1f22;
  }
}
body.dark .docs-navigation {
  border-top:1px solid #1b1f22
}
body.dark pre code::-webkit-scrollbar-thumb {
  background:#1b1f22
}
body.dark code:not(.hljs) {
  background:#1b1f22;
  color:#dee2e6
}
body.dark pre code:hover {
  scrollbar-width:thin;
  scrollbar-color:#1b1f22 transparent
}
body.dark pre code::-webkit-scrollbar-thumb:hover {
  background:#1b1f22
}
body.dark blockquote {
  border-left:3px solid #1b1f22
}
body.dark .footer {
  border-top:1px solid #1b1f22
}
body.dark .docs-links,
body.dark .docs-toc {
  scrollbar-width:thin;
  scrollbar-color:#212529 #212529
}
body.dark .docs-links::-webkit-scrollbar,
body.dark .docs-toc::-webkit-scrollbar {
  width:5px
}
body.dark .docs-links::-webkit-scrollbar-track,
body.dark .docs-toc::-webkit-scrollbar-track {
  background:#212529
}
body.dark .docs-links::-webkit-scrollbar-thumb,
body.dark .docs-toc::-webkit-scrollbar-thumb {
  background:#212529
}
body.dark .docs-links:hover,
body.dark .docs-toc:hover {
  scrollbar-width:thin;
  scrollbar-color:#1b1f22 #212529
}
body.dark .docs-links:hover::-webkit-scrollbar-thumb,
body.dark .docs-toc:hover::-webkit-scrollbar-thumb {
  background:#1b1f22
}
body.dark .docs-links::-webkit-scrollbar-thumb:hover,
body.dark .docs-toc::-webkit-scrollbar-thumb:hover {
  background:#1b1f22
}
body.dark .docs-links h3:not(:first-child),
body.dark .docs-links .h3:not(:first-child) {
  border-top:1px solid #1b1f22
}
body.dark a.docs-link {
  color:#dee2e6
}
body.dark .page-links li:not(:first-child) {
  border-top:1px dashed #1b1f22
}
body.dark .card {
  background:#212529;
  border:1px solid #1b1f22
}
body.dark .card.bg-light {
  background:#1b1f22 !important
}
body.dark .navbar .menu-icon .navicon {
  background:#dee2e6
}
body.dark .navbar .menu-icon .navicon::before,
body.dark .navbar .menu-icon .navicon::after {
  background:#dee2e6
}
body.dark .logo-light {
  display:none !important
}
body.dark .logo-dark {
  display:inline-block !important
}
body.dark .bg-light {
  background:#1e2125 !important
}
body.dark .bg-dots {
  background-image:radial-gradient(#556370 15%, transparent 15%)
}
body.dark .text-muted {
  color:#c8cfd6 !important
}
body.dark .alert-primary {
  background:#8ed6fb;
  color:#212529
}
body.dark .figure-caption {
  color:#dee2e6
}
body.dark .copy-status::after {
  content:"Copy";
  display:block;
  color:#dee2e6
}
body.dark .copy-status:hover::after {
  content:"Copy";
  display:block;
  color:#8ed6fb
}
body.dark .copy-status:focus::after,
body.dark .copy-status:active::after {
  content:"Copied";
  display:block;
  color:#8ed6fb
}
.hljs {
  display:block;
  overflow-x:auto;
  padding:0.5em;
  background:#fbf7f0;
  color:#1d2d35
}
.hljs-string,
.hljs-variable,
.hljs-template-variable,
.hljs-symbol,
.hljs-bullet,
.hljs-section,
.hljs-addition,
.hljs-attribute,
.hljs-link {
  color:#d32e9d
}
.hljs-comment,
.hljs-quote,
.hljs-meta,
.hljs-deletion {
  color:#888
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-section,
.hljs-name,
.hljs-type,
.hljs-strong {
  font-weight:bold
}
.hljs-emphasis {
  font-style:italic
}
body.dark .hljs {
  background:#1b1f22;
  color:#dee2e6
}
body.dark .hljs-string,
body.dark .hljs-variable,
body.dark .hljs-template-variable,
body.dark .hljs-symbol,
body.dark .hljs-bullet,
body.dark .hljs-section,
body.dark .hljs-addition,
body.dark .hljs-attribute,
body.dark .hljs-link {
  color:#8ed6fb
}
pre,
code,
kbd,
samp {
  font-family:sfmono-regular,menlo,monaco,consolas,"Liberation Mono","Courier New",monospace;
  font-size:.875rem;
  border-radius:.25rem
}
code {
  background:#fbf7f0;
  color:#1d2d35;
  padding:0.25rem 0.5rem
}
pre {
  margin:2rem 0
}
pre code {
  display:block;
  overflow-x:auto;
  line-height:1.5;
  padding:1.25rem 1.5rem;
  -moz-tab-size:4;
  -o-tab-size:4;
  tab-size:4;
  scrollbar-width:thin;
  scrollbar-color:transparent transparent
}
.hljs {
  padding:1.25rem 1.5rem
}
@media (max-width: 575.98px) {
  pre,
  code,
  kbd,
  samp {
    border-radius:0
  }
  pre {
    margin:2rem -1.5rem
  }
}
pre code::-webkit-scrollbar {
  height:5px
}
pre code::-webkit-scrollbar-thumb {
  background:#e9ecef
}
pre code:hover {
  scrollbar-width:thin;
  scrollbar-color:#e9ecef transparent
}
pre code::-webkit-scrollbar-thumb:hover {
  background:#e9ecef
}
.alert {
  font-family:sfmono-regular,menlo,monaco,consolas,"Liberation Mono","Courier New",monospace;
  font-size:.875rem
}
.alert-icon {
  margin-right:0.75rem
}
.docs .alert {
  margin:2rem -1.5rem
}
.alert-warning {
  background:#fbf7f0;
  color:#1d2d35
}
.alert-primary {
  color:#fff;
  background-color:#2b388f
}
.navbar .btn-link {
  color:#1d2d35;
  padding:0.4375rem 0
}
#mode {
  margin-right:0.5rem
}
.btn-link:focus {
  outline:0;
  box-shadow:none
}
#navigation {
  margin-left:1.25rem
}
@media (min-width: 768px) {
  #mode {
    margin-right:0.5rem
  }
  .navbar .btn-link {
    padding:0.5625em 0.25rem 0.5rem 0.125rem
  }
}
.navbar .btn-link:hover {
  color:#5d2f86
}
.navbar .btn-link:active {
  color:#5d2f86
}
body .toggle-dark {
  display:block
}
body .toggle-light {
  display:none
}
body.dark .toggle-light {
  display:block
}
body.dark .toggle-dark {
  display:none
}
.btn-clipboard {
  display:none
}
@media (min-width: 768px) {
  .doks-clipboard {
    position:relative;
    float:right
  }
  .btn-clipboard {
    position:absolute;
    top:1rem;
    right:0.25rem;
    z-index:10;
    display:block;
    padding:0.25rem 0.5rem;
    font-size:.875rem
  }
}
.copy-status::after {
  content:"Copy";
  display:block;
  color:#1d2d35
}
.copy-status:hover::after {
  content:"Copy";
  display:block;
  color:#d32e9d
}
.copy-status:focus::after,
.copy-status:active::after {
  content:"Copied";
  display:block;
  color:#d32e9d
}
blockquote {
  margin-bottom:1rem;
  font-size:1.25rem;
  border-left:3px solid #dee2e6;
  padding-left:1rem
}
figure {
  margin:2rem 0
}
.figure-caption {
  margin:0.25rem 0 0.75rem
}
.blur-up {
  filter:blur(5px)
}
.blur-up.lazyloaded {
  filter:unset
}
.img-simple {
  margin-top:0.375rem;
  margin-bottom:1.25rem
}
.navbar-form {
  position:relative
}
#suggestions {
  position:absolute;
  right:0;
  margin-top:0.5rem;
  width:calc(100vw - 3rem)
}
#suggestions a {
  display:block;
  text-decoration:none;
  padding:0.75rem;
  margin:0 0.5rem
}
#suggestions a:focus {
  background:#f8f9fa;
  outline:0
}
#suggestions div:not(:first-child) {
  border-top:1px dashed #e9ecef
}
#suggestions div:first-child {
  margin-top:0.5rem
}
#suggestions div:last-child {
  margin-bottom:0.5rem
}
#suggestions a:hover {
  background:#f8f9fa
}
#suggestions span {
  display:flex;
  font-size:1rem
}
#suggestions span:first-child {
  font-weight:700;
  color:#1d2d35
}
#suggestions span:nth-child(2) {
  color:#495057
}
@media (min-width: 576px) {
  #suggestions {
    width:30rem
  }
  #suggestions a {
    display:flex
  }
  #suggestions span:first-child {
    width:9rem;
    padding-right:1rem;
    border-right:1px solid #e9ecef;
    display:inline-block;
    text-align:right
  }
  #suggestions span:nth-child(2) {
    width:19rem;
    padding-left:1rem
  }
}
table {
  margin:3rem 0
}
.footer {
  border-top:1px solid #e9ecef;
  padding-top:1.125rem;
  padding-bottom:1.125rem
}
.footer ul {
  margin-bottom:0
}
.footer li {
  font-size:.875rem;
  margin-bottom:0
}
@media (min-width: 768px) {
  .footer li {
    font-size:1rem
  }
}
.navbar-text {
  margin-left:1rem
}
.navbar-brand {
  font-weight:700
}
.navbar-light .navbar-brand,
.navbar-light .navbar-brand:hover,
.navbar-light .navbar-brand:active {
  color:#1d2d35
}
.navbar-light .navbar-nav .active .nav-link {
  color: #5d2f86;
}
@media (min-width: 768px) {
  .navbar-brand {
    font-size:1.375rem
  }
  .navbar-text {
    margin-left:1.25rem
  }
}
.navbar-nav {
  flex-direction:row
}
.nav-item {
  margin-left:1.25rem
}
@media (min-width: 768px) {
  .nav-item {
    margin-left:0.5rem
  }
}
@media (max-width: 575.98px) {
  .nav-item:first-child {
    margin-left:0
  }
}
@media (max-width: 767.98px) {
  .navbar .container {
    padding-left:1.5rem;
    padding-right:1.5rem
  }
}
.break {
  flex-basis:100%;
  height:0
}
.navbar {
  background-color:rgba(255,255,255,0.95);
  border-bottom:1px solid #e9ecef;
  margin-top:4px
}
.header-bar {
  border-top:4px solid;
  border-image-source:linear-gradient(90deg, #2b388f, #8ed6fb 50%, #d32e9d);
  border-image-slice:1
}
.home .navbar {
  border-bottom:0
}
.navbar-form {
  position:relative;
  margin-top:0.25rem
}
@media (min-width: 768px) {
  .navbar-brand {
    margin-right:1rem !important
  }
  .main-nav .nav-item:first-child .nav-link,
  .social-nav .nav-item:first-child .nav-link {
    padding-left:0
  }
  .main-nav .nav-item:last-child .nav-link,
  .social-nav .nav-item:last-child .nav-link {
    padding-right:0
  }
  .navbar-form {
    margin-top:0;
    margin-left:6rem;
    margin-right:1.5rem
  }
}
@media (min-width: 992px) {
  .navbar-form {
    margin-left:15rem
  }
}
@media (min-width: 1200px) {
  .navbar-form {
    margin-left:30rem
  }
}
.form-control.is-search {
  padding-right:2.5rem;
  background:#f8f9fa;
  border:0
}
.navbar-form::after {
  position:absolute;
  top:0.4625rem;
  right:0.5375rem;
  display:flex;
  align-items:center;
  justify-content:center;
  height:1.5rem;
  padding-right:0.4375rem;
  padding-left:0.4375rem;
  font-size:.75rem;
  color:#495057;
  content:"/";
  border:1px solid #dee2e6;
  border-radius:0.25rem
}
.algolia-autocomplete {
  display:flex !important
}
.algolia-autocomplete .ds-dropdown-menu {
  box-shadow:0 0.5rem 1rem rgba(0,0,0,0.15) !important
}
@media (max-width: 575.98px) {
  .algolia-autocomplete .ds-dropdown-menu {
    max-width:512px !important;
    min-width:312px !important;
    width:auto !important
  }
  .algolia-autocomplete .algolia-docsearch-suggestion .algolia-docsearch-suggestion--subcategory-column::after {
    content:"|";
    margin-right:0.25rem
  }
}
.algolia-autocomplete .algolia-docsearch-suggestion--title {
  margin-bottom:0
}
.algolia-autocomplete .algolia-docsearch-suggestion--highlight {
  padding:0 0.05em
}
.algolia-autocomplete .algolia-docsearch-footer {
  margin-top:1rem;
  margin-right:0.5rem;
  margin-bottom:0.5rem
}
.navbar .menu-icon {
  cursor:pointer;
  padding:1.125rem 0.625rem;
  margin:0 0 0 -0.625rem;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none
}
.navbar .menu-icon .navicon {
  background:#1d2d35;
  display:block;
  height:2px;
  position:relative;
  transition:background 0.2s ease-out;
  width:18px
}
.navbar .menu-icon .navicon::before,
.navbar .menu-icon .navicon::after {
  background:#1d2d35;
  content:"";
  display:block;
  height:100%;
  position:absolute;
  transition:all 0.2s ease-out;
  width:100%
}
.navbar .menu-icon .navicon::before {
  top:5px
}
.navbar .menu-icon .navicon::after {
  top:-5px
}
.navbar .menu-btn {
  display:none
}
.navbar .menu-btn:checked~.navbar-collapse {
  display:block;
  max-height:100vh
}
.navbar .menu-btn:checked~.menu-icon .navicon {
  background:transparent
}
.navbar .menu-btn:checked~.menu-icon .navicon::before {
  transform:rotate(-45deg)
}
.navbar .menu-btn:checked~.menu-icon .navicon::after {
  transform:rotate(45deg)
}
.navbar .menu-btn:checked~.menu-icon:not(.steps) .navicon::before,
.navbar .menu-btn:checked~.menu-icon:not(.steps) .navicon::after {
  top:0
}
#content>h1[id]::before,
#content>.h1[id]::before,
#content>h2[id]::before,
#content>.h2[id]::before,
#content>h3[id]::before,
#content>.h3[id]::before,
#content>h4[id]::before,
#content>.h4[id]::before,
.subpages h2[id]::before {
  display:block;
  height:6rem;
  margin-top:-6rem;
  content:""
}
#more {
  display:block;
  height:6rem;
  margin-top:-6rem;
  content:""
}
.anchor {
  visibility:hidden;
  padding-left:0.5rem
}
h1:hover a,
.h1:hover a,
h2:hover a,
.h2:hover a,
h3:hover a,
.h3:hover a,
h4:hover a,
.h4:hover a {
  visibility:visible;
  text-decoration:none
}
.card-list {
  margin-top:2.25rem
}
.edit-page {
  margin-top:3rem;
  font-size:1rem
}
.edit-page svg {
  margin-right:0.5rem;
  margin-bottom:0.25rem
}
p.meta {
  margin-top:0.5rem;
  font-size:1rem
}
.breadcrumb {
  margin-top:2.25rem;
  font-size:1rem
}
.home .card,
.contributors.list .card,
.blog.list .card {
  margin-top:2rem;
  margin-bottom:2rem;
}
.home .card:hover,
.contributors.list .card:hover,
.blog.list .card:hover {

}
.home .card-body,
.contributors.list .card-body,
.blog.list .card-body {
  padding:0 2rem 1rem
}
.blog-header {
  margin-bottom:2rem
}
.docs-links,
.docs-toc {
  scrollbar-width:thin;
  scrollbar-color:#fff #fff
}
.docs-links::-webkit-scrollbar,
.docs-toc::-webkit-scrollbar {
  width:5px
}
.docs-links::-webkit-scrollbar-track,
.docs-toc::-webkit-scrollbar-track {
  background:#fff
}
.docs-links::-webkit-scrollbar-thumb,
.docs-toc::-webkit-scrollbar-thumb {
  background:#fff
}
.docs-links:hover,
.docs-toc:hover {
  scrollbar-width:thin;
  scrollbar-color:#e9ecef #fff
}
.docs-links:hover::-webkit-scrollbar-thumb,
.docs-toc:hover::-webkit-scrollbar-thumb {
  background:#e9ecef
}
.docs-links::-webkit-scrollbar-thumb:hover,
.docs-toc::-webkit-scrollbar-thumb:hover {
  background:#e9ecef
}
.docs-links h3,
.docs-links .h3,
.page-links h3,
.page-links .h3 {
  text-transform:uppercase;
  font-size:1rem;
  margin:1.25rem 0 0.5rem 0;
  padding:1.5rem 0 0 0
}
@media (min-width: 992px) {
  .docs-links h3,
  .docs-links .h3,
  .page-links h3,
  .page-links .h3 {
    margin:1.125rem 1.5rem 0.75rem 0;
    padding:1.375rem 0 0 0
  }
}
.docs-links h3:not(:first-child),
.docs-links .h3:not(:first-child) {
  border-top:1px solid #e9ecef
}
a.docs-link, .subnav a {
  color:#1d2d35;
  display:block;
  padding:0.125rem 0;
  font-size:1rem
}
.subnav .active > a{
  color: #5d2f86;
}
.page-links li {
  margin-top:0.375rem;
  padding-top:0.375rem
}
.page-links li ul li {
  border-top:none;
  padding-left:1rem;
  margin-top:0.125rem;
  padding-top:0.125rem
}
.page-links li:not(:first-child) {
  border-top:1px dashed #e9ecef
}
.page-links a {
  color:#1d2d35;
  display:block;
  padding:0.125rem 0;
  font-size:.9375rem
}
.docs-link:hover,
.docs-link.active,
.page-links a:hover {
  text-decoration:none;
  color:#5d2f86
}
.docs-links h3.sidebar-link,
.docs-links .sidebar-link.h3,
.page-links h3.sidebar-link,
.page-links .sidebar-link.h3 {
  text-transform:none;
  font-size:1.125rem;
  font-weight:normal
}
.docs-links h3.sidebar-link a,
.docs-links .sidebar-link.h3 a,
.page-links h3.sidebar-link a,
.page-links .sidebar-link.h3 a {
  color:#1d2d35
}
.docs-links h3.sidebar-link a:hover,
.docs-links .sidebar-link.h3 a:hover,
.page-links h3.sidebar-link a:hover,
.page-links .sidebar-link.h3 a:hover {
  text-decoration:underline
}

.nav {
  display: block;
}

.navbar-nav {
  display:flex;
}

.breadcrumb a {
  padding: 0 0.3rem;
}

.breadcrumb a:first-child{
  padding-left:0;
}

.nav, .subnav {
  padding-left: 0;
  list-style: none;
}

.docs-links .nav > li {
  margin: 1.125rem 1.5rem 0.75rem 0;
  padding: 1.375rem 0 0 0;
}

.docs-links .nav > li:not(:first-child) {
  border-top: 1px solid #e9ecef;
}

.docs-toc #toc {
  border-bottom: 1px solid #e9ecef;
  margin-bottom:1rem;
  padding-bottom: 1rem;
}

body.dark .docs-links .nav > li:not(:first-child) {
 border-top: 1px solid #1b1f22;
}

body.dark .docs-toc #toc {
  border-bottom: 1px solid #1b1f22;
  margin-bottom:1rem;
  padding-bottom: 1rem;
}

.docs-links .nav > li > a {
  text-transform: uppercase;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.2;
} 

body.dark .docs-links .nav > li  a {
  color: #dee2e6;
}

body.dark .docs-links .nav > li.active  li.active > a,  body.dark .docs-links .nav > li.active > a.dropdown-toggle {
  text-decoration: none;
  color: #8ed6fb;
}

#toc .h3-toc {
  margin-left: .25in;
}
#toc .h4-toc {
  margin-left: .50in;
}
#toc .h5-toc {
  margin-left: .75in;
}
#toc .h6-toc {
  margin-left: 1in;
}

#toc > div {
  margin-bottom:0.3rem;
}

div.content-body {
  margin-bottom: 1rem;
}

.top-menu ul.dropdown-menu {
  display:none;
}

img, iframe {
  max-width:100%;
}

p.post-footer {
  padding-top:1rem;
}

.cat-meta {
padding-right: 0.5rem;	
}

.tag-meta a::after {
  content: ',';
} 
.tag-meta a:last-child:after {
  content: '';
}

.quote blockquote{
  padding: 2rem;
}

.content-media {
  margin-bottom: 2rem;
}

.related {
  padding-top: 1rem;
}

.related ul {
  padding-bottom: 0.5rem;
}

/*! Docs icon */
.docs-bar .menu-icon {
  cursor:pointer;
  padding:1.75rem 0;
  padding-bottom: 1.25rem;
  margin:0 0 0 -0.625rem;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select:none;
  user-select:none
}
.docs-bar .menu-icon .navicon {
  background:#1d2d35;
  display:block;
  height:2px;
  position:relative;
  transition:background 0.2s ease-out;
  width:18px
}
.docs-bar .menu-icon .navicon::before,
.docs-bar .menu-icon .navicon::after {
  background:#1d2d35;
  content:"";
  display:block;
  height:100%;
  position:absolute;
  transition:all 0.2s ease-out;
  width:100%
}
.docs-bar .menu-icon .navicon::before {
  top:5px
}
.docs-bar .menu-icon .navicon::after {
  top:-5px
}
.docs-bar .menu-btn {
  display:none
}
.docs-bar .menu-btn:checked~.docs-bar-collapse {
  display:block;
  max-height:100vh
}
.docs-bar .menu-btn:checked~.menu-icon .navicon {
  background:transparent
}
.docs-bar .menu-btn:checked~.menu-icon .navicon::before {
  transform:rotate(-45deg)
}
.docs-bar .menu-btn:checked~.menu-icon .navicon::after {
  transform:rotate(45deg)
}
.docs-bar .menu-btn:checked~.menu-icon:not(.steps) .navicon::before,
.docs-bar .menu-btn:checked~.menu-icon:not(.steps) .navicon::after {
  top:0
}

.docs-bar .menu-btn:checked ~ .docs-sidebar {
  display: block;
  max-height: 100%;
}

body.dark .docs-bar .menu-icon .navicon{
  background-color: #dee2e6;
}

body.dark .docs-bar .menu-icon .navicon::before, body.dark .docs-bar .menu-icon .navicon::after {
  background: #dee2e6;
}

.float-right {
  float:right;
}

.jump-link a {
  border: 1px solid #e9ecef;
}

.dark .jump-link a {
  color: #8ed6fb;
  border: 1px solid #1b1f22;
}

.dark .toc-wrapper {
  background: #1b1f22;
  border: 1px solid #1b1f22;
}

.dark .toc-wrapper a {
  color: #dee2e6;
}

.social-logo a {
  padding: 0.40em 0.25rem 0.5rem 0.125rem;
  line-height:1;
  height:24px;
  color: #1d2d35;
  margin-right: 0.5rem!important;
}

body.dark .social-logo a {
  color: #dee2e6;
}

body.dark .social-logo a:hover, .dark .toc-wrapper a:hover {
  color: #8ed6fb;
  text-decoration:none;
}

.list-unstyled a, .list-unstyled .subnav a {
  padding: 0.125rem 0;
}

.list-unstyled li {
  margin: 0;
}