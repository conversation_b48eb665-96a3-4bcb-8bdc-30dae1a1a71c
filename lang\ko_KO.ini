about = "이 블로그는..."
add_category = "새 카테고리 만들기"
add_content = "새 글 쓰기"
add_link = "링크 추가"
add_menu = "메뉴 추가"
add_new_page = "새 페이지 만들기"
add_new_post = "새 글 쓰기"
add_source_link_optional = "소스 링크 추가 (선택 사항)"
add_sub = "서브 페이지 추가"
address_url = "주소 (URL)"
admin = "관리자"
admin_panel_style_based_on = "관리자 패널 스타일 기반: "
all_blog_posts = "모든 블로그 글"
all_cache_has_been_deleted = "모든 캐시를 삭제했어요!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "아카이브"
are_you_sure_you_want_to_delete_ = "정말 삭제할거예요? '<strong>%s</strong>'"
at_the_moment_you_are_using_auto_generated_menu = "지금은 자동으로 만든 메뉴를 쓰고 있어요."
audio_post = "오디오 첨부"
audio_post_comment = "오디오를 넣은 글을 쓸 수 있어요"
author = "글쓴이"
author_description = "또 다른 HTMLy 사용자"
back_to = "돌아가기"
backup = "백업"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "한 단락으로 이 블로그에 대해 더 알려주세요."
blog_theme = "블로그 테마"
blog_title = "블로그 제목"
blog_title_placeholder = "내 HTMLy 블로그"
blog_posts_displayed_as = "글 보기 모드"
breadcrumb_home_text = "사이트 이동 홈 (Breadcrumb)"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "가져오기를 사용하면 피드가 사용자의 것이거나 적어도 게시할 권한이 있다고 동의하는거예요."
css_class_optional = "CSS 클래스 (선택 사항)"
cache_expiration = "캐시 만료 (시간 단위) "
cache_off = "캐시 끄기"
cache_timestamp = "캐시 타임 스탬프"
cancel = "취소"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha가 틀렸어요"
categories = "카테고리"
category = "카테고리"
check_update = "업데이트 확인"
clear_cache = "캐시 지우기"
comma_separated_values = "쉼표로 분리된 값"
comment_system = "댓글 시스템"
comments = "댓글"
config = "설정"
congrats_you_have_the_latest_version_of_htmly = "축하해요! HTMLy 최신 버전을 쓰고 있어요."
content = "콘텐츠"
contents = "콘텐츠"
copyright_line = "저작권 표시"
copyright_line_placeholder = "(C) 당신의 이름."
create_backup = "백업 만들기"
created = "만듦"
custom = "사용자 지정"
custom_settings = "사용자 지정 설정"
dashboard = "대시보드"
date = "날짜"
date_format = "날짜 형식"
delete = "지우기"
description = "설명"
disable = "사용 안함"
disabled = "사용 안함"
disqus_shortname = "Disqus shortname"
disqus_shortname_placeholder = "htmly"
draft = "초안"
edit = "편집"
edit_category = "카테고리 편집"
edit_post = "편집"
edit_profile = "프로필 편집"
enable = "사용"
enable_blog_url = "'blog' URL 켜기"
enter_image_url = "이미지 URL을 입력하세요"
facebook_app_id = "페이스북 AppID"
facebook_page = "페이스북 페이지"
featured_audio = "오디오 기능"
featured_image = "이미지 기능"
featured_link = "링크 기능"
featured_quote = "인용 기능"
featured_video = "비디오 기능"
feed_url = "URL 피드"
filename = "파일 이름"
follow = "팔로우"
for_google_site_verification_meta = "google-site-verification 메타"
for_msvalidate_01_meta = "msvalidate.01 메타"
front_page_displays = "첫 페이지 표시"
full_post = "전체 글 보기"
general = "일반"
general_settings = "일반 설정"
get_one_here = "이 곳에서 얻을 수 있어요: "
github_pre_release = "Github 시험판"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "구글 검색 콘솔"
home = "홈"
if_left_empty_we_will_excerpt_it_from_the_content_below = "비워둘 경우 아래 내용에서 발췌해요"
if_the_url_is_left_empty_we_will_use_the_page_title = "URL이 비어 있으면 페이지 제목을 사용해요"
if_the_url_is_left_empty_we_will_use_the_post_title = "URL이 비어 있으면 글 제목을 사용해요"
image_post = "이미지 첨부"
image_post_comment = "이미지를 넣은 글을 쓸 수 있어요"
import = "가져오기"
import_feed = "피드 가져오기 시작"
import_rss = "RSS 가져오기"
import_rss_feed_2.0 = "RSS 2.0 피드 가져오기"
insert_image = "이미지 넣기"
invalid_error = "오류: 사용자나 비밀번호가 틀렸어요"
language = "시스템 언어"
link_name = "링크 이름"
link_post = "링크 첨부"
link_post_comment = "링크가 들어간 글을 쓸 수 있어요"
login = "로그인"
login_page = "로그인 페이지"
logout = "로그아웃"
menu = "메뉴"
menus = "메뉴 편집"
meta_description = "메타 설명"
meta_description_character = "메타 설명 문자"
metatags = "메타 태그"
metatags_settings = "메타 태그 설정"
mine = "내꺼"
more = "더"
my_draft = "내 초안"
my_posts = "내 글"
name = "이름"
newer = "최신"
next = "다음"
next_post = "다음 글"
no_available_backup = "백업이 없어요."
no_draft_found = "초안이 없어요"
no_posts_found = "글이 없어요"
no_related_post_found = "관련된 글이 없어요"
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "찾기 결과가 없어요"
nope = "아니오"
not = "아니오"
older = "오래전"
only = "오직(Only)"
operations = "작업"
page = "페이지"
page_generation_time = "페이지 생성 시간"
pages = "페이지"
pass_error = "비밀번호 필드는 필수예요"
password = "비밀번호"
performance = "성능"
performance_settings = "성능 설정"
permalink = "고정링크"
popular = "인기있는"
popular_posts = "인기 게시물"
popular_posts_widget = "인기 글 위젯"
popular_posts_widget_at_most = "최대 인기 글"
popular_tags = "인기 태그"
post_by_author = "이 글쓴이가 썼어요"
posted_in = "카테고리: "
posted_on = "글을 올렸어요: "
posts = "글"
posts_by = "글쓴이: "
posts_draft = "초안 목록"
posts_in_archive_page_at_most = "최대 아카이브 페이지"
posts_in_category_page_at_most = "최대 카테고리 페이지"
posts_in_front_page_show_at_most = "최대 프론트 페이지"
posts_in_profile_page_at_most = "최대 프로필 페이지"
posts_in_search_result_at_most = "최대 검색 결과"
posts_in_tag_page_at_most = "최대 태그 페이지"
posts_in_type_page_at_most = "최대 타입 페이지"
posts_index_settings = "인덱스 설정"
posts_list = "글 목록"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "시험판 써보기"
prev = "이전"
prev_post = "이전 글"
preview = "미리보기"
profile_for = "Profile for"
proudly_powered_by = "Proudly Powered by"
publish = "올리기"
publish_draft = "올리기"
published = "글쓴이"
quote_post = "인용 글"
quote_post_comment = "인용 글을 쓸 수 있어요"
rss_character = "RSS 글자"
rss_feeds_show_the_most_recent = "최대 최근 RSS 피드"
rss_settings = "RSS 설정"
read_more_text = "더 읽기 표시"
read_more_text_placeholder = "더 읽기"
reading = "읽기"
reading_settings = "읽기 설정"
recaptcha = "reCAPTCHA"
recent_posts = "최근 글"
recent_posts_widget_at_most = "최대 최근 글"
regular_post = "글 쓰기"
regular_post_comment = "평범하게 글을 쓸 수 있어요"
related_posts = "관련 글"
related_widget_posts_at_most = "최대 관련 위젯"
revert_to_draft = "초안으로 되돌리기"
save = "저장"
save_config = "설정 저장"
save_edit = "편집 저장"
save_menu = "메뉴 저장"
save_as_draft = "초안 저장하기"
save_category = "카테고리 저장"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "찾기"
search_for = "찾기"
search_results_for = "Search results for"
search_results_not_found = "찾기 결과가 없어요!"
secret_key = "비밀 키"
settings = "설정"
sign_in_to_start_your_session = "시작하려면 로그인 하세요"
site_key = "사이트 키"
sitemap = "사이트맵"
slug = "줄임표시"
social_media = "소셜 미디어"
static_page = "고정 페이지"
static_page_comment = "고정 페이지 만들기"
static_pages = "고정 페이지"
summary = "요약 보기"
summary_character = "요약 글자수"
tag = "태그"
tagcloud_widget_at_most = "최대 TagCloud"
tagline = "태그 라인"
tagline_placeholder = "데이터베이스를 쓰지 않는 PHP 블로그 플랫폼"
tagline_description = "몇 단어로 이 블로그에 대해 설명해주세요."
tags = "태그"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "이것은 레거시 코드입니다. 보통 gtag.js를 써서 새 분석을 만들어요."
this_page_doesnt_exist = "페이지가 존재하지 않아요!"
time = "시간"
timezone = "시간대"
title = "제목"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Disqus나 페이스북 댓글을 사용하려면 Disqus의 shortname 또는, 페이스북 AppID가 필요해요."
token_error = "CSRF 토큰이 바르지 않아요"
tools = "도구"
twitter_account = "트위터 계정"
type_to_search = "찾을 내용 입력"
uncategorized = "카테고리 없음"
uncategorized_comment = "카테고리가 필요하지 않거나 다른 카테고리에 넣을 수 없는 주제"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "업데이트"
update_available = "업데이트가 있어요"
update_draft = "초안 업데이트"
update_post = "글 업데이트"
update_to = "업데이트: "
upload = "업로드"
user = "사용자"
user_error = "사용자 필드는 필수예요"
valid_values_range_from_0_to_1.0._see = "유효한 범위는 0.0에서 1.0까지예요. 참조: "
video_post = "비디오 첨부"
video_post_comment = "비디오를 넣은 글을 쓸 수 있어요"
view = "보기"
view_post = "보기"
views = "보기"
widget = "위젯"
widget_settings = "위젯 설정"
would_you_like_to_try_our = "우리 제품을 시험해 보시겠어요? "
yes_im_in = "예, 써볼께요"
yes_not_recommended = "예 (권장하지 않음)"
you_dont_have_permission_to_access_this_page = "이 페이지에 접근할 권한이 없어요"
your_new_config_key = "새 설정 키"
your_new_value = "새 값"
your_backups = "백업"
your_latest_blog_posts = "최신 블로그 글"
your_recent_posts = "최근 글"
by = ": "
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>힌트:</u> <code>CTRL</code>/<code>CMD</code> + <code>F</code> 를 눌러 키나 값을 찾을 수 있어요."
homepage = "홈페이지"
instead = "대신(instead)"
item_class = "CSS 클래스 추가"
item_slug = "링크 URL 추가"
now = "지금"
of = "of"
optional = "선택 사항"
post_your_post_slug = "/post/문서-줄임표시"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>추가 힌트:</u> 사용자 지정 설정 키를 만들고 템플릿 어디에서나 키 값을 인쇄할 수 있어요."
read_more = "더 읽기"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/년/월/문서-줄임표시"
your_key = "your.key"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
