about = "Informacje"
add_category = "Dodaj kategorię"
add_content = "Dodaj zawartoś<PERSON>"
add_link = "Dodaj odnośnik"
add_menu = "Dodaj menu"
add_new_page = "Dodaj nową stronę"
add_new_post = "Dodaj nowy post"
add_source_link_optional = "Dodaj odnośnik do źródła (opcjonalne)"
add_sub = "Dodaj podstronę"
address_url = "Addres (URL)"
admin = "Admin"
admin_panel_style_based_on = "Styl panelu administracyjnego bazuje na"
all_blog_posts = "Wszystkie posty"
all_cache_has_been_deleted = "Pamięć podręczna została wyczyszczona!"
all_posts_tagged = "All posts tagged"
archive_for = "Archive for"
archive_page_for = "Archive page for"
archives = "Archiwa"
are_you_sure_you_want_to_delete_ = "<PERSON><PERSON> ch<PERSON><PERSON> usun<PERSON> <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "W tym momencie menu jest generowane automatycznie."
audio_post = "Post z audio"
audio_post_comment = "Utwórz dźwiękowy post na blogu"
author = "Autor"
author_description = "Po prostu kolejne konto HTMLy"
back_to = "Wróć do"
backup = "Kopia zapasowa"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "W jednym akapicie opisz ten blog."
blog_theme = "Motyw bloga"
blog_title = "Tytuł bloga"
blog_title_placeholder = "Skrót bloga"
blog_posts_displayed_as = "Posty blogowe wyświetlane jako"
breadcrumb_home_text = "Tekst nawigacji domowej"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Używając tego importera zgadzasz się, że feed jest Twój lub przynajmniej masz uprawnienia do jego publikacji."
css_class_optional = "Klasy CSS (opcjonalne)"
cache_expiration = "Czas trwania pamięci tymczasowej (w godzinach)"
cache_off = "Pamięć tymczasowa wyłączona"
cache_timestamp = "Odcisk czasu pamięci podręcznej"
cancel = "Anuluj"
cannot_read_feed_content = "Cannot read feed content"
captcha_error = "reCaptcha błędna"
categories = "Kategorie"
category = "Kategoria"
check_update = "Sprawdź aktualizacje"
clear_cache = "Wyczyść pamięć podręczną"
comma_separated_values = "Wartości rozdzielane przecinkiem"
comment_system = "System komentarzy"
comments = "Komentarze"
config = "Konfiguracja"
congrats_you_have_the_latest_version_of_htmly = "Gratulacje! Masz aktualną wersję HTMLy."
content = "Zawartość"
contents = "Zawartość"
copyright_line = "Fragment praw autorskich"
copyright_line_placeholder = "(c) Twoje imię."
create_backup = "Stwórz kopie zapasowe"
created = "Utworzono"
custom = "Modyfikacje"
custom_settings = "Ustawienia modyfikacji"
dashboard = "Kokpit"
date = "Data"
date_format = "Format daty"
delete = "Usuń"
description = "Opis"
disable = "Wyłączenie"
disabled = "Nieaktywne"
disqus_shortname = "Disqus skrócona nazwa"
disqus_shortname_placeholder = "htmly"
draft = "Wersja robocza"
edit = "Edytuj"
edit_category = "Edytuj kategorię"
edit_post = "Edycja"
edit_profile = "Edytuj profil"
enable = "Włączenie"
enable_blog_url = "Włączenie URL blog"
enter_image_url = "Wpisz URL obrazka"
facebook_app_id = "Facebook App ID"
facebook_page = "Strona Facebook"
featured_audio = "Prezentowany Dźwięk"
featured_image = "Prezentowany Obraz"
featured_link = "Prezentowany Odnośnik"
featured_quote = "Prezentowany Cytat"
featured_video = "Prezentowane Wideo"
feed_url = "Feed URL"
filename = "Nazwa pliku"
follow = "Śledź"
for_google_site_verification_meta = "Dla google-site-verification meta"
for_msvalidate_01_meta = "Dla msvalidate.01 meta"
front_page_displays = "Wyświetlenie strony startowej"
full_post = "Pełen post"
general = "Ogólnie"
general_settings = "Ustawienia ogólne"
get_one_here = "Zdobądź jeden teraz"
github_pre_release = "Github pre-release"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Strona domowa"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Jeżeli pozostanie puste, zawartość poniżej zostanie użyta jako zajawka"
if_the_url_is_left_empty_we_will_use_the_page_title = "Jeżeli URL pozostanie puste, tytuł strony zostanie użyty"
if_the_url_is_left_empty_we_will_use_the_post_title = "Jeżeli URL pozostanie puste, tytuł posta zostanie użyty"
image_post = "Post obrazkowy"
image_post_comment = "Tworzy post z wyróżnionym zdjęciem"
import = "Import"
import_feed = "Rozpocznij import Feed'a"
import_rss = "Import RSS"
import_rss_feed_2.0 = "Importuj RSS Feed 2.0"
insert_image = "Wstaw Obraz"
invalid_error = "BŁĄD: Błędna nazwa konta lub hasło"
language = "Język systemu"
link_name = "Nazwa odnośnika"
link_post = "Odnośnik"
link_post_comment = "Tworzy post z wyróżnionym linkiem"
login = "Login"
login_page = "Strona logowania"
logout = "Wyloguj się"
menu = "Menu"
menus = "Edytor Menu"
meta_description = "Meta-informacje"
meta_description_character = "Ilość znaków metaopisu"
metatags = "Metatags"
metatags_settings = "Ustawienia metatagów"
mine = "Moje"
more = "Więcej"
my_draft = "Mój szkic"
my_posts = "Moje posty"
name = "Nazwa"
newer = "Nigdy"
next = "Dalej"
next_post = "Następny post"
no_available_backup = "W tym momencie nie ma dostępnych kopii zapasowych."
no_draft_found = "Nie znaleziono szkicu"
no_newer_posts = "Nie znaleziono najnowszych postów"
no_posts_found = "Nie znaleziono postów"
no_related_post_found = "Nie znaleziono powiązanych postów."
no_scheduled_posts_found = "No scheduled posts found!"
no_search_results = "Nie znaleziono"
nope = "Nie"
not = "Nie"
older = "Starsze"
only = "Tylko"
operations = "Operacje"
page = "Strona"
page_generation_time = "Czas wygenerowania strony"
pages = "Strony"
pass_error = "Wartość z hasłem konta jest wymagana"
password = "Hasło"
performance = "Wydajność"
performance_settings = "Ustawienia wydajności"
permalink = "Stały odnośnik"
popular = "Popularne"
popular_posts = "Popularne posty"
popular_posts_widget = "Sekcja postów popularnych"
popular_posts_widget_at_most = "Maksymalna ilość postów wyświetlana w Sekcji Postów Popularnych"
popular_tags = "Popularne tagi"
post_by_author = "Posty autorstwa"
posted_in = "Napisano w"
posted_on = "Opublikowano "
posts = "Posty"
posts_by = "Posty "
posts_draft = "Szablon posta"
posts_in_archive_page_at_most = "Maksymalna ilość postów w archiwum"
posts_in_category_page_at_most = "Maksymalna ilość postów na stronie kategorii"
posts_in_front_page_show_at_most = "Posty na stronie startowej widzialne od najnowszego"
posts_in_profile_page_at_most = "Maksymalna ilość postów na stronie profilu"
posts_in_search_result_at_most = "Maksymalna ilość postów na stronie wyszukiwania"
posts_in_tag_page_at_most = "Maksymalna ilość postów na stronie tagu"
posts_in_type_page_at_most = "Maksymalna ilość postów na stronie typu"
posts_index_settings = "Ustawienia indeksowania postów"
posts_list = "Lista postów"
posts_tagged = "Posts tagged"
posts_with_type = "Posts with type"
pre_release = "Pre-release"
prev = "Starszy"
prev_post = "Poprzedni post"
preview = "Podgląd"
profile_for = "Profile for"
proudly_powered_by = "Dumnie tworzone przez"
publish = "Opublikuj"
publish_draft = "Opublikuj wersję roboczą"
published = "Opublikowano"
quote_post = "Cytuj post"
quote_post_comment = "Tworzenie postu z cytatem"
rss_character = "Ilość znaków RSS"
rss_feeds_show_the_most_recent = "Maksymalna ilość wpisów Feed'u RSS"
rss_settings = "Ustawienia RSS"
read_more_text = "Czytaj Więcej tekst"
read_more_text_placeholder = "Czytaj więcej"
reading = "Czytanie"
reading_settings = "Ustawienia czytania"
recaptcha = "reCAPTCHA"
recent_posts = "Ostatnie posty"
recent_posts_widget_at_most = "Maksymalna ilość postów wyświetlana w Sekcji Ostatnich Postów"
regular_post = "Standardowy post"
regular_post_comment = "Tworzenie zwykłego postu"
related_posts = "Powiązane posty"
related_widget_posts_at_most = "Maksymalna ilość postów wyświetlana w Sekcji Postów Powiązanych"
revert_to_draft = "Cofnij do wersji roboczej"
save = "Zapisz"
save_config = "Zapisz konfigurację"
save_edit = "Zapisz edycję"
save_menu = "Zapisz menu"
save_as_draft = "Zapisz jako szkic"
save_category = "Zapisz kategorię"
scheduled = "Scheduled"
scheduled_posts = "Scheduled posts"
scheduled_tips = "Publishing a post with future date or time, it will go into scheduled posts"
search = "Szukaj"
search_for = "Szukaj"
search_results_for = "Search results for"
search_results_not_found = "Brak wyników wyszukiwania!"
secret_key = "Klucz sekretny"
settings = "Ustawienia"
sign_in_to_start_your_session = "Zaloguj się by rozpocząć sesję"
site_key = "Klucz strony"
sitemap = "Mapa strony"
slug = "Uproszczenie"
social_media = "Platforma społecznościowa"
static_page = "Statyczna strona"
static_page_comment = "Tworzy statyczną stronę"
static_pages = "Strony statyczne"
summary = "Podsumowanie"
summary_character = "Ilość znaków podsumowania"
tag = "Tag"
tagcloud_widget_at_most = "Maksymalna ilość tagów w Chmurze Tagów"
tagline = "Krótki opis"
tagline_placeholder = "Bezbazodanowa platforma PHP do blogowania"
tagline_description = "W kilku słowach opsiz o czym ten blog jest."
tags = "Tagi"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "To jest przestarzały kod. Zazwyzcaj nowo utworzone analizy używają gtag.js"
this_page_doesnt_exist = "Ta strona nie istnieje!"
time = "Czas"
timezone = "Strefa czasowa"
title = "Tytuł"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Do używania Disqus lub Komentarzy Facebook potrzeba wpisać ich App ID."
token_error = "Token CSRF jest niepoprawny"
tools = "Narzędzia"
twitter_account = "Konto Twitter"
type_to_search = "Wpisz wyszkukiwaną frazę"
uncategorized = "Bez kategorii"
uncategorized_comment = "Wpisy, któe nie potrzebują kategorii, lub niepasujące do żadnej kategorii."
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Unknown feed format"
update = "Aktualizacja"
update_available = "Aktualizacje są dostępne"
update_draft = "Aktualizuj szkic"
update_post = "Aktualizuj post"
update_to = "Aktualizuj do"
upload = "Wgraj"
user = "Konto"
user_error = "Wartość pola z nazwą konta jest wymagane"
valid_values_range_from_0_to_1.0._see = "Poprawne wartości są w zakresie 0.0 do 1.0. Zobacz"
video_post = "Film"
video_post_comment = "Tworzy post z wyróżnionym filmem."
view = "Zobacz"
view_post = "Podgląd"
views = "Wyświetleń"
widget = "Widgety"
widget_settings = "Ustawienia widgetów"
would_you_like_to_try_our = "Czy przetestujesz "
yes_im_in = "Tak, wchodzę w to"
yes_not_recommended = "Tak (niezalecane)"
you_dont_have_permission_to_access_this_page = "Nie masz uprawnień do tej strony"
your_new_config_key = "Twój nowy klucz konfiguracji"
your_new_value = "Wartość twojego nowego klucza konfiguracji"
your_backups = "Twoje kopie zapasowe"
your_latest_blog_posts = "Twoje ostatnie wpisy"
your_recent_posts = "Twoje ostatnie posty"
by = "przez"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>wskazówka:</u> Użyj <code>Ctrl</code>/<code>CMD</code> + <code>F</code> by wyszukać wartość klucza konfiguracji."
homepage = "strona_domowa"
instead = "zamiast"
item_class = "Wstaw klasy CSS"
item_slug = "Wstaw URL odnośnika"
now = "teraz"
of = " "
optional = "opcjonalne"
post_your_post_slug = "/post/uproszczona-nazwa-postu"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>super wskazówka:</u> Możesz tworzyć własne klucze konfiguracji i wyświetlać je gdziekolwiek w szablonie."
read_more = "Czytaj więcej"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/rok/miesiąc/uproszczona-nazwa-postu"
your_key = "ten.klucz"
summary_behavior = "Summary behavior"
default = "Default"
check_shortcode = "Check shortcode"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In summary mode, whether check the shortcode first or not before trim the content to x char"
manage_users = "Manage users"
add_user = "Add user"
username = "Username"
role = "Role"
change_password = "Change password"
config_mfa = "Configure MFA"
mfacode = "MFA Code"
verify_code = "Verify the MFA code"
verify_password = "Verify current password"
manualsetupkey = "You can also manually add the setup key"
mfa_error = "MFA code is not correct"
disablemfa = "Disable MFA"
enable_auto_save = "Enable Auto Save"
explain_autosave = "When enabled, new content or draft will be automatically saved every 60 seconds."
login_protect_system = "Login protection system"
cloudflare_info = "Review Cloudflare's Turnstile documentation: "
mfa_config = "Multi Factor Authentication (MFA)"
set_mfa_globally = "Set the status of MFA"
explain_mfa = "When enabled, MFA is optional for all users. When disabled, no users can use it and it hides the field on the login page."
set_version_publicly = "Version Visibility"
explain_version = "By default the version of HTMLy is visible publicly in the source code, some admins may prefer to hide this."
focus_mode = "Toggle Focus"
writing = "Writing"
writing_settings = "Writing Settings"
security = "Security"
security_settings = "Security Settings"
msg_error_field_req_username = "Username field is required."
msg_error_field_req_password = "Password field is required."
msg_error_field_req_title = "Title field is required."
msg_error_field_req_content = "Content field is required."
msg_error_field_req_tag = "Tag field is required."
msg_error_field_req_image = "Image field is required."
msg_error_field_req_video = "Video field is required."
msg_error_field_req_link = "Link field is required."
msg_error_field_req_quote = "Quote field is required."
msg_error_field_req_audio = "Audio field is required."
msg_error_field_req_feedurl = "You need to specify the feed url."
rss_feeds_description_select = "RSS Description"
rss_description_body = "Post Body"
rss_description_meta = "Post Meta Description"
admin_theme = "Admin theme"
admin_theme_light = "Light"
admin_theme_dark = "Dark"
search_index = "Search Index"
fulltext_search = "Fulltext Search"
add_search_index = "Add posts to Search Index"
clear_search_index = "Clear Search Index"
unindexed_posts = "Here are the posts that have not been indexed"
indexed_posts = "Posts has been indexed"
custom_fields = "Custom fields"
