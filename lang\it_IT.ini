about = "Informazioni"
add_category = "Aggiungi categoria"
add_content = "Aggiungi contenuto"
add_link = "Aggiungi collegamento"
add_menu = "Aggiungi menu"
add_new_page = "Aggiungi una nuova pagina"
add_new_post = "Aggiungi un nuovo articolo"
add_source_link_optional = "Aggiungi collegamento sorgente (facoltativo)"
add_sub = "Aggiungi sub"
address_url = "Indirizzo (URL)"
admin = "Pannello Amministratore"
admin_panel_style_based_on = "Stile del pannello amministratore basato su"
all_blog_posts = "Tutti gli articoli del blog"
all_cache_has_been_deleted = "Tutta la cache è stata svuotata !"
all_posts_tagged = "Tutti gli articoli etichettati"
archive_for = "Archivio per"
archive_page_for = "Pagina di archivio per"
archives = "Archivi"
are_you_sure_you_want_to_delete_ = "Sei sicuro di voler eliminare <strong>%s</strong>?"
at_the_moment_you_are_using_auto_generated_menu = "Al momento stai usando un menu generato automaticamente."
audio_post = "Articolo con audio"
audio_post_comment = "Crea un articolo del blog con un audio in evidenza"
author = "Autore"
author_description = "Solo un altro utente di HTMLy"
back_to = "Torna indietro"
backup = "Backup"
bing_webmaster_tools = "Bing Webmaster Tools"
blog_description = "In un paragrafo, parlaci un po' di più del tuo blog."
blog_theme = "Tema del Blog"
blog_title = "Titolo del Blog"
blog_title_placeholder = "Il mio blog con HTMLy"
blog_posts_displayed_as = "Articoli del blog visualizzati come"
breadcrumb_home_text = "Testo della home Breadcrumb"
by_using_this_importer_you_confirm_that_the_feed_is_yours_or_that_at_least_you_have_the_authority_to_publish_it = "Usando questo importatore sei d'accordo che il feed sia tuo o almeno tu ne abbia l'autorità per pubblicarlo."
css_class_optional = "Classe CSS (facoltativa)"
cache_expiration = "Scadenza della cache (in ore)"
cache_off = "Disattiva cache"
cache_timestamp = "Fuso orario della cache"
cancel = "Annulla"
cannot_read_feed_content = "Impossibile leggere il contenuto del feed"
captcha_error = "reCaptcha non corretto"
categories = "Categorie"
category = "Categoria"
check_update = "Controllo aggiornamenti"
clear_cache = "Svuota la cache"
comma_separated_values = "Valori separati da virgole"
comment_system = "Sistema dei commenti"
comments = "Commenti"
config = "Configurazione"
congrats_you_have_the_latest_version_of_htmly = "Congratulazioni! Hai l'ultima versione di HTMLy."
content = "Contenuto"
contents = "Contenuti"
copyright_line = "Riga del Copyright"
copyright_line_placeholder = "(c) Il tuo nome."
create_backup = "Crea backup"
created = "Creato"
custom = "Personalizzato"
custom_settings = "Impostazioni personalizzate"
dashboard = "Pannello utente"
date = "Data"
date_format = "Formato della data"
delete = "Elimina"
description = "Descrizione"
disable = "Disabilita"
disabled = "Disabilitato"
disqus_shortname = "Nomignolo Disqus"
disqus_shortname_placeholder = "htmly"
draft = "Bozza"
edit = "Modifica"
edit_category = "Modifica categoria"
edit_post = "Modifica"
edit_profile = "Modifica profilo"
enable = "Abilita"
enable_blog_url = "Abilita URL del blog"
enter_image_url = "Inserisci l'URL dell'immagine"
facebook_app_id = "ID di Facebook"
facebook_page = "Pagina Facebook"
featured_audio = "Audio in primo piano"
featured_image = "Immagine in primo piano"
featured_link = "Collegamento in primo piano"
featured_quote = "Citazione in primo piano"
featured_video = "Video in primo piano"
feed_url = "URL del Feed"
filename = "Nome del file"
follow = "Segui"
for_google_site_verification_meta = "For google-site-verification meta"
for_msvalidate_01_meta = "For msvalidate.01 meta"
front_page_displays = "Visualizza pagina principale"
full_post = "Articoli completi"
general = "Generali"
general_settings = "Impostazioni generali"
get_one_here = "Prendine una qui"
github_pre_release = "Pre-release di Github"
google_analytics = "Google Analytics"
google_analytics_legacy = "Google Analytics (legacy)"
google_search_console = "Google Search Console"
home = "Home"
if_left_empty_we_will_excerpt_it_from_the_content_below = "Se assente verrà estratto dai contenuti qui sotto"
if_the_url_is_left_empty_we_will_use_the_page_title = "Se l'url rimane in bianco verrà usato il titolo della pagina"
if_the_url_is_left_empty_we_will_use_the_post_title = "Se l'url rimane in bianco verrà usato il titolo dell'articolo"
image_post = "Articolo con immagine"
image_post_comment = "Crea un articolo del blog con un'immagine in evidenza"
import = "Importa"
import_feed = "Avvia importazione del Feed"
import_rss = "Importa RSS"
import_rss_feed_2.0 = "Importa Feed RSS 2.0"
insert_image = "Inserisci immagine"
invalid_error = "ERRORE: Nome utente o password non valido/a"
language = "Lingua di sistema"
link_name = "Nome del collegamento"
link_post = "Articolo con collegamento"
link_post_comment = "Crea un articolo del blog con un collegamento in evidenza"
login = "Connessione"
login_page = "Pagina di login"
logout = "Disconnessione"
menu = "Menu"
menus = "Editor del Menu"
meta_description = "Meta descrizione"
meta_description_character = "Carattere della Metadescrizione"
metatags = "Metatags"
metatags_settings = "Impostazioni dei Metatag"
mine = "I miei contenuti"
more = "Leggi di più"
my_draft = "Le mie bozze"
my_posts = "I miei articoli"
name = "Nome"
newer = "Più recenti"
next = "Successivo"
next_post = "Articolo successivo"
no_available_backup = "Nessun backup disponibile a quest'ora."
no_draft_found = "Nessuna bozza trovata"
no_posts_found = "Nessun articolo trovato"
no_related_post_found = "Nessun articolo correlato"
no_scheduled_posts_found = "Non è stato trovato alcun articolo pianificato!"
no_search_results = "Nessun risultato della ricerca"
nope = "Nessuno"
not = "No"
older = "Più vecchi"
only = "Solo"
operations = "Operazioni"
page = "Pagina"
page_generation_time = "Tempo di generazione della pagina"
pages = "Pagine"
pass_error = "È richiesto il campo Password"
password = "Password"
performance = "Prestazioni"
performance_settings = "Impostazioni delle Prestazioni"
permalink = "Permalink"
popular = "Popolare"
popular_posts = "Articoli popolari"
popular_posts_widget = "Widget articoli popolari"
popular_posts_widget_at_most = "Widget articoli popolari al massimo"
popular_tags = "Etichette popolari"
post_by_author = "Articoli di questo utente"
posted_in = "Pubblicato in"
posted_on = "Pubblicato il"
posts = "Articoli"
posts_by = "Articoli di"
posts_draft = "Elenco delle bozze"
posts_in_archive_page_at_most = "Gli articoli nella pagina archivio al massimo"
posts_in_category_page_at_most = "Gli articoli nella pagina delle categoria al massimo"
posts_in_front_page_show_at_most = "Articoli in prima pagina da visualizzare di più"
posts_in_profile_page_at_most = "Gli articoli nella pagina del profilo al massimo"
posts_in_search_result_at_most = "Gli articoli nei risultati della ricerca al massimo"
posts_in_tag_page_at_most = "Gli articoli nella pagina delle etichette al massimo"
posts_in_type_page_at_most = "Gli articoli della pagina dei tipi al massimo"
posts_index_settings = "Impostazioni indice degli articoli"
posts_list = "Elenco articoli"
posts_tagged = "Articoli etichettati"
posts_with_type = "Articoli con un tipo"
pre_release = "Pre-release"
prev = "Precedenti"
prev_post = "Articolo precedente"
preview = "Anteprima"
profile_for = "Profilo per"
proudly_powered_by = "Questo blog è basato su"
publish = "Pubblica"
publish_draft = "Pubblica la bozza"
published = "Pubblicato"
quote_post = "Articolo con una citazione"
quote_post_comment = "Crea un articolo del blog con una citazione in evidenza"
rss_character = "Carattere RSS"
rss_feeds_show_the_most_recent = "I feed RSS visualizzano i più recenti"
rss_settings = "Impostazioni RSS"
read_more_text = "Leggi più testo"
read_more_text_placeholder = "Leggi di più"
reading = "Lettura"
reading_settings = "Impostazioni di lettura"
recaptcha = "Protezione login"
recent_posts = "Articoli recenti"
recent_posts_widget_at_most = "Widget articoli recenti al massimo"
regular_post = "Articolo semplice"
regular_post_comment = "Crea un articolo semplice per il blog"
related_posts = "Articoli correlati"
related_widget_posts_at_most = "Widget articoli correlati al massimo"
revert_to_draft = "Trasforma in bozza"
save = "Salva"
save_config = "Salva configurazione"
save_edit = "Salva Modifica"
save_menu = "Salva menu"
save_as_draft = "Salva come bozza"
save_category = "Salva categoria"
scheduled = "Pianificazione"
scheduled_posts = "Articoli pianificati"
scheduled_tips = "Pubblicando un articolo con una data o un'ora futura, questo verrà inserito negli articoli pianificati"
search = "Cerca"
search_for = "Cerca per"
search_results_for = "Risultati della ricerca per"
search_results_not_found = "Non è stato trovato nessun risultato della ricerca!"
secret_key = "Chiave segreta"
settings = "Impostazioni"
sign_in_to_start_your_session = "Connettiti per avviare la sessione"
site_key = "Chiave del sito"
sitemap = "Mappa del sito"
slug = "Slug"
social_media = "Social Media"
static_page = "Pagina statica"
static_page_comment = "Creazione di una pagina statica"
static_pages = "Pagine statiche"
summary = "Riassunto"
summary_character = "Caratteri del riassunto"
tag = "Etichetta"
tagcloud_widget_at_most = "TagCloud al massimo"
tagline = "Slogan"
tagline_placeholder = "Piattaforma per blog in PHP senza database"
tagline_description = "In poche parole, spiega di cosa parla questo blog."
tags = "Etichette"
this_is_legacy_code_usually_new_created_analytics_using_gtag_js = "Questo è codice legacy. Di solito le nuove statistiche usano il file gtag.js"
this_page_doesnt_exist = "Questa pagina non esiste !"
time = "Ora"
timezone = "Fuso orario"
title = "Titolo"
to_use_disqus_or_facebook_comment_you_need_to_provide_disqus_shortname_or_facebook_app_id = "Per usare Disqus o i commenti di Facebook hai bisogno di fornire il nomignolo di Disqus o l'ID di Facebook."
token_error = "Il token CSRF non è corretto"
tools = "Strumenti"
twitter_account = "Profilo Twitter"
type_to_search = "Digita per cercare"
uncategorized = "Senza categoria"
uncategorized_comment = "Argomenti che non necessitano di una categoria, o che non si adattano a nessuna categoria esistente"
universal_analytics = "Universal Analytics (gtag.js)"
unknown_feed_format = "Formato del feed sconosciuto"
update = "Aggiorna"
update_available = "Aggiornamento disponibile"
update_draft = "Aggiorna bozza"
update_post = "Aggiorna articolo"
update_to = "Aggiorna a"
upload = "Carica"
user = "Utente"
user_error = "È richiesto il campo nome utente"
valid_values_range_from_0_to_1.0._see = "Il campo dei valori validi va da 0.0 a 1.0. Vedi"
video_post = "Articolo video"
video_post_comment = "Crea un articolo del blog con un video in evidenza"
view = "Visualizza"
view_post = "Visualizza"
views = "Visualizzazioni"
widget = "Widget"
widget_settings = "Impostazioni dei Widget"
would_you_like_to_try_our = "Volete provare la nostra "
yes_im_in = "Si, ci sono"
yes_not_recommended = "Si (non consigliato)"
you_dont_have_permission_to_access_this_page = "Non hai il permesso di accedere a questa pagina"
your_new_config_key = "La tua nuova chiave di configurazione"
your_new_value = "Il tuo nuovo valore"
your_backups = "I tuoi backup"
your_latest_blog_posts = "I tuoi ultimi articoli sul blog"
your_recent_posts = "I tuoi articoli più recenti"
by = "di"
hint_use_ctrlcmdf_to_search_for_your_config_key_or_value = "<u>consiglio:</u> Usa <code>Ctrl</code>/<code>CMD</code> + <code>F</code> per cercare la chiave o il valore di configurazione."
homepage = "homepage"
instead = "invece"
item_class = "Inserisci Classe CSS"
item_slug = "Inserisci URL del Link"
now = "ora"
of = "di"
optional = "facoltativo"
post_your_post_slug = "/articolo/il-tuo-slug-degli-articoli"
pro_tips_you_can_create_custom_config_key_and_print_out_your_config_key_value_anywhere_in_your_template = "<u>consiglio pratico:</u> Puoi creare la tua chiave di configurazione e usare il valore della chiave di configurazione dovunque nel tuo template."
read_more = "leggi di più"
widget_key_placeholder = "12345abcde"
year_month_your_post_slug = "/anno/mese/il-tuo-slug-degli-articoli"
your_key = "la.tua.chiave"
summary_behavior = "Modalità riassunto"
default = "Predefinito"
check_shortcode = "Controlla codice breve"
in_summary_mode_whether_check_the_shortcode_first_or_not_before_trim_the_content_to_x_char = "In modalità riassunto, si può controllare il codice breve o no prima di ridurre il contenuto a x caratteri"
manage_users = "Gestione utenti"
add_user = "Aggiungi utente"
username = "Nome utente"
role = "Ruolo"
change_password = "Modifica password"
config_mfa = "Configura AMF"
mfacode = "Codice AMF"
verify_code = "Verifica il codice AMF"
verify_password = "Verifica la password in uso"
manualsetupkey = "Si può anche aggiungere manualmente la chiave di setup"
mfa_error = "Il codice AMF non è corretto"
disablemfa = "Disabilita AMF"
enable_auto_save = "Abilita il salvataggio automatico nelle Bozze"
explain_autosave = "Una volta abilitato, i nuovi articoli o le nuove pagine verranno automaticamente salvate come bozze ogni 60 secondi dopo aver iniziato a scrivere."
login_protect_system = "Sistema di protezione della connessione"
cloudflare_info = "Rivedi la documentazione di Cloudflare Turnstile: "
mfa_config = "Autenticazione Multi Fattore (AMF)"
set_mfa_globally = "Imposta lo stato della AMF"
explain_mfa = "Una volta abilitata, la AMF è facoltativa per tutti gli utenti. Se disattivata, nessun utente può usarla ed il relativo campo nella pagina di connessione viene nascosto."
set_version_publicly = "Visibilità della versione"
explain_version = "Per impostazione predefinita la versione di HTMLy è visibile a tutti nel codice sorgente, alcuni amministratori preferirebbero nasconderla."
focus_mode = "Cambia visuale"
writing = "Scrittura"
writing_settings = "Impostazioni della scrittura"
security = "Sicurezza"
security_settings = "Impostazioni della sicurezza"
msg_error_field_req_username = "È richiesto il campo nome utente."
msg_error_field_req_password = "È richiesto il campo Password."
msg_error_field_req_title = "È richiesto il campo Titolo."
msg_error_field_req_content = "È richiesto il campo Contenuto."
msg_error_field_req_tag = "È richiesto il campo Etichetta."
msg_error_field_req_image = "È richiesto il campo Immagine."
msg_error_field_req_video = "È richiesto il campo Video."
msg_error_field_req_link = "È richiesto il campo Collegamento."
msg_error_field_req_quote = "È richiesto il campo Citazione."
msg_error_field_req_audio = "È richiesto il campo Audio."
msg_error_field_req_feedurl = "Devi specificare l'url del feed."
rss_feeds_description_select = "Descrizione del feed RSS"
rss_description_body = "Corpo dell'articolo"
rss_description_meta = "Meta descrizione dell'articolo"
admin_theme = "Tema Pannello Amministratore"
admin_theme_light = "Chiaro"
admin_theme_dark = "Scuro"
search_index = "Indice di ricerca"
fulltext_search = "Ricerca testo completo"
add_search_index = "Aggiungi gli articoli all'Indice di ricerca"
clear_search_index = "Cancella l'Indice di ricerca"
unindexed_posts = "Qui ci sono gli articoli che non sono stati indicizzati"
indexed_posts = "Gli articoli sono stati indicizzati"
custom_fields = "Campi personalizzati"
